{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.10.4", "date": "2024-12-18 14:50:52", "message": "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]", "author": "kmin"}, "舊分支": {"branch_name": "release_5.8.10.3", "date": "2024-10-01 10:53:47", "message": "[流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]", "author": "lorenchang"}, "比較時間": "2025-07-18 11:25:15", "新增commit數量": 122, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "a991fa1834b708f1cef7f8d752144d0fd80e583f", "commit_訊息": "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]", "提交日期": "2024-12-18 14:50:52", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "86a3aed4e3d7b611763bf39eebf514afb9450e6b", "commit_訊息": "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常", "提交日期": "2024-10-17 14:03:53", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "16d875c0f7f858ac714d5f740bec61c4e7c37233", "commit_訊息": "Revert \"[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常\"", "提交日期": "2024-12-16 10:43:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "67e767d5e5191b2d39673dcb8afe1cb49dddc185", "commit_訊息": "[內部]修正 SonarQube 顯示錯誤之程式：tIsE10Int.equals(\"\")(補)", "提交日期": "2024-12-09 16:18:37", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "748b6ccfc2ddcd6da5def8066a13c6b1984969af", "commit_訊息": "[Web]C01-20241205001 修正表單自適應寬度对subTab頁籤无效的问题", "提交日期": "2024-12-09 10:31:49", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7cdcc5f3a99ae905afff0341942ab9cea5f05f5a", "commit_訊息": "[TrmModule]新增差旅助理後端接口使用的dao與service[補]", "提交日期": "2024-12-06 16:33:39", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmToolDao.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmToolDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/TrmToolService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/impl/TrmToolServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "b9edcdeb6929a0c12c7a62d26a40e32d562f0cac", "commit_訊息": "更新58104 patch", "提交日期": "2024-12-06 15:17:48", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c2f52df1c4af2095c9a1ae3b45326f4d6a8bc02", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58", "提交日期": "2024-12-06 15:16:11", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c3d8cb4a5fd624df66b77e8aba6d186030cbffe1", "commit_訊息": "[文件總結助手]文件智能家更名為文件總結助手(補)", "提交日期": "2024-12-05 16:46:56", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/service/ChatFileCommonService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/service/impl/ChatFileCommonServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISOMod001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001Manager.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "2eeb4bc21850b4a97716c0cd1367a87c3b77a3d9", "commit_訊息": "[Web]C01-20241125013 修正复杂SQL多组“order by”报错，优化包子查询[補]", "提交日期": "2024-12-05 14:08:32", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a041222fa3aad047e026b844152b93aa20f69578", "commit_訊息": "[Web]C01-20241125017 優化Grid元件排序加入數值判斷", "提交日期": "2024-12-04 11:15:54", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b4048b3cf37618d755003aaded42411fc32b57d2", "commit_訊息": "[流程引擎]C01-20241129004 優化表單定義被修改后使用重發新流程，restful轉存表單的邏輯", "提交日期": "2024-12-02 11:27:21", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e5b90815bf040a3bd39b51aa887724c2d86cb7bc", "commit_訊息": "[Web]C01-20241125013 修正复杂SQL多组“order by”报错，优化包子查询", "提交日期": "2024-12-02 11:14:39", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8da7d06d1ceadff5765512be23576db702182ba", "commit_訊息": "[文件总结助手]修正绝对位置表单打开空白,报表管理新增页面少汇入两多语系", "提交日期": "2024-12-02 10:06:37", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "367d7b68bedbb29262abcf6d465e350701645c94", "commit_訊息": "[文件總結助手]文件智能家更名為文件總結助手(補)", "提交日期": "2024-11-28 14:37:03", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d207510cc0aaa02df5cd21575392196f154804e1", "commit_訊息": "[PRODT]S00-20231106007 調整Web流程管理工具中在選擇組織或人員畫面上增加搜尋機制[補]", "提交日期": "2024-11-28 09:55:10", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ce69ea792aa9653de3fa678bc30afd247069715f", "commit_訊息": "[資安]C01-20241125005 弱點掃描調整,將ua-parser.js進行壓縮+加密+混淆", "提交日期": "2024-11-27 17:38:14", "作者": "davidhr-2997", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ua-parser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7761e625a1f5d83ec258be9c34d9d9686469b751", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58", "提交日期": "2024-11-27 17:34:39", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "b589ac3d3a26efc8cdcdd58116bb88b88abfa13a", "commit_訊息": "[文件總結助手]文件智能家更名為文件總結助手(補)", "提交日期": "2024-11-27 17:01:56", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3bd94ced248d1b9ca2b59a547de77447479615fb", "commit_訊息": "[內部]增加DML備註：各 DB SQL 執行時產生當下日期的語法：MSSQL：GETDATE()、Oracle：SYSDATE、DM8：SYSDATE()", "提交日期": "2024-11-27 16:20:36", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1444ef718d0bbfed1ea4eee0c4b59b5008e24904", "commit_訊息": "[文件總結助手]新增功能：找經驗(補)", "提交日期": "2024-11-27 16:18:15", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a4dab80bd1533a2f4ab6c857aa74e48f54be66d", "commit_訊息": "[內部]修正 SonarQube 顯示錯誤之程式：tIsE10Int.equals(\"\")", "提交日期": "2024-11-27 15:13:53", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0a477d736dd2eeba6c3d31d351c208ade3391900", "commit_訊息": "[內部]V00-20241022002 修改系統設定onlineread.allow.convert.filetype的描述，如果pdfconverter.bcl.easypdfversion為digiwin就一併更改設定值(補)", "提交日期": "2024-11-27 14:34:42", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f1831a64083cf907cfa5d671051f606ead3d9474", "commit_訊息": "[內部]V00-20241022002 修改系統設定onlineread.allow.convert.filetype的描述，如果pdfconverter.bcl.easypdfversion為digiwin就一併更改設定值", "提交日期": "2024-11-27 11:27:25", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3e38a4e767936df9d217dd0d520b3b1820c40c03", "commit_訊息": "[ESS]C01-20241126006 更新ESSF51", "提交日期": "2024-11-26 17:35:52", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF51\\345\\212\\240\\347\\217\\255\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213(\\345\\244\\232\\346\\231\\202\\346\\256\\265\\345\\244\\232\\344\\272\\272).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF51\\345\\212\\240\\347\\217\\255\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213(\\345\\244\\232\\346\\231\\202\\346\\256\\265\\345\\244\\232\\344\\272\\272).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF51\\345\\212\\240\\347\\217\\255\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213(\\345\\244\\232\\346\\231\\202\\346\\256\\265\\345\\244\\232\\344\\272\\272).form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "292bcc151480bc7d667e05a10bf30aa8767d0583", "commit_訊息": "[TrmModule]調整差旅助理EAI服務接口[補]", "提交日期": "2024-11-26 15:47:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/impl/TrmBasicInfoServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AthenaMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "76e5e8bcd7cbce93d056482da374fb7cb54977ac", "commit_訊息": "[Web]C01-20241125002 修正當流程定義有設定主旨範本時重發新流程未帶預設值", "提交日期": "2024-11-26 11:26:20", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8bfce9eee3502602fe162bd7edf8d22f1bb1d5f1", "commit_訊息": "[內部]C01-20241122004 增加服務任務調用RESTful接口的請求回應Log(補)", "提交日期": "2024-11-26 11:30:54", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/RestfulToolAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f87360fb2a2ff462b34f18fa4a9254ffa61885da", "commit_訊息": "[Web]C01-20241125010 優化“無法順利地與資料庫建立連線”log資訊", "提交日期": "2024-11-26 14:58:06", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "24409b931f368565f4b47d75484e85cb6a4aee77", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58", "提交日期": "2024-11-26 14:26:21", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "52e31d4f14bd7b38a987d2125e26646d027ba5a9", "commit_訊息": "[BPM APP]C01-20241125001 修正當流程設定為允許修改主旨(不可空白)且設置主旨範本時，行動端未顯示預設主旨導致無法發起流程的問題", "提交日期": "2024-11-26 13:57:22", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9a3e55c2072a6c6d5d0d290c6495d8a702e97cfb", "commit_訊息": "C01-20241125005 弱點掃描調整,將ua-parser.js進行壓縮+加密+混淆", "提交日期": "2024-11-26 11:59:56", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ua-parser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63981690e4e1d2532dbbbf8cb63ab81ffdfd072c", "commit_訊息": "[Web]C01-20241125011 修正離職作業維護查詢條件日期結束時間改成當天的最後時間", "提交日期": "2024-11-26 10:57:58", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d04b986c9436d1a3f4acbc2881237a164cc40287", "commit_訊息": "[BPM APP]C01-20241125003 修正當Line整合授權帳號未與BPM帳號綁定時多語系顯示異常問題", "提交日期": "2024-11-25 17:00:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5ebeed4c3664dc5a7ce9da8fb835745c6e1a2745", "commit_訊息": "[TrmModule]合併InitSQL至5.8.10.4", "提交日期": "2024-11-25 13:53:52", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "df2ab9cf3110923e223f70cd282e6536b09b367d", "commit_訊息": "[文件總結助手]新增功能：找經驗", "提交日期": "2024-09-25 11:36:35", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileExecutionRecordDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileExperienceDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileExperienceRecordsDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileExperienceScheduleDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFilePresetProblemDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileToolDao.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExecutionRecordDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceRecordsDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceScheduleDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFilePresetProblemDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileToolDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileExecutionRecord.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileExperience.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileExperienceRecords.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileExperienceSchedule.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFilePresetProblem.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileQARecord.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/WordReportMappingDao.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/impl/WordReportMappingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/domain/WordReportMapping.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/PDFHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/iso/PDFHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/iso/PDFHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefinitionManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefinitionManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IPDFHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/PDFHandlerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 53}, {"commit_hash": "da8629877a249d7ceb90b920a582ec48065378c8", "commit_訊息": "[TrmModule]合併多語系至5.8.10.4", "提交日期": "2024-11-25 11:50:23", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e5d9fc9afbd9ae17eaa36d95d7470946f321144c", "commit_訊息": "[BPM APP]C01-20241111006 修正iOS的低版本有機率發生setValue設定選中值沒效果問題", "提交日期": "2024-11-25 11:32:41", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "51407dad085a2abfb1969e19d9f587dc55391ff0", "commit_訊息": "[TrmModule]合併DDL&DML至5.8.10.4", "提交日期": "2024-11-25 11:00:00", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.4_DDL_DM8_TRM.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/5.8.10.4_DDL_MSSQL_TRM.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/5.8.10.4_DDL_Oracle_TRM.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8_TRM.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL_TRM.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle_TRM.sql", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 9}, {"commit_hash": "56d57babe4543d24303c959e64fbc0b8497d6b00", "commit_訊息": "[TrmModule]新增差旅助手模組", "提交日期": "2024-09-24 16:30:21", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/.keep", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmCompanyMappingDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmConversionFunctionDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmInitiateProcessProfileDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmInitiateRecordDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmPropertiesDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmSourceFormDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmToolDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmCompanyMappingDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmConversionFunctionDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmInitiateProcessProfileDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmInitiateRecordDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmPropertiesDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmSourceFormDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmToolDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/.keep", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmCompanyMapping.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmConversionFunction.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmConversionFunctionData.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmInitiateProcessProfile.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmInitiateRecord.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmProperties.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmSourceForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dto/BaseDtoObject.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dto/TrmConversionFunctionDataDto.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dto/TrmConversionFunctionDto.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/.keep", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/TrmBasicInfoService.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/TrmToolService.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/impl/TrmBasicInfoServiceImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/impl/TrmToolServiceImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/EaiExecutionRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/EaiPackageRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/EaiPackageStdDataRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Athena.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AthenaMgr.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.4_DDL_DM8_TRM.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.4_DDL_MSSQL_TRM.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.4_DDL_Oracle_TRM.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8_TRM.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL_TRM.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle_TRM.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 48}, {"commit_hash": "61adc4375bdbcb21593b1b418396e653e0f1de8e", "commit_訊息": "[內部]C01-20241122004 增加服務任務調用RESTful接口的請求回應Log", "提交日期": "2024-11-25 10:11:00", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a68fcebca81afd53bf0d33cf81865b821cf63501", "commit_訊息": "[資安]Q00-20241113001 bootstrap-3.3.5.print.css更換bootstrap-c.c.e.print.css", "提交日期": "2024-11-21 16:19:11", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap-c.c.e-print.css", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "b9b960492ca0eb2e7746d9f08b9679f34c8aeb74", "commit_訊息": "[BPM APP] C01-20241114007 修正產品開窗預設值過濾組織id沒效果的問題[补]", "提交日期": "2024-11-21 15:22:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "2387ab0cc5b9573abd405a2b21dba4afa93469c4", "commit_訊息": "[Web]C01-20241119005 優化T100查看BPM簽核流程卡控未登入狀況的錯誤訊息[補]", "提交日期": "2024-11-21 14:26:10", "作者": "kmin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "14c3577aa2245d95995943be3d58f51184f992b2", "commit_訊息": "[Web]C01-20241119005 優化T100查看BPM簽核流程卡控未登入狀況的錯誤訊息", "提交日期": "2024-11-21 14:10:17", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f21e4e9cb340dd50526fb906793af23f24e61472", "commit_訊息": "[Web]C01-20241115004 E10同步表单元件不存在时新增log，印出错误的元件id", "提交日期": "2024-11-21 09:17:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f29e3713305568e8a963c5b92d4619c15c04774a", "commit_訊息": "[BPM APP]C01-20241119006 修正已轉派流程中有流程主旨為空時會顯示undefined訊息問題", "提交日期": "2024-11-20 15:44:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a5ef283663ad8108fa4a665a2677932c1a4fa93e", "commit_訊息": "[BPM APP] C01-20241119008 修正企业微信端人員不存在，执行企业微信禁用成員排程会报错的問題[补]", "提交日期": "2024-11-20 15:29:00", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "304b4c539d0c7524f6e2f749f6b384ed7829433c", "commit_訊息": "[BPM APP] C01-20241119008 修正企业微信端人員不存在，执行企业微信禁用成員排程会报错的問題", "提交日期": "2024-11-20 15:06:21", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ca90555100ea4168cc61727b6aa0bcae882bad25", "commit_訊息": "[ISO]C01-20241118008 修正ISO作廢單缺少記錄version造成ISOformSave儲存異常", "提交日期": "2024-11-20 13:51:17", "作者": "kmin", "檔案變更": [{"檔案路徑": "Release/copyfiles/@iso/default-form/ISOCancel001.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "10f2ae66c4a214444af7a1e16c7469b79cd5d737", "commit_訊息": "[BPM APP]C01-20241118003 修正多人簽核流程中若使用者沒有簽核記錄時會出現無查看權限問題", "提交日期": "2024-11-20 11:34:03", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0055e3a66b3185e842feca247c3c6aa430dbeef6", "commit_訊息": "[Web]C01-20241118004 修正終止流程formScript返回不允許終止，會跳下一張單據，不會停在原單據", "提交日期": "2024-11-19 10:18:49", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc625dfc9b2b504d92f103e70dbfc2257afad1c1", "commit_訊息": "[資安]Q00-20241113001 將bootstrap-3.3.4.min.js、bootstrap-3.3.5.min.js、bootstrap-c.c.d.min.js、bootstrap-c.c.e.min.js壓縮、混淆、加密", "提交日期": "2024-11-18 16:59:10", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-3.3.4.min.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-3.3.5.min.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-c.c.d.min.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-c.c.e.min.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "b2b826a26d2ffff88113052b59e93c1c3f5175af", "commit_訊息": "[資安]Q00-20241113001 移除版本號", "提交日期": "2024-11-18 16:48:52", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap-3.3.5-print.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "00b83d8eac39a1d582b8808d42f9c549b1224924", "commit_訊息": "[資安]Q00-20241113001 bootstrap-3.3.5.css、bootstrap-3.3.5.min.css更換,增加bootstrap-c.c.e.css、bootstrap-c.c.e.min.css", "提交日期": "2024-11-18 16:46:35", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/css/bootstrap-3.3.5.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/css/bootstrap-3.3.5.min.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/css/bootstrap-c.c.e.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/css/bootstrap-c.c.e.min.css", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "8d47b26b0d5bd21e880421f1809a2386897f6da9", "commit_訊息": "[資安]Q00-20241113001 bootstrap-3.3.5.css更換bootstrap-c.c.e.css", "提交日期": "2024-11-18 16:39:51", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/TimeExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AdjustActivityOrder.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AttachmentHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseMutilPrefechAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseOrganizationUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchProcessTerminating.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteEmployeeWorkReassigning.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessTerminating.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteReferProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkItemSending.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkRegetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeReferProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/OnlySignImageUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraditionInvokeProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ViewReassignHistory.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WebHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImageSub.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewAutoAgentActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewDecisionActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewParticipantActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/ProcessPreviewResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/SubProcessPreviewResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/CompleteFormDataSearching.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ChooseDefaultSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteLeftEmployeeWorkReassigning.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/SetProcessCondition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/WebViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 90}, {"commit_hash": "58f49b9636436a6dab5c5cd1bfd2bbd44c6d4b2d", "commit_訊息": "[資安]Q00-20241113001 bootstrap-3.3.5.css更換bootstrap-c.c.e.css", "提交日期": "2024-11-18 16:26:58", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/WrapProcessMonitorInfo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DesignerDownload/DesignerDownloadMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/InstallCertificate/InstallCertificate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocCategory/ManageDocCategoryMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/AccessRightChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/BatchUploadMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocClauseChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocFileUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocLevelChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocServerChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentForQuery.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/PDFUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/SingleDocCategoryChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/SnGenRuleChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/SetMultiLanguage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/CompleteThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/LogoImageUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManageResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ImageUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ShowSignImage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoComplete.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoTaskManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineRead/OnlineReadFileUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 83}, {"commit_hash": "a5fec8e679c9f1e58646e9cce119a2e8d5d39159", "commit_訊息": "[資安]Q00-20241113001 bootstrap-3.3.5.css更換bootstrap-c.c.e.css", "提交日期": "2024-11-18 16:12:42", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxCommonTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExtOrgTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxFormTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxOrgTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxProcessTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxService.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/AttachmentExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/ButtonExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/DateExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/DialogExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/DropdownExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileRWDExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormScriptExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/TextboxExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Index.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/RESTful/RESTfulIndex.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ErrorPage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ExtraLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/ChildGridChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/PerformWorkFromMail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ProductManifest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 34}, {"commit_hash": "2f86a43576f78f5a975d7fa03fa86421b4b3a0c1", "commit_訊息": "[資安]Q00-20241113001 bootstrap-3.3.5.min.css更換bootstrap-c.c.e.min.css", "提交日期": "2024-11-18 16:02:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamSetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/IWCIndicatorDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/Resignation.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CannotAccessWarnning.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/ViewSapFormField.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGFormManager.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGPropertise.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGSignerTemplate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/SSOCallBack.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/TFAModule/TFASetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/TFAModule/TFAUnauthlist.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AdministratorFunction/AdministratorFunction.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/IntelligentLearningManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribe.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineRead/WatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportTemplate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/OtherMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 47}, {"commit_hash": "85415fd450fcb95783b7d44342b097050777bddb", "commit_訊息": "[資安]Q00-20241113001 bootstrap-3.3.5.css、bootstrap-3.3.5.min.css更換,增加bootstrap-c.c.e.css、bootstrap-c.c.e.min.css", "提交日期": "2024-11-18 15:28:41", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-3.3.5.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-3.3.5.min.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-c.c.e.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-c.c.e.min.css", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "012d6a38b4994d80530496badf6fe9f3fa311fd5", "commit_訊息": "[BPM APP]C01-20241108004 修正企業微信操作切換企業後直接從推播進入時會導向追蹤清單或者待辦被轉派沒導向追蹤畫面問題[補]", "提交日期": "2024-11-18 13:39:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a967c937525299a26b6c6ba07116d033eee302b2", "commit_訊息": "[文件總結助手]文件智能家更名為文件總結助手", "提交日期": "2024-11-15 17:03:18", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "dd3d4d883bf53470dcc8e55cdea759f2402d52b7", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2024-11-15 15:03:13", "作者": "kmin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "a0da618b117bd4ea1e8b796c5780cf2dd624b0a5", "commit_訊息": "[Web]C01-20241114002 修正關卡設定信件有直接簽核網址且必填簽核意見未卡控為空異常問題", "提交日期": "2024-11-15 14:54:21", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BpmMailStraightSignOffPhrase.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0494d8bda83ca74441ddcbbea4a21395e979153", "commit_訊息": "[BPM APP]C01-20241115001 調整行動版表單必填欄位樣式顯示可以根據驗證設置的卡控規則顯示", "提交日期": "2024-11-15 15:00:55", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f712601bc1b0569f1159142f98760859294585d6", "commit_訊息": "[其它]C01-20240916003 修正可能收不到 GuardService 認證失敗 Mail 的異常(補)", "提交日期": "2024-11-15 14:00:54", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a422c4638b3c6103b7f2c30b3b7f02dce7e5922", "commit_訊息": "[BPM APP] C01-20241114007 修正產品開窗預設值過濾組織id沒效果的問題", "提交日期": "2024-11-15 13:30:15", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "01c124c545c19a349bcea3637aeeb3d756d65b2e", "commit_訊息": "[其它]C01-20240916003 修正可能收不到 GuardService 認證失敗 Mail 的異常", "提交日期": "2024-11-15 11:17:08", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "da2d6ebe59b7a92da9d0455a0b4cf11142e37221", "commit_訊息": "[BPM APP]C01-20241112003 修正釘釘查看附件時iOS手機無法上下滑動問題與Android手機查看圖片附件時支持放大縮小功能", "提交日期": "2024-11-14 16:45:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "03057711c23b80ba4063c8cfae91ad3107225b69", "commit_訊息": "[流程引擎]C01-20241112005 優化加簽關卡後派送失敗log訊息不明確", "提交日期": "2024-11-14 09:15:28", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b9db96010d5538e1cc9d46f3b4f1af264af512d5", "commit_訊息": "[PRODT]C01-20241111007 卡控核決關卡缺少ActivitySetDefinitions資料", "提交日期": "2024-11-14 09:07:28", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "049ff31d91f158428c9a14fab08bd6762e0ac6af", "commit_訊息": "[Web]A00-20241111001 修正指定關卡勾選「必須上傳新附件」，刪除附件被視為已上傳允許繼續派送的異常", "提交日期": "2024-11-12 13:44:34", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "11afc6d14be172a8d04d55e20dba85ac71746328", "commit_訊息": "[BPM APP]C01-20241108004 修正企業微信操作切換企業後直接從推播進入時會導向追蹤清單或者待辦被轉派沒導向追蹤畫面問題", "提交日期": "2024-11-11 16:08:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "fdb7d4f58125c86d40b570eb595e4459f863e463", "commit_訊息": "[其他] S00-20241025001 鼎新轉檔工具新增支援.msg檔", "提交日期": "2024-11-11 10:01:59", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f0d56c41b07b8f5903efe160a2189f15365993ae", "commit_訊息": "[SSO] 调整Athena SSO登录请求Header中去除appToken", "提交日期": "2024-11-08 10:05:59", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/AthenaSSO.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "da9c032e6637c1725bcfcb351cdd850fe4de3758", "commit_訊息": "[BPM APP]C01-20241106002 修正在手機端發起發生異常時顯示的畫面都是undefined 無法與伺服器溝通問題", "提交日期": "2024-11-07 15:30:10", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "72bf69c072545c5c9477f599eb951fb9cd9a62f3", "commit_訊息": "[BPM APP]C01-20241107001 優化企業微信接收訊息在處理Exception時的邏輯", "提交日期": "2024-11-07 14:10:06", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f20c46bcfd8f0380beceb9fcd3d56b2b726e99a5", "commit_訊息": "[BPM APP]C01-20241106004 修正使用自定義開窗當無資料時搜尋按鈕會消失問題", "提交日期": "2024-11-06 17:37:34", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a903f339992913b9ce763a04ef5b750f13d5197", "commit_訊息": "[Web表單設計師]C01-20241104007 修正pairId重覆多組導致元件消失", "提交日期": "2024-11-05 16:33:21", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "89156964814a3628dfe4f18af32ceb1442c205c0", "commit_訊息": "[BPM APP] C01-20241030008 优化LINE推播逻辑，避免JSON序列化重复执行导致CPU资源占用过高的问题", "提交日期": "2024-11-05 15:05:35", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b70c15a8b9faa5c5de9bbabd44b7f8bcdc7c139", "commit_訊息": "[Web] C01-20241104003 调整从pHttpServletRequest获取ip及port", "提交日期": "2024-11-04 14:00:15", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "72743eb6ec97b396a78c6d2052894bd8bbf01e29", "commit_訊息": "[Web]C01-20241030004 修正當關卡勾選\"需要使用者指定發送的組織單位\"時，繼續派送的部門選取畫面確認按鈕使用舊圖示及主部門未預設勾選的問題", "提交日期": "2024-11-01 17:17:42", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "755935c9effcc1005b8112629e9dfa7885e9d57e", "commit_訊息": "[Web]C01-20241030003 修正流程草稿因表單新增SerialNumber元件進版後造成無法開啟呈現空白", "提交日期": "2024-11-01 14:45:57", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05bdf9b150ca814e2be2ba11e5fdf6d608a64408", "commit_訊息": "[流程引擎]C01-20241030009 修正轉由他人處理之待辦通知信內容簽核歷程缺失問題", "提交日期": "2024-11-01 11:41:57", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0330e8e6ca85803fa7f900c7a45866e8820b65fe", "commit_訊息": "[內部]Q00-20241029001 優化寄信mail log的記錄判讀", "提交日期": "2024-10-29 16:12:55", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b7f9f896bb9c55981fd8a40c03a662ac264d9a4d", "commit_訊息": "[PRODT]C01-20241024005 修正Web流程管理工具中活動參與者組織相關選擇群組內的使用者時會顯示Error問題", "提交日期": "2024-10-29 15:49:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fb24a64ab970fd89b14f9ec55757b4b7a034b5cc", "commit_訊息": "[流程引擎]C01-20241025004 修正改派待辦通知信內容簽核歷程缺失問題", "提交日期": "2024-10-28 10:58:22", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e9fda495f672a2d50a242213f484da2138db22d", "commit_訊息": "[流程引擎]C01-20241022005 修正呼叫RESTful的發起流程絕對表單單身資料缺失問題", "提交日期": "2024-10-24 17:36:18", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4b06462711903c7d00d14445734de2f7761dd9e0", "commit_訊息": "[Web] Q00-20241023001 修正多选自定義開窗的選取清單有值後會跑版的問題[補]", "提交日期": "2024-10-24 09:33:52", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ce39fbd4c9e6d774fd4d71e9da73cf55fdf2a769", "commit_訊息": "[Web] Q00-20241023001 修正多选自定義開窗的選取清單有值後會跑版的問題", "提交日期": "2024-10-23 17:29:51", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c48b2802dc45250fe1ff234739230ac8905e8835", "commit_訊息": "[雙因素認證]C01-20241022003 修正使用LdapId登入的記住此裝置沒有作用的異常(信任端點資訊也沒有記錄)", "提交日期": "2024-10-23 16:05:04", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "12a4b74fd4128a1f71ca51e1ac35b53a963f8f9e", "commit_訊息": "[IMG]C01-20241021002 修正直接進入智能待辦應用時會無法顯示清單問題", "提交日期": "2024-10-22 13:52:27", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f62b76931b9e540eb9285175fbfd072419822378", "commit_訊息": "[Web]C01-20241021005 註解關閉表單Grid元件loadGridInfo的console.log訊息", "提交日期": "2024-10-22 10:59:16", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "eaba1dc6706c1d47d6996c8d5af3eb0784ed924f", "commit_訊息": "[Web]C01-20240806002 singleOpenWin支援回收AdditionalCriteria參數是否包子查詢加上額外條件作為判斷供客戶使用", "提交日期": "2024-10-21 11:56:01", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ff58cf107171de5c11287e7d8e290460c3a779c8", "commit_訊息": "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常", "提交日期": "2024-10-17 14:03:53", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "61ab127a8b05158a08f2524d0aa18507447c3259", "commit_訊息": "[WebService]C01-20241014003 修正並重新啟用 WebService 接口 addCustomParallelAndSerialActivity", "提交日期": "2024-10-17 16:04:53", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d04a9210b3111b6203d0dfd5e6b51349a06af147", "commit_訊息": "[E10]C01-20241012001 修正E10多人簽核關卡簽核歷程重覆問題", "提交日期": "2024-10-17 13:47:47", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be7229c6546f915925c741e42b69bdad86f1c245", "commit_訊息": "[Web]C01-20241015002 修正待辦列印表單處理者為代理簽核時不會顯示(代)", "提交日期": "2024-10-16 14:40:45", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1e9607cb06661e13254d5a4c81ca31ca97008a8b", "commit_訊息": "[Web]C01-20241014004 修正絕對表單tOtherHtmlObj元件沒有style.top屬性造成列印問題", "提交日期": "2024-10-16 09:44:11", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2ba12baf57e69525fe10f1bb9bcfab5e3fe33bec", "commit_訊息": "[流程設計師]C01-20241014007 修正服務任務呼叫 URL 結尾為\".asmx?wsdl\"的 WebService 出現讀取失敗的異常(TypeName=null 造成 NullPointerException)", "提交日期": "2024-10-15 11:30:42", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c0fc7578f69ee9a588d805080db571c241f4225e", "commit_訊息": "[Web]C01-20241009001 修正待辦連結用記住我時若密碼已事先變更造成畫面無窮驗證失敗直到帳號鎖定問題", "提交日期": "2024-10-14 15:55:44", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f9930660684922195366eb843a906b77d4302c3a", "commit_訊息": "[Web]C01-20241014002 修正系統設定 workitem.list.order 的描述，移除無法排序的 ProcessInstance.subject", "提交日期": "2024-10-14 15:10:53", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.4_DML_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.4_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "cafb0c1438ac08964d4a7dee5ef969f8bfa39ff2", "commit_訊息": "[Web表單設計師]C01-20241011002 修正表單資訊的欄位比例從表單外及表單內查看值會不一樣", "提交日期": "2024-10-14 14:54:07", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/FormDefinitionInfoVo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f727e4d56cbb383482711a761ad5351bb1a93351", "commit_訊息": "[Web]V00-20240918001 修正當活動關卡設定有勾選「允許輸入密碼」造成批次終止跟轉派異常[補]", "提交日期": "2024-10-14 08:36:35", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ece98baf4452a7a8d5e185e843c00d3e06750e3", "commit_訊息": "[E10]C01-20240930001 修正E10表單同步無法新增元件", "提交日期": "2024-10-09 15:54:01", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4dbf6949f5bcc6eb88160cb6f473e5b878ff1e06", "commit_訊息": "[Web]C01-20241007002 修正iReport報表定義描述的內容有換行時，刪除報表定義檔的畫面無法正常顯示的異常", "提交日期": "2024-10-09 14:15:38", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/report/ReportConfigViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "db598b2c42778a80ac784842af201452c9181fa0", "commit_訊息": "[流程引擎]A00-20241008001 修正通知信過濾非HTML標籤主旨值問題", "提交日期": "2024-10-09 10:49:16", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/HtmlUtils.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e111519848d9b749a574ba1c264bc9ab6583b332", "commit_訊息": "[Web]C01-20241004001 修正同時向前加簽並向後加簽後簡易流程呈現狀態異常問題", "提交日期": "2024-10-08 14:34:47", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "747af86576796187d607dd441790853c6b657a15", "commit_訊息": "[在線閱覽]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)[補]", "提交日期": "2024-10-08 09:52:30", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.mjs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.mjs.map", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.sandbox.mjs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.sandbox.mjs.map", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.worker.mjs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.worker.mjs.map", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "66e03211ded020658ab60bc26fb2684105c68bdc", "commit_訊息": "[Web]A00-20240920001 修正絕對表單Date、Time元件預設值(textValue)造成web表單設計師的顯示文字消失問題並消除代入預設值", "提交日期": "2024-10-04 10:46:04", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "130ae7a19e956922f03ca71ba95a06e63a2a2540", "commit_訊息": "[在線閱覽]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)", "提交日期": "2024-10-04 08:36:02", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.js", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.worker.js", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/BPMviewer.js", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/BPMviewer.mjs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/BPMviewer.mjs.map", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/cmaps/CNS2-V.bcmap", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/cmaps/ETenms-B5-H.bcmap", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/cmaps/GB-H.bcmap", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/debugger.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/debugger.js", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/debugger.mjs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/altText_add.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/altText_disclaimer.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/altText_done.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/altText_spinner.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/altText_warning.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/annotation-paperclip.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/annotation-pushpin.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/cursor-editorFreeHighlight.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/cursor-editorFreeText.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/cursor-editorInk.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/cursor-editorTextHighlight.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/editor-toolbar-delete.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-next-rtl.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-next.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-next.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-previous-rtl.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-previous.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-previous.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/grab.cur", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/grabbing.cur", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/gv-toolbarButton-download.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/loading-dark.svg", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/loading-small.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/messageBar_closingButton.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/messageBar_warning.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-documentProperties.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-documentProperties.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-firstPage.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-firstPage.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-handTool.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-handTool.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-lastPage.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-lastPage.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-rotateCcw.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-rotateCcw.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-rotateCw.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-rotateCw.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollHorizontal.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollHorizontal.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollPage.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollVertical.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollVertical.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollWrapped.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollWrapped.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-selectTool.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-selectTool.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadEven.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadEven.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadNone.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadNone.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadOdd.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadOdd.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/shadow.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/texture.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-bookmark.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-bookmark.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-currentOutlineItem.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-download.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-download.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-editorFreeText.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-editorHighlight.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-editorInk.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-editorStamp.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-menuArrow.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-menuArrows.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-openFile.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-openFile.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageDown-rtl.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageDown.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageDown.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageUp-rtl.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageUp.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageUp.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-presentationMode.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-presentationMode.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-print.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-print.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-search.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-search.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-secondaryToolbarToggle-rtl.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-secondaryToolbarToggle.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-secondaryToolbarToggle.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-sidebarToggle-rtl.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-sidebarToggle.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-sidebarToggle.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewAttachments.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewAttachments.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewLayers.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewOutline-rtl.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewOutline.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewOutline.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewThumbnail.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewThumbnail.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-zoomIn.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-zoomIn.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-zoomOut.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-zoomOut.svg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/treeitem-collapsed-rtl.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/treeitem-collapsed.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/treeitem-expanded.png", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/BPMlocale.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ach/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ach/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/af/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/af/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ak/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/an/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/an/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ar/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ar/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/as/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ast/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ast/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/az/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/az/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/be/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/be/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bg/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bg/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn-BD/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn-IN/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bo/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bo/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/br/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/br/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/brx/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/brx/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bs/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bs/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ca/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ca/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cak/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cak/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ckb/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ckb/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/crh/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cs/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cs/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/csb/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cy/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cy/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/da/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/da/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/de/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/de/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/dsb/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/dsb/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/el/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/el/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-CA/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-CA/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-GB/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-GB/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-US/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-US/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-ZA/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eo/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eo/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-AR/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-AR/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-CL/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-CL/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-ES/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-ES/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-MX/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-MX/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/et/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/et/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eu/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eu/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fa/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fa/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ff/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ff/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fi/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fi/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fr/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fr/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fur/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fy-NL/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fy-NL/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ga-IE/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ga-IE/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gd/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gd/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gl/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gl/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gn/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gn/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gu-IN/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gu-IN/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/he/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/he/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hi-IN/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hi-IN/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hr/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hr/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hsb/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hsb/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hto/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hu/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hu/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hy-AM/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hy-AM/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hye/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hye/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ia/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ia/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/id/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/id/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/is/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/is/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/it/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/it/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ja/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ja/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ka/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ka/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kab/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kab/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kk/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kk/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/km/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/km/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kn/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kn/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ko/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ko/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kok/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ks/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ku/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lg/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lij/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lij/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lo/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lo/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/locale.json", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/locale.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lt/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lt/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ltg/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ltg/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lv/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lv/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mai/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/meh/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/meh/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mk/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mk/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ml/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mn/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mr/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mr/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ms/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ms/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/my/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/my/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nb-NO/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nb-NO/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ne-NP/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ne-NP/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nl/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nl/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nn-NO/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nn-NO/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nso/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/oc/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/oc/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/or/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pa-IN/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pa-IN/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pl/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pl/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-BR/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-BR/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-PT/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-PT/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/rm/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/rm/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ro/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ro/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ru/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ru/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/rw/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sah/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sat/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sat/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sc/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sc/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/scn/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/scn/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sco/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sco/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/si/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/si/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sk/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sk/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/skr/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sl/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sl/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/son/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/son/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sq/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sq/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sr/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sr/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sv-SE/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sv-SE/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sw/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/szl/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/szl/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ta-LK/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ta/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ta/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/te/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/te/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tg/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tg/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/th/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/th/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tl/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tl/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tn/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tr/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tr/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/trs/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/trs/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tsz/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uk/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uk/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ur/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ur/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uz/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uz/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/vi/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/vi/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/wo/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/wo/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/xh/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/xh/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zam/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-CN/BPMviewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-CN/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-CN/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-TW/BPMviewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-TW/viewer.ftl", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-TW/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zu/viewer.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSans.pfb", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSansBold.pfb", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSansBoldItalic.pfb", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSansItalic.pfb", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.js", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.js.map", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.mjs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.mjs.map", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 414}, {"commit_hash": "c6f4e89f3f495dadce5dc7455b4ba142bf82a5e8", "commit_訊息": "[Web]A00-20201022001 修正流程主旨中最後是\\的符號，會導致無已轉派工作追蹤流程的清單頁會無法開啟", "提交日期": "2024-10-01 15:46:06", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "af331b5103fededdd1f2f8f20effbdf0e93b77ea", "commit_訊息": "[Web]A00-20240924001 附件名稱包含特殊字元，流程派送後顯示無限增長", "提交日期": "2024-10-01 14:42:19", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "33ec1023da8081f90a215f182cde843341a68f31", "commit_訊息": "[流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]", "提交日期": "2024-10-01 10:53:47", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb8159c1a4f395dcf4ba623388efce93ec00052e", "commit_訊息": "[Web]C01-20240927004 修正58103版本流程的簽核歷程和簡易流程圖畫面沒有顯示進行中的關卡的資訊", "提交日期": "2024-10-01 10:16:11", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ab0805cf1b372dcb4788b2565424efee6fd87d9a", "commit_訊息": "[EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息[補]", "提交日期": "2024-10-01 10:12:03", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "769d8c7f472ee0fa295b49ee93c9b8bddd63ec08", "commit_訊息": "[ESS]C01-20240926002 修正ESS流程若關卡符合自動簽核跳過關卡時，且系統參數有開啟非同步簽核時，ESS流程會無法繼續自動往下派送", "提交日期": "2024-09-30 15:36:44", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c6b3d02dd6d8940a9daae074dbf687ed493f8fd0", "commit_訊息": "[ESS]C01-20240924001 修正58102版本的ESS流程在撤銷、終止流程時有可能會無法撤銷、終止的異常", "提交日期": "2022-07-14 16:33:03", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d91f3329e507ca76080d7009b9446bd47d26c6b3", "commit_訊息": "[文件智能家] chatfile设定档资料新增chatfile接口授权令牌[補修正]", "提交日期": "2024-09-27 12:02:25", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b7144b5a645d89a71ffaee1bd8da26b025ea3198", "commit_訊息": "[雙因素認證]C01-*********** 修正使用LdapId登入不會進入雙因素認證的異常", "提交日期": "2024-09-24 10:23:34", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "fd796a968e36872dbb475e6c298b6a23bf409439", "commit_訊息": "[MPT]C01-20240916005 調整MPT公告申請單中公告內文是從Word地方複製貼上時會多了空白的問題", "提交日期": "2024-09-26 14:37:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/copyfiles/@mpt/default-form/MptAncApply.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa159656362931e8cc5f6070044a14a5c9466865", "commit_訊息": "[ISO]修正歸檔浮水印新增的字型設定產生的設定值與BCL8不相容造成中文字變方框", "提交日期": "2024-09-25 17:28:45", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}