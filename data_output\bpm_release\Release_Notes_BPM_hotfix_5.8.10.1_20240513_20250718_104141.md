# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.10.1_20240513
- **舊版本**: release_5.8.10.1
- **生成時間**: 2025-07-18 10:41:41
- **新增 Commit 數量**: 61

## 變更摘要

### 刘旭 (10 commits)

- **2024-05-11 09:50:36**: [Web] C01-20240509007 "模擬使用者"可以授權給一般使用者，會讓一般使用者可以模擬最高權限的administrator问题修正
  - 變更檔案: 1 個
- **2024-04-29 17:39:16**: [Web] Q00-20240429004 報表設計器儲存報 SqlConditionList is too long问题修正
  - 變更檔案: 1 個
- **2024-04-28 14:22:18**: [Web] Q00-20240428001 開窗資料條件財產名稱輸入[PL3/雙驅動改造]，資料帶回gird，儲存草稿/儲存表單後gird資料顯示異常问题修正
  - 變更檔案: 1 個
- **2024-04-23 09:14:36**: [Web] Q00-20240423001 客户5521版到5894 將流程從XPDL轉BPMN會失敗问题修正
  - 變更檔案: 1 個
- **2024-04-18 14:31:05**: [Web] Q00-20240417002 数据库中forminstance表中的fieldvalues字段行间距很大问题修正[补]
  - 變更檔案: 2 個
- **2024-04-17 10:23:42**: [Web] Q00-20240417002 数据库中forminstance表中的fieldvalues字段行间距很大问题修正
  - 變更檔案: 1 個
- **2024-04-11 11:27:19**: [Web] Q00-20240403002 Grid單身欄位加總異常问题修正[补]
  - 變更檔案: 1 個
- **2024-04-10 18:21:31**: [Web] Q00-20240410002 從5894版到58101後，Grid 欄位框線不會對齊,將調整寬度的script註解掉，Grid的欄位框線就可正常對齊问题修正
  - 變更檔案: 1 個
- **2024-04-03 16:22:52**: [Web] Q00-20240403002 Grid單身欄位加總異常问题修正
  - 變更檔案: 1 個
- **2024-04-03 14:01:05**: [Web] V00-20240402002 流程发起时选择流程重要性为：紧急，但是在待办事项列表中没有出现红色标记列问题修正
  - 變更檔案: 1 個

### 周权 (17 commits)

- **2024-05-10 17:31:42**: [Web]C01-20240509004 修正grid多栏位格线对不齐的问题
  - 變更檔案: 1 個
- **2024-05-08 11:43:06**: [流程引擎]C01-20240502001 修正在核決權限關卡加簽，預解析会重复出现核決權限表名稱的问题
  - 變更檔案: 1 個
- **2024-05-08 10:43:00**: [Web]Q00-20240508001 修正系統設定發起人看不到追蹤流程之設定
  - 變更檔案: 1 個
- **2024-04-26 10:35:22**: [Web]Q00-20240426003 修正栏位过多，设置栏位宽度没效果的问题
  - 變更檔案: 1 個
- **2024-04-25 15:32:11**: [Web]Q00-20240425002 修正开启绝对位置表单偶发报错TypeError: Cannot read properties of undefined (reading 'ElementGroup')的问题
  - 變更檔案: 1 個
- **2024-04-19 15:40:55**: [Web]Q00-20240416001 修正含有日期元件運算异常问题[補修正]
  - 變更檔案: 1 個
- **2024-04-19 13:56:50**: [Web]Q00-20240419003 修正追踪流程作业清单加载卡慢的问题
  - 變更檔案: 1 個
- **2024-04-18 14:49:17**: [流程引擎]Q00-20240418003 修正多人關卡設定工作完工比率時，流程實例開啟報OOPS的问题
  - 變更檔案: 1 個
- **2024-04-18 10:34:02**: [Web]Q00-20240418001 修正资料选取器回传栏位Disable状态下，双击清空栏位仍生效的问题
  - 變更檔案: 1 個
- **2024-04-17 13:56:05**: [Web]Q00-20240417004 修正radio元件在Disable的狀態下 在「重發新流程」時不會將該元件的選項清空
  - 變更檔案: 1 個
- **2024-04-16 10:59:44**: [Web]Q00-20240416001 修正含有日期元件運算异常问题
  - 變更檔案: 2 個
- **2024-04-12 10:05:56**: [Web]Q00-20240412001 修正grid 設定table 模式，有很多個欄位會擠一起的情況
  - 變更檔案: 1 個
- **2024-04-09 16:17:15**: [Web]Q00-20240409005 修正attachment物件OID为空时，会抛错[此URL没有下载文件的权限]的问题
  - 變更檔案: 1 個
- **2024-04-02 16:59:55**: [Web]Q00-20240402001 修正[報表維護作業]產出的報表畫面欄位异常
  - 變更檔案: 1 個
- **2024-04-02 16:00:27**: [Web]V00-20240402001 修正待办事项中【由我处理】按钮点击没有反应
  - 變更檔案: 1 個
- **2024-04-01 11:41:17**: [Web]Q00-20240401001 修正XPDL简易流程图显示主旨异常
  - 變更檔案: 1 個
- **2024-03-27 17:27:14**: [Web]Q00-20240327003 BPM首頁的工作事項主旨內容增加顯示轉派的簽核意見
  - 變更檔案: 1 個

### 林致帆 (12 commits)

- **2024-05-09 17:21:23**: [TIPTOP]C01-20240429008 修正回寫失敗訊息無法顯示在畫面上
  - 變更檔案: 1 個
- **2024-05-09 10:35:56**: [流程設計師]C01-20240416006 修正流程走到核決關卡後點擊待辦清單的流程會無法正常打開
  - 變更檔案: 1 個
- **2024-04-26 08:56:49**: [PLM]Q00-20240426001 調整PLM歷程接口返回內容
  - 變更檔案: 3 個
- **2024-04-26 08:21:34**: [Web]Q00-20240425005 修正加簽關卡選取經常對像在吳資料的狀況下會顯示錯誤頁面
  - 變更檔案: 1 個
- **2024-04-25 15:45:57**: [流程引擎]Q00-20240425003 修正核決關卡設置前置關係人造成前置關係人關卡無法繼續簽核
  - 變更檔案: 1 個
- **2024-04-25 09:09:30**: [TIPTOP]Q00-20240425001 調整TIPTOP接口呼叫封存的PORT號改成取流程主機設定
  - 變更檔案: 1 個
- **2024-04-24 11:15:43**: [TIPTOP]Q00-20240424001 修正流程封存邏輯影響到TIPTOP流程結案異常
  - 變更檔案: 1 個
- **2024-04-16 13:47:59**: [流程引擎]Q00-20240416002 修正流程在併簽關卡進行撤銷or終止，若有呼叫流程事件會造成資料庫Lock
  - 變更檔案: 1 個
- **2024-04-12 16:15:04**: [Web]Q00-20240412003 修正主旨顯示為編碼後的內容
  - 變更檔案: 11 個
- **2024-04-10 08:56:42**: [雙因素模組]Q00-20240409006 修正未啟用兩步驟認證清單會顯示已綁定的用戶 [補修正]
  - 變更檔案: 2 個
- **2024-04-09 17:15:22**: [雙因素模組]Q00-20240409006 修正未啟用兩步驟認證清單會顯示已綁定的用戶
  - 變更檔案: 1 個
- **2024-04-09 15:32:44**: [雙因素模組]Q00-20240409003 信任端點裝置時間修正為24小時制
  - 變更檔案: 1 個

### yamiyeh10 (5 commits)

- **2024-05-09 11:29:33**: [內部]Q00-20240509001 調整重新向資料庫取SystemVariable內容發生錯誤時的log訊息
  - 變更檔案: 1 個
- **2024-05-03 10:00:57**: [SYSDT]C01-20240502002 修正設計師使用權限管理中若人員的最後工作日設為未來日期時會無法顯示使用者問題
  - 變更檔案: 1 個
- **2024-04-24 14:08:07**: [PRODT]Q00-20240424002 修正Web流程設計師中發起權限設定屬性的職務資料在編輯狀態後儲存會遺失問題
  - 變更檔案: 1 個
- **2024-04-12 12:07:36**: [流程設計師]Q00-20240412002 修正流程存在核決關卡時執行XDPL轉BPMN因活動集合定義遺失無法替換id而導致無法轉換問題
  - 變更檔案: 1 個
- **2024-04-09 11:42:27**: [ORGDT]V00-20240321002 修正Web組織管理工具中透過放大鏡搜尋部門後查看隸屬單位顯示錯誤問題
  - 變更檔案: 1 個

### 邱郁晏 (15 commits)

- **2024-05-08 17:29:08**: [流程引擎] C01-20240508002 修正加簽關卡後，沒有釋放連線數
  - 變更檔案: 1 個
- **2024-05-08 10:24:27**: [Web] Q00-20240507002 調整流程表單符合多個條件式時，錯誤提示不明確問題，新增Log(補)
  - 變更檔案: 1 個
- **2024-05-07 18:01:08**: [Web] Q00-20240507002 調整流程表單符合多個條件式時，錯誤提示不明確問題，新增Log
  - 變更檔案: 1 個
- **2024-05-07 14:57:51**: [流程引擎] Q00-20240507001 新增單筆封存流程還原接口的防呆，若無流程封存模組則不可調用
  - 變更檔案: 2 個
- **2024-04-26 11:03:40**: [Web] Q00-20240426002 修正主管首頁待辦處理量OracleDB異常
  - 變更檔案: 1 個
- **2024-04-25 17:48:22**: [Web] Q00-20240425004 修正絕對位置表單，調整表單大小導致ScrollBar異常增加問題
  - 變更檔案: 1 個
- **2024-04-23 15:15:41**: [Web]Q00-20240423004 在觸發排程Trigger加入睡眠機制，以避免排程執行過快導致重複觸發狀況
  - 變更檔案: 1 個
- **2024-04-22 15:37:19**: [Web]Q00-20240422002 調整主管頁面在途中流程處理時效計算SQL寫法
  - 變更檔案: 1 個
- **2024-04-19 11:28:32**: [Web] Q00-20240419001 修正檢視參與者發送按鈕消失異常
  - 變更檔案: 1 個
- **2024-04-09 17:45:34**: [Secudocx] V00-20240409001 調整以柔整合寫法優化
  - 變更檔案: 1 個
- **2024-04-09 16:27:19**: [Web] Q00-20240409004 修正流程草稿主旨上有反斜線\導致畫面空白問題
  - 變更檔案: 1 個
- **2024-04-08 18:08:07**: [Web] Q00-20240408001 調整刪除流程的邏輯，改為不檢核直接刪除流程
  - 變更檔案: 1 個
- **2024-03-29 15:28:10**: [組織同步] Q00-20240329002 修正組織同步User帳號啟用邏輯異常，導致部分使用者同步後帳號變為未啟用
  - 變更檔案: 1 個
- **2024-03-28 14:38:28**: [Web] Q00-20240328001 修正showDialog寫法異常
  - 變更檔案: 1 個
- **2024-03-27 15:12:54**: [組織同步] Q00-20240327002 調整HR組織同步，塞入中介表資料若異常，不拋出錯誤
  - 變更檔案: 1 個

### pinchi_lin (2 commits)

- **2024-03-27 10:49:32**: [PRODT]Q00-20240327001 修正Web流程管理工具中匯入流程無法覆蓋流程進版的問題
  - 變更檔案: 1 個
- **2024-03-27 10:06:50**: [ORGDT]Q00-20240322002 修正Web組織管理工具中調離所有部門功能異常問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [Web] C01-20240509007 "模擬使用者"可以授權給一般使用者，會讓一般使用者可以模擬最高權限的administrator问题修正
- **Commit ID**: `34789622b4e255a206f019c5ee0ff0417b1b93f6`
- **作者**: 刘旭
- **日期**: 2024-05-11 09:50:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 2. [Web]C01-20240509004 修正grid多栏位格线对不齐的问题
- **Commit ID**: `e4865ca622a6de148d069b24720bb26ce8406022`
- **作者**: 周权
- **日期**: 2024-05-10 17:31:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 3. [TIPTOP]C01-20240429008 修正回寫失敗訊息無法顯示在畫面上
- **Commit ID**: `68dd10ff94dc4a002df5ec51b91692be43d7fd49`
- **作者**: 林致帆
- **日期**: 2024-05-09 17:21:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`

### 4. [內部]Q00-20240509001 調整重新向資料庫取SystemVariable內容發生錯誤時的log訊息
- **Commit ID**: `5b78af1869bf39c5746d1e3f8eb4943a275ee663`
- **作者**: yamiyeh10
- **日期**: 2024-05-09 11:29:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`

### 5. [流程設計師]C01-20240416006 修正流程走到核決關卡後點擊待辦清單的流程會無法正常打開
- **Commit ID**: `5bff6feecc8ca958a94981993b9ead20414a34b1`
- **作者**: 林致帆
- **日期**: 2024-05-09 10:35:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java`

### 6. [流程引擎] C01-20240508002 修正加簽關卡後，沒有釋放連線數
- **Commit ID**: `f1584ebf9c1a304b7ce8fd6febf920a19ff7f6be`
- **作者**: 邱郁晏
- **日期**: 2024-05-08 17:29:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`

### 7. [流程引擎]C01-20240502001 修正在核決權限關卡加簽，預解析会重复出现核決權限表名稱的问题
- **Commit ID**: `a8b61cff98f101d77e57fc0d5c07ed98cea8d5f4`
- **作者**: 周权
- **日期**: 2024-05-08 11:43:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 8. [Web]Q00-20240508001 修正系統設定發起人看不到追蹤流程之設定
- **Commit ID**: `a139add485528e9d8d97000b9c69787d69600eef`
- **作者**: 周权
- **日期**: 2024-05-08 10:43:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 9. [Web] Q00-20240507002 調整流程表單符合多個條件式時，錯誤提示不明確問題，新增Log(補)
- **Commit ID**: `aef9185ac1b453bd63505c47cdf0cab36c2dfb6b`
- **作者**: 邱郁晏
- **日期**: 2024-05-08 10:24:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 10. [Web] Q00-20240507002 調整流程表單符合多個條件式時，錯誤提示不明確問題，新增Log
- **Commit ID**: `2d5fb528708f3413b9074115d85e84e5b8eeb8f0`
- **作者**: 邱郁晏
- **日期**: 2024-05-07 18:01:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 11. [流程引擎] Q00-20240507001 新增單筆封存流程還原接口的防呆，若無流程封存模組則不可調用
- **Commit ID**: `0a75ea6a02ae80815da8d063dcf1907f7682c326`
- **作者**: 邱郁晏
- **日期**: 2024-05-07 14:57:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/NaNaXWebHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 12. [SYSDT]C01-20240502002 修正設計師使用權限管理中若人員的最後工作日設為未來日期時會無法顯示使用者問題
- **Commit ID**: `35224b1e848d540e7b9bbb1eab7f8c4829e010e5`
- **作者**: yamiyeh10
- **日期**: 2024-05-03 10:00:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java`

### 13. [Web] Q00-20240429004 報表設計器儲存報 SqlConditionList is too long问题修正
- **Commit ID**: `464f17cb97a0b1d744a11244373648ff79fa9bc8`
- **作者**: 刘旭
- **日期**: 2024-04-29 17:39:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java`

### 14. [Web] Q00-20240428001 開窗資料條件財產名稱輸入[PL3/雙驅動改造]，資料帶回gird，儲存草稿/儲存表單後gird資料顯示異常问题修正
- **Commit ID**: `b98caad93e1307780bd90dd2754864907c3437bf`
- **作者**: 刘旭
- **日期**: 2024-04-28 14:22:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 15. [Web] Q00-20240426002 修正主管首頁待辦處理量OracleDB異常
- **Commit ID**: `66c9931ef2f597429450d3315fa73c52cb7b0a66`
- **作者**: 邱郁晏
- **日期**: 2024-04-26 11:03:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 16. [Web]Q00-20240426003 修正栏位过多，设置栏位宽度没效果的问题
- **Commit ID**: `8dc181c4bb7261f170d0915fd08aeb19213dc241`
- **作者**: 周权
- **日期**: 2024-04-26 10:35:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 17. [PLM]Q00-20240426001 調整PLM歷程接口返回內容
- **Commit ID**: `1267f2cd11e304730b0e1084ad1dd110785aac30`
- **作者**: 林致帆
- **日期**: 2024-04-26 08:56:49
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WebServiceUtil.java`

### 18. [Web]Q00-20240425005 修正加簽關卡選取經常對像在吳資料的狀況下會顯示錯誤頁面
- **Commit ID**: `bf6f6d751ccc668931f89261c37f7d711a5dce8f`
- **作者**: 林致帆
- **日期**: 2024-04-26 08:21:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`

### 19. [Web] Q00-20240425004 修正絕對位置表單，調整表單大小導致ScrollBar異常增加問題
- **Commit ID**: `8e9b1f8add529c913af591d39c92a8f1452a3adc`
- **作者**: 邱郁晏
- **日期**: 2024-04-25 17:48:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`

### 20. [流程引擎]Q00-20240425003 修正核決關卡設置前置關係人造成前置關係人關卡無法繼續簽核
- **Commit ID**: `6cfbb41b769e93d278b3eb798d7f6cfd4f890d7c`
- **作者**: 林致帆
- **日期**: 2024-04-25 15:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 21. [Web]Q00-20240425002 修正开启绝对位置表单偶发报错TypeError: Cannot read properties of undefined (reading 'ElementGroup')的问题
- **Commit ID**: `00fd545e93b34de3e0198f1a92de0e29cb404433`
- **作者**: 周权
- **日期**: 2024-04-25 15:32:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`

### 22. [TIPTOP]Q00-20240425001 調整TIPTOP接口呼叫封存的PORT號改成取流程主機設定
- **Commit ID**: `6501588f92bb6254e2f54693430a840bb10b2c9c`
- **作者**: 林致帆
- **日期**: 2024-04-25 09:09:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java`

### 23. [PRODT]Q00-20240424002 修正Web流程設計師中發起權限設定屬性的職務資料在編輯狀態後儲存會遺失問題
- **Commit ID**: `b7b6fed7c91ca36cb5d37fc0797d84d1c2060d09`
- **作者**: yamiyeh10
- **日期**: 2024-04-24 14:08:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 24. [TIPTOP]Q00-20240424001 修正流程封存邏輯影響到TIPTOP流程結案異常
- **Commit ID**: `bd45f9a813015af2eb167d335948bd3bd4379bda`
- **作者**: 林致帆
- **日期**: 2024-04-24 11:15:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java`

### 25. [Web]Q00-20240423004 在觸發排程Trigger加入睡眠機制，以避免排程執行過快導致重複觸發狀況
- **Commit ID**: `0adbd234394b5b8483542a8467f602ffe6b72eae`
- **作者**: 邱郁晏
- **日期**: 2024-04-23 15:15:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/schedule/SystematicJob.java`

### 26. [Web] Q00-20240423001 客户5521版到5894 將流程從XPDL轉BPMN會失敗问题修正
- **Commit ID**: `c73e428c7d86f174e3d07698c0850608c2b5d69e`
- **作者**: 刘旭
- **日期**: 2024-04-23 09:14:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java`

### 27. [Web]Q00-20240422002 調整主管頁面在途中流程處理時效計算SQL寫法
- **Commit ID**: `4089f6f2c2986f242b9abc986972ea135598dd9b`
- **作者**: 邱郁晏
- **日期**: 2024-04-22 15:37:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 28. [Web]Q00-20240416001 修正含有日期元件運算异常问题[補修正]
- **Commit ID**: `522250f1b9cecaeed5c400524355b1a1c5ad0c1c`
- **作者**: 周权
- **日期**: 2024-04-19 15:40:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 29. [Web]Q00-20240419003 修正追踪流程作业清单加载卡慢的问题
- **Commit ID**: `39ec60d27d77f4c776278ecb0121042fa5019757`
- **作者**: 周权
- **日期**: 2024-04-19 13:56:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 30. [Web] Q00-20240419001 修正檢視參與者發送按鈕消失異常
- **Commit ID**: `9730501f7c562bbb3e9da5f853b62c8e66f88098`
- **作者**: 邱郁晏
- **日期**: 2024-04-19 11:28:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 31. [流程引擎]Q00-20240418003 修正多人關卡設定工作完工比率時，流程實例開啟報OOPS的问题
- **Commit ID**: `19a01ed5401cb8dbcecd5b02603f70a55c4d3538`
- **作者**: 周权
- **日期**: 2024-04-18 14:49:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 32. [Web] Q00-20240417002 数据库中forminstance表中的fieldvalues字段行间距很大问题修正[补]
- **Commit ID**: `0869c66fdcc1fc944b4ee63c5192a469e8a18f52`
- **作者**: 刘旭
- **日期**: 2024-04-18 14:31:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 33. [Web]Q00-20240418001 修正资料选取器回传栏位Disable状态下，双击清空栏位仍生效的问题
- **Commit ID**: `9adf43ce5a90d40314532c8ad19617dc1614b810`
- **作者**: 周权
- **日期**: 2024-04-18 10:34:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 34. [Web]Q00-20240417004 修正radio元件在Disable的狀態下 在「重發新流程」時不會將該元件的選項清空
- **Commit ID**: `8f62a5e0d772f17135ab40c530088b1adc879b03`
- **作者**: 周权
- **日期**: 2024-04-17 13:56:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 35. [Web] Q00-20240417002 数据库中forminstance表中的fieldvalues字段行间距很大问题修正
- **Commit ID**: `a61e8cb83b968de70e8c5dd11d60936fafc52810`
- **作者**: 刘旭
- **日期**: 2024-04-17 10:23:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 36. [流程引擎]Q00-20240416002 修正流程在併簽關卡進行撤銷or終止，若有呼叫流程事件會造成資料庫Lock
- **Commit ID**: `cbcfaf53654f4385f51031e9b3f4d68ce17c5380`
- **作者**: 林致帆
- **日期**: 2024-04-16 13:47:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 37. [Web]Q00-20240416001 修正含有日期元件運算异常问题
- **Commit ID**: `b7d2db1093a6d6a4ce1ff224e5efe15362e7e7bb`
- **作者**: 周权
- **日期**: 2024-04-16 10:59:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 38. [Web]Q00-20240412003 修正主旨顯示為編碼後的內容
- **Commit ID**: `377ce412ff31adeaace498d49adf6e22102c3110`
- **作者**: 林致帆
- **日期**: 2024-04-12 16:15:04
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/StringUtil.js`

### 39. [流程設計師]Q00-20240412002 修正流程存在核決關卡時執行XDPL轉BPMN因活動集合定義遺失無法替換id而導致無法轉換問題
- **Commit ID**: `31ed4a07461d75b51d4735a7730ba44e56a8909a`
- **作者**: yamiyeh10
- **日期**: 2024-04-12 12:07:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java`

### 40. [Web]Q00-20240412001 修正grid 設定table 模式，有很多個欄位會擠一起的情況
- **Commit ID**: `ba9f9167585267f030a52267ab6e27caeb8180ef`
- **作者**: 周权
- **日期**: 2024-04-12 10:05:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 41. [Web] Q00-20240403002 Grid單身欄位加總異常问题修正[补]
- **Commit ID**: `812149872b06c376a28c3ec7640aa670c819d623`
- **作者**: 刘旭
- **日期**: 2024-04-11 11:27:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 42. [Web] Q00-20240410002 從5894版到58101後，Grid 欄位框線不會對齊,將調整寬度的script註解掉，Grid的欄位框線就可正常對齊问题修正
- **Commit ID**: `c8fe7c9a7a0e024fa6eedeb68cbc5ca894b742ec`
- **作者**: 刘旭
- **日期**: 2024-04-10 18:21:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 43. [雙因素模組]Q00-20240409006 修正未啟用兩步驟認證清單會顯示已綁定的用戶 [補修正]
- **Commit ID**: `aaa048e0d2f03f4ef0cff89b0051daae97f655ed`
- **作者**: 林致帆
- **日期**: 2024-04-10 08:56:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 44. [Secudocx] V00-20240409001 調整以柔整合寫法優化
- **Commit ID**: `d7ba0924b2c690cf88ff30680b8373fd28d1b41d`
- **作者**: 邱郁晏
- **日期**: 2024-04-09 17:45:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 45. [雙因素模組]Q00-20240409006 修正未啟用兩步驟認證清單會顯示已綁定的用戶
- **Commit ID**: `9181a59b049fb976f2674f8e9da5dd202f42f9c9`
- **作者**: 林致帆
- **日期**: 2024-04-09 17:15:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`

### 46. [Web] Q00-20240409004 修正流程草稿主旨上有反斜線\導致畫面空白問題
- **Commit ID**: `f2f1aabd44ac328abd6142763e6055d908c1f6a9`
- **作者**: 邱郁晏
- **日期**: 2024-04-09 16:27:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`

### 47. [Web]Q00-20240409005 修正attachment物件OID为空时，会抛错[此URL没有下载文件的权限]的问题
- **Commit ID**: `e402abd17a5887a819655636d3d8fbd71afc95f5`
- **作者**: 周权
- **日期**: 2024-04-09 16:17:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 48. [雙因素模組]Q00-20240409003 信任端點裝置時間修正為24小時制
- **Commit ID**: `dfe3d7ec00496156fc940386c5ac066649aef382`
- **作者**: 林致帆
- **日期**: 2024-04-09 15:32:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 49. [ORGDT]V00-20240321002 修正Web組織管理工具中透過放大鏡搜尋部門後查看隸屬單位顯示錯誤問題
- **Commit ID**: `0282271015d1577b8002774f2eef43596413d2db`
- **作者**: yamiyeh10
- **日期**: 2024-04-09 11:42:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 50. [Web] Q00-20240408001 調整刪除流程的邏輯，改為不檢核直接刪除流程
- **Commit ID**: `33b335c4a35b9f221571cd07bab3bc039d88010c`
- **作者**: 邱郁晏
- **日期**: 2024-04-08 18:08:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 51. [Web] Q00-20240403002 Grid單身欄位加總異常问题修正
- **Commit ID**: `f2908a3f7348e351e0699fa0fa3067ea5f34d24b`
- **作者**: 刘旭
- **日期**: 2024-04-03 16:22:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 52. [Web] V00-20240402002 流程发起时选择流程重要性为：紧急，但是在待办事项列表中没有出现红色标记列问题修正
- **Commit ID**: `e24827f9f038e51d1cbe08f6b73721ed6c258e6a`
- **作者**: 刘旭
- **日期**: 2024-04-03 14:01:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 53. [Web]Q00-20240402001 修正[報表維護作業]產出的報表畫面欄位异常
- **Commit ID**: `3a2876a93ff6281039778037dbf48aca271c59c9`
- **作者**: 周权
- **日期**: 2024-04-02 16:59:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 54. [Web]V00-20240402001 修正待办事项中【由我处理】按钮点击没有反应
- **Commit ID**: `0c5a030daba466260aaefb1e5cc4ea8d165c9f39`
- **作者**: 周权
- **日期**: 2024-04-02 16:00:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 55. [Web]Q00-20240401001 修正XPDL简易流程图显示主旨异常
- **Commit ID**: `c42138c4f3cb75624bc847af9cafc613a1dc686d`
- **作者**: 周权
- **日期**: 2024-04-01 11:41:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`

### 56. [組織同步] Q00-20240329002 修正組織同步User帳號啟用邏輯異常，導致部分使用者同步後帳號變為未啟用
- **Commit ID**: `634951dafe2608136884e031600b67915f326c37`
- **作者**: 邱郁晏
- **日期**: 2024-03-29 15:28:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 57. [Web] Q00-20240328001 修正showDialog寫法異常
- **Commit ID**: `6a14e2e97c44027c0f271eff19be9435cce4f06d`
- **作者**: 邱郁晏
- **日期**: 2024-03-28 14:38:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 58. [Web]Q00-20240327003 BPM首頁的工作事項主旨內容增加顯示轉派的簽核意見
- **Commit ID**: `2a755da549277fea7fcb6069ca14bdd1d6c3f4fe`
- **作者**: 周权
- **日期**: 2024-03-27 17:27:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 59. [組織同步] Q00-20240327002 調整HR組織同步，塞入中介表資料若異常，不拋出錯誤
- **Commit ID**: `207289eb01513ffccd693fddb6610ba19b68606f`
- **作者**: 邱郁晏
- **日期**: 2024-03-27 15:12:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

### 60. [PRODT]Q00-20240327001 修正Web流程管理工具中匯入流程無法覆蓋流程進版的問題
- **Commit ID**: `327ae56b54cbe5e2d2068f72b1ac65093938163e`
- **作者**: pinchi_lin
- **日期**: 2024-03-27 10:49:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 61. [ORGDT]Q00-20240322002 修正Web組織管理工具中調離所有部門功能異常問題
- **Commit ID**: `097191f839d851d5c8b918a2b0a23236df5d001e`
- **作者**: pinchi_lin
- **日期**: 2024-03-27 10:06:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

