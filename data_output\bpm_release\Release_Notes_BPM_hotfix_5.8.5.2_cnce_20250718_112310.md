# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.5.2_cnce
- **舊版本**: release_5.8.5.2
- **生成時間**: 2025-07-18 11:23:10
- **新增 Commit 數量**: 38

## 變更摘要

### 郭哲榮 (2 commits)

- **2022-12-07 11:50:51**: [BPM APP]C01-20221202005 修正移動表單含有千分位的欄位在簽核後該數字會顯示異常的問題
  - 變更檔案: 1 個
- **2022-07-04 18:02:57**: [BPM APP]C01-20220627005 修正IMG中間層Grid元件的itemOrder有0時會造成欄位順序錯亂問題
  - 變更檔案: 1 個

### lorenchang (2 commits)

- **2022-07-04 16:40:39**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.2
  - 變更檔案: 25 個
- **2022-03-28 16:25:37**: [流程引擎]提高58版Queue併發承載量
  - 變更檔案: 20 個

### yanann_chen (20 commits)

- **2022-06-24 15:43:20**: [內部]Q00-20220624001 調整AutomaticSignOffMaintanceManagerBean NaNaLog內容
  - 變更檔案: 1 個
- **2022-05-03 12:01:14**: [流程引擎]S00-*********** 新增「自動簽核異常處理」功能
  - 變更檔案: 10 個
- **2022-02-10 17:51:10**: [ESS]Q00-20220210001 調整BPM取得ESS流程當前存檔狀態的邏輯，若ESS流程狀態是03，則不可再更新此流程在BPM的狀態
  - 變更檔案: 1 個
- **2022-01-11 16:58:59**: [流程引擎]Q00-20220111002 修正多人關卡在執行自動簽核時，偶發的沒有押上簽核意見或簽核意見押到正常簽核的工作上的問題
  - 變更檔案: 1 個
- **2021-10-27 14:42:30**: [ESS]Q00-20211026002 調整BPM呼叫ESS存檔前的判斷，防止同單據在ESS與BPM狀態不一致
  - 變更檔案: 2 個
- **2021-10-20 14:50:46**: [Web]Q00-20211020003 當響應式表單的下拉式選單元件設定為動態生成選項時，列印表單無法顯示欄位值
  - 變更檔案: 1 個
- **2021-09-30 16:04:06**: [流程引擎]Q00-20210930003 修正因有特製流程定義的資料，導致原本的流程定義刪除後無法再匯入相同流程
  - 變更檔案: 1 個
- **2021-09-24 18:25:51**: [ESS]Q00-20210924006 在呼叫ESS存檔前增加判斷，防止同單據在ESS與BPM狀態不一致
  - 變更檔案: 3 個
- **2021-07-30 17:52:04**: [流程引擎]Q00-20210730002 修正關卡設定「只有一個人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核
  - 變更檔案: 1 個
- **2021-07-20 09:00:46**: [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能[補]
  - 變更檔案: 1 個
- **2021-07-08 17:23:53**: [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能
  - 變更檔案: 1 個
- **2021-06-07 11:43:59**: [流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題
  - 變更檔案: 1 個
- **2021-06-02 15:44:58**: [流程設計師]Q00-20210531001 修正「複製有連接線的關卡造成實際流程派送發生異常」的問題
  - 變更檔案: 2 個
- **2021-03-17 16:18:42**: [Web]A00-20210317002 修正進入多個處理者僅需一人處理的待辦事項，若使用者未接收工作就返回待辦清單，畫面卡住問題
  - 變更檔案: 1 個
- **2021-05-20 15:30:42**: [流程引擎]Q00-20210520005 組織同步時，一併同步職務/職稱/角色的簡稱
  - 變更檔案: 1 個
- **2021-05-17 15:06:33**: [流程引擎]Q00-20210517001 調整DealOvertimeProcessHandler排程
  - 變更檔案: 1 個
- **2021-05-13 15:09:13**: [內部]排程DealOvertimeProcessHandler加入排查用的log
  - 變更檔案: 2 個
- **2021-04-26 18:21:18**: [內部]Q00-20210426002 調整活動逾時排程SQL，MSSQL加入WITH NOLOCK指令
  - 變更檔案: 1 個
- **2021-04-26 15:05:46**: [流程引擎]Q00-20210303003 修正「活動逾時排程執行過程中發生資料庫鎖定」問題
  - 變更檔案: 2 個
- **2021-04-26 11:49:07**: [流程引擎]Q00-20210426001 修正「流程設定流程撤銷事件，於多人關卡尚未處理時撤銷流程，發生資料庫鎖定(DB Lock)」問題
  - 變更檔案: 2 個

### walter_wu (10 commits)

- **2022-01-10 17:19:15**: [流程引擎]Q00-20211220002 修正客戶附件遺失問題
  - 變更檔案: 2 個
- **2021-08-16 13:48:13**: [流程引擎]Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回[補修正]
  - 變更檔案: 1 個
- **2021-08-13 19:23:24**: Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回
  - 變更檔案: 1 個
- **2021-07-08 14:56:23**: [內部]C01-20210707003 編輯線出錯時導致空白畫面，增加Log
  - 變更檔案: 3 個
- **2021-06-01 20:29:53**: [流程引擎]A00-20210527001 修正同關卡加簽兩次以上會出現流程壞掉的異常
  - 變更檔案: 1 個
- **2021-04-16 11:15:25**: [簽核流程設計師]Q00-20210416001 修正在簽出ProcessPackage時會誤取到CustomProcessPackage
  - 變更檔案: 1 個
- **2021-05-14 14:43:28**: [組織設計師]S00-20210222001 組織設計師優化 取消父類別開啟時載入整個組織 [補]
  - 變更檔案: 2 個
- **2021-05-13 15:44:04**: [組織設計師]S00-20210222001 組織設計師優化 取消父類別開啟時載入整個組織
  - 變更檔案: 3 個
- **2021-05-13 15:33:56**: Merge branch 'hotfix_5.8.5.2_cnce' of http://10.40.41.229/BPM_Group/BPM.git into hotfix_5.8.5.2_cnce
- **2021-05-13 15:33:06**: [內部]優化取得OrgDTO方式
  - 變更檔案: 1 個

### 詩雅 (1 commits)

- **2021-07-09 14:00:08**: [BPM APP]C01-20210511002 修正當啟用動態渲染，且使用Oracle資料庫時要取得IMG的流程列表會顯示無資料問題
  - 變更檔案: 3 個

### 林致帆 (3 commits)

- **2021-07-01 09:18:46**: [內部]Q00-20210630002 ESS回傳狀態及更新AppFormActivityRecord的事件優化log[補修正]
  - 變更檔案: 1 個
- **2021-06-30 19:25:23**: [ESS]Q00-20210630003 調整ESS單據發起流程時如果在資料表已有紀錄，就不該往下繼續派送
  - 變更檔案: 1 個
- **2021-06-30 19:13:51**: [內部]Q00-20210630002 ESS回傳狀態及更新AppFormActivityRecord的事件優化log
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. [BPM APP]C01-20221202005 修正移動表單含有千分位的欄位在簽核後該數字會顯示異常的問題
- **Commit ID**: `0daef0ca08c957d1af1943a4f54757a17c8445bd`
- **作者**: 郭哲榮
- **日期**: 2022-12-07 11:50:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`

### 2. [BPM APP]C01-20220627005 修正IMG中間層Grid元件的itemOrder有0時會造成欄位順序錯亂問題
- **Commit ID**: `cb4ed3e8026abc758552f90d69afafe3bad19bc5`
- **作者**: 郭哲榮
- **日期**: 2022-07-04 18:02:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`

### 3. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.2
- **Commit ID**: `d353b1f4a32883ef51838fae436af4616a640e63`
- **作者**: lorenchang
- **日期**: 2022-07-04 16:40:39
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 4. [內部]Q00-20220624001 調整AutomaticSignOffMaintanceManagerBean NaNaLog內容
- **Commit ID**: `edfe5207a9b545afef4b85cdeba9bd250b4c8c12`
- **作者**: yanann_chen
- **日期**: 2022-06-24 15:43:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/AutomaticSignOffMaintanceManagerBean.java`

### 5. [流程引擎]S00-*********** 新增「自動簽核異常處理」功能
- **Commit ID**: `1722e0a94ed5e05b18e24857febfa2feca441cc2`
- **作者**: yanann_chen
- **日期**: 2022-05-03 12:01:14
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AutomaticSignOffMaintanceDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/AutomaticSignOffMaintanceManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/AutomaticSignOffMaintanceManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/AutomaticSignOffMaintanceManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/AutomaticSignOffMaintance.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_5.8.9.1.xlsx`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.9.8.1_DML_Oracle.sql`

### 6. [流程引擎]提高58版Queue併發承載量
- **Commit ID**: `1b9fd51d92ff4e2c57113a91d96f7d5aac61d1f3`
- **作者**: lorenchang
- **日期**: 2022-03-28 16:25:37
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocConvertWithFileHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/MessageHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/QueueHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutoAgentPerformerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/BatchNoticeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/DeleteClosedProcessInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/E10SendSignInfoBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/EventDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/FormInstanceTransformerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MailerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/McloudPushInvokeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/NoCmDocumentsBackgroundServiceBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/TiptopCleanDocumentBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 7. [ESS]Q00-20220210001 調整BPM取得ESS流程當前存檔狀態的邏輯，若ESS流程狀態是03，則不可再更新此流程在BPM的狀態
- **Commit ID**: `3122c6bd4d79bfb078936f53426028c38e26df99`
- **作者**: yanann_chen
- **日期**: 2022-02-10 17:51:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 8. [流程引擎]Q00-20220111002 修正多人關卡在執行自動簽核時，偶發的沒有押上簽核意見或簽核意見押到正常簽核的工作上的問題
- **Commit ID**: `82a64edb761fb6f75dd0a296c591ebeb53ab454d`
- **作者**: yanann_chen
- **日期**: 2022-01-11 16:58:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 9. [流程引擎]Q00-20211220002 修正客戶附件遺失問題
- **Commit ID**: `baddbd809183ce0df08a0070edb34a0b16c48af1`
- **作者**: walter_wu
- **日期**: 2022-01-10 17:19:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 10. [ESS]Q00-20211026002 調整BPM呼叫ESS存檔前的判斷，防止同單據在ESS與BPM狀態不一致
- **Commit ID**: `41829c522a3c80c9bd7abd55c6bee3c74c4e2977`
- **作者**: yanann_chen
- **日期**: 2021-10-27 14:42:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 11. [Web]Q00-20211020003 當響應式表單的下拉式選單元件設定為動態生成選項時，列印表單無法顯示欄位值
- **Commit ID**: `55b9a5cff7b44b93041575de93cf2441042f2e14`
- **作者**: yanann_chen
- **日期**: 2021-10-20 14:50:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 12. [流程引擎]Q00-20210930003 修正因有特製流程定義的資料，導致原本的流程定義刪除後無法再匯入相同流程
- **Commit ID**: `2f58b57ef16eef7c17f638ad815254ae4fc5c22f`
- **作者**: yanann_chen
- **日期**: 2021-09-30 16:04:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 13. [ESS]Q00-20210924006 在呼叫ESS存檔前增加判斷，防止同單據在ESS與BPM狀態不一致
- **Commit ID**: `dccc5b68a113354162b42cb74776619d23b79e83`
- **作者**: yanann_chen
- **日期**: 2021-09-24 18:25:51
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/dao/OJBAppFormActivityRecordDAO.java`

### 14. [流程引擎]Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回[補修正]
- **Commit ID**: `cd7bc07b5a0ce75fa389dbc9219e640d34b7a977`
- **作者**: walter_wu
- **日期**: 2021-08-16 13:48:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 15. Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回
- **Commit ID**: `015974aaee922b5f656b809102a4a578b6ddf216`
- **作者**: walter_wu
- **日期**: 2021-08-13 19:23:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 16. [流程引擎]Q00-20210730002 修正關卡設定「只有一個人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核
- **Commit ID**: `574d447665e767f98f22d6509650daf999206234`
- **作者**: yanann_chen
- **日期**: 2021-07-30 17:52:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 17. [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能[補]
- **Commit ID**: `70c23e5dcfcde3bd123db158d7307d2c5f4aa68b`
- **作者**: yanann_chen
- **日期**: 2021-07-20 09:00:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 18. [BPM APP]C01-20210511002 修正當啟用動態渲染，且使用Oracle資料庫時要取得IMG的流程列表會顯示無資料問題
- **Commit ID**: `3c605441c91d239dee696a5f30d0842e75e62560`
- **作者**: 詩雅
- **日期**: 2021-07-09 14:00:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 19. [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能
- **Commit ID**: `f5066d1f508328c590c8f449b4566d9696424dd0`
- **作者**: yanann_chen
- **日期**: 2021-07-08 17:23:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 20. [內部]C01-20210707003 編輯線出錯時導致空白畫面，增加Log
- **Commit ID**: `7e6b6f9f20f2e83c1dcac36a61759e2294e464bf`
- **作者**: walter_wu
- **日期**: 2021-07-08 14:56:23
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/model/ProcessDefinitionModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/model/ProcessPackageModel.java`

### 21. [內部]Q00-20210630002 ESS回傳狀態及更新AppFormActivityRecord的事件優化log[補修正]
- **Commit ID**: `38332b293d0ae526ed24f38bd15fcf66d606522e`
- **作者**: 林致帆
- **日期**: 2021-07-01 09:18:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 22. [ESS]Q00-20210630003 調整ESS單據發起流程時如果在資料表已有紀錄，就不該往下繼續派送
- **Commit ID**: `e7e5ff3c344dfcfc15da75d5f8322350c0e1aa1c`
- **作者**: 林致帆
- **日期**: 2021-06-30 19:25:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 23. [內部]Q00-20210630002 ESS回傳狀態及更新AppFormActivityRecord的事件優化log
- **Commit ID**: `b8dd7adefbb1e67b851e1310107c0d68aa69115a`
- **作者**: 林致帆
- **日期**: 2021-06-30 19:13:51
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/dao/OJBAppFormActivityRecordDAO.java`

### 24. [流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題
- **Commit ID**: `555532df47c0789eb76b2de27712729294ec5a5d`
- **作者**: yanann_chen
- **日期**: 2021-06-07 11:43:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 25. [流程設計師]Q00-20210531001 修正「複製有連接線的關卡造成實際流程派送發生異常」的問題
- **Commit ID**: `b7dc4f15ebfc43f50677fd563bf818cb818999f9`
- **作者**: yanann_chen
- **日期**: 2021-06-02 15:44:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramAction.java`

### 26. [流程引擎]A00-20210527001 修正同關卡加簽兩次以上會出現流程壞掉的異常
- **Commit ID**: `86c2c3e687024e01352a6061a4b5bfad2e9a66c8`
- **作者**: walter_wu
- **日期**: 2021-06-01 20:29:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 27. [簽核流程設計師]Q00-20210416001 修正在簽出ProcessPackage時會誤取到CustomProcessPackage
- **Commit ID**: `7cf4bc79bc31e9c24c32409a542d1f7f9d008eab`
- **作者**: walter_wu
- **日期**: 2021-04-16 11:15:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 28. [Web]A00-20210317002 修正進入多個處理者僅需一人處理的待辦事項，若使用者未接收工作就返回待辦清單，畫面卡住問題
- **Commit ID**: `c6a404c0997d687d52ab35061c4d38c408536619`
- **作者**: yanann_chen
- **日期**: 2021-03-17 16:18:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 29. [流程引擎]Q00-20210520005 組織同步時，一併同步職務/職稱/角色的簡稱
- **Commit ID**: `ea5296f811f603280a205946cbe9abe657fe906a`
- **作者**: yanann_chen
- **日期**: 2021-05-20 15:30:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 30. [流程引擎]Q00-20210517001 調整DealOvertimeProcessHandler排程
- **Commit ID**: `17b031d933b5b6e6ced78a838d7fc98cc7ba4a50`
- **作者**: yanann_chen
- **日期**: 2021-05-17 15:06:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 31. [組織設計師]S00-20210222001 組織設計師優化 取消父類別開啟時載入整個組織 [補]
- **Commit ID**: `92e259a1806ecdbac13b068e68973c81ae5c79b5`
- **作者**: walter_wu
- **日期**: 2021-05-14 14:43:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainFunctionLevelDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainUnitLevelDialog.java`

### 32. [組織設計師]S00-20210222001 組織設計師優化 取消父類別開啟時載入整個組織
- **Commit ID**: `94412321a56cf8a66b55a7695b525ac8918f18fa`
- **作者**: walter_wu
- **日期**: 2021-05-13 15:44:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/AbstractTableDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainFunctionLevelDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainUnitLevelDialog.java`

### 33. Merge branch 'hotfix_5.8.5.2_cnce' of http://10.40.41.229/BPM_Group/BPM.git into hotfix_5.8.5.2_cnce
- **Commit ID**: `17ec4c7a636418625dd5757f9dae88578fff870f`
- **作者**: walter_wu
- **日期**: 2021-05-13 15:33:56
- **變更檔案數量**: 0

### 34. [內部]優化取得OrgDTO方式
- **Commit ID**: `df98e5d0e8e780301cfb9088ec737a5822f8a225`
- **作者**: walter_wu
- **日期**: 2021-05-13 15:33:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 35. [內部]排程DealOvertimeProcessHandler加入排查用的log
- **Commit ID**: `acf46dbb0c613d17fdf6d71b697f753528fb33a7`
- **作者**: yanann_chen
- **日期**: 2021-05-13 15:09:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 36. [內部]Q00-20210426002 調整活動逾時排程SQL，MSSQL加入WITH NOLOCK指令
- **Commit ID**: `61c251689177d4ff76dbcb10e82e7e83648b7062`
- **作者**: yanann_chen
- **日期**: 2021-04-26 18:21:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 37. [流程引擎]Q00-20210303003 修正「活動逾時排程執行過程中發生資料庫鎖定」問題
- **Commit ID**: `4f60dc326b10b8e1b55a15dc1fbe402a63f3649f`
- **作者**: yanann_chen
- **日期**: 2021-04-26 15:05:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineLocal.java`

### 38. [流程引擎]Q00-20210426001 修正「流程設定流程撤銷事件，於多人關卡尚未處理時撤銷流程，發生資料庫鎖定(DB Lock)」問題
- **Commit ID**: `a6e541888959fc6c23d76a0eaf6f0b496e6fd90b`
- **作者**: yanann_chen
- **日期**: 2021-04-26 11:49:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerLocal.java`

