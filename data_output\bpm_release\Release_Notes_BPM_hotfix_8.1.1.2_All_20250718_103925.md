# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_8.1.1.2_All
- **舊版本**: release_8.1.1.2
- **生成時間**: 2025-07-18 10:39:25
- **新增 Commit 數量**: 1

## 變更摘要

### lorenchang (1 commits)

- **2025-07-10 11:15:37**: [ESS]C01-20250604003 增加避免串單的卡控機制：1.檢查當前流程與Session內的ProcessSerialNumber是否匹配，2.檢查Identifier有沒有被其它流程用過
  - 變更檔案: 7 個

## 詳細變更記錄

### 1. [ESS]C01-20250604003 增加避免串單的卡控機制：1.檢查當前流程與Session內的ProcessSerialNumber是否匹配，2.檢查Identifier有沒有被其它流程用過
- **Commit ID**: `53258e95951181ea7192be0abc703ae3ddaff734`
- **作者**: lorenchang
- **日期**: 2025-07-10 11:15:37
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/exception/AppFormProcessMismatchException.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormCacheSingletonCollection.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AppFormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/appform/EPIBasePageJS.js`

