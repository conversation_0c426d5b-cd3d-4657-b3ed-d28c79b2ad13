{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.6.5.4_1", "date": "tag 5.6.5.4_1\nTagger: 施翔耀 <jose<PERSON><PERSON><EMAIL>>\n\n2018-06-12 10:46:00", "message": "修正Oracle取得時間語法", "author": "yamiyeh10"}, "舊分支": {"branch_name": "5.6.5.3_1", "date": "tag 5.6.5.3_1\nTagger: 施翔耀 <jose<PERSON><PERSON><PERSON>@digiwin.biz>\n\n2018/04/17 14:30 last build2018-04-17 10:42:57", "message": "修正鼎捷移動session already invalided 問題", "author": "ChinRong"}, "比較時間": "2025-07-28 18:11:33", "新增commit數量": 51, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "ac1ac22943164b0259d7cc081f77962940c62d22", "commit_訊息": "修正Oracle取得時間語法", "提交日期": "2018-06-12 10:46:00", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.3_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "41c46082a049d8709c4618a64a39e26e2109f6a2", "commit_訊息": "修改Web表單設計器的格線設定，增加拖曳元件時的間距設定", "提交日期": "2018-06-12 10:36:02", "作者": "<PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5654.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "bfe46d7d246846a929caaa144d5da19cadc036dc", "commit_訊息": "修正議題", "提交日期": "2018-06-11 15:06:21", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "41f87c04d1307099cc2546164708aa3d5cb24181", "commit_訊息": "修正Grid新增時如果有特殊符號時表單中間層顯示錯誤", "提交日期": "2018-06-08 11:24:53", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4e61b278ad1fb88a8da92cb0f877bf10b8be2d56", "commit_訊息": "修正MCloud修改語系無法生效的問題", "提交日期": "2018-06-08 10:51:55", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "50cb37f70473c995839dc7b3eea1340a6d04d50c", "commit_訊息": "修正議題", "提交日期": "2018-06-08 10:19:12", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba63c7a0e0122ca575741ef247ab548301e27d23", "commit_訊息": "修正議題", "提交日期": "2018-06-08 09:12:43", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "afcad1733b6e5f3104e8e85640ed5696cc27026b", "commit_訊息": "修正行動版客製開窗異常及更新多語系", "提交日期": "2018-06-07 17:33:05", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5654.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ca22e709f793d9ca83d58c09ed4090ded37f4d83", "commit_訊息": "補上轉派資訊多語系", "提交日期": "2018-06-07 16:24:00", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5654.xls", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "69a02ae7199d9b7c928faba054baf676822a2e6d", "commit_訊息": "修正企業微信多語系異常", "提交日期": "2018-06-07 15:59:18", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ebba8849d69b058cddafba9c1df158a57b2bb23f", "commit_訊息": "發單RESTful服務回傳流程序號", "提交日期": "2018-06-07 15:46:30", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9714c185bfc2f5e769b80910bcd1b82ce836998a", "commit_訊息": "修正議題", "提交日期": "2018-06-07 15:30:09", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "06f36135b8562aee367d078805663e8dfc554ef9", "commit_訊息": "優化Web表單設計器，提升表單開啟效能，將表單輔助格線變更為3種模式，分別為5x5 10x10 20x20。", "提交日期": "2018-06-07 11:03:18", "作者": "<PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppAbsoluteDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/util.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "7ef77b6ff75a5f88048dbdf56f61c4255105265f", "commit_訊息": "新增鼎捷移動列表轉派資訊欄位", "提交日期": "2018-06-07 09:08:20", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderUtil.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "812bebcac33f22a7cc78e91c5122327d8c648735", "commit_訊息": "A00-20180116001 修正文件一覽表 文件型態階層條件\"會無法查詢到文件", "提交日期": "2018-06-06 17:41:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "452f2f1188cd8d0100befe7415f580387de9bf60", "commit_訊息": "A00-20180402002 修正分區文管 新增單文件類別無法開啟下階類別", "提交日期": "2018-06-06 17:40:50", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ead0ad01b10109c73fc1b2d1929cc203bd0876a0", "commit_訊息": "修正 : ISO開啟檔案異常", "提交日期": "2018-06-04 16:50:54", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "44279559f9215581dcb1cc9b62942ef48e5cec31", "commit_訊息": "Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop", "提交日期": "2018-06-04 16:37:29", "作者": "jose<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "cb563a6181426e537001f95ff10a5f1ed17c7d0a", "commit_訊息": "A00-20180316001 調整 ESS回傳錯誤代碼判斷格式 ,避免非錯誤代碼文字影響判斷", "提交日期": "2018-06-04 16:36:39", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0ea272cf0654577399a471aa2d1441a73986dfa0", "commit_訊息": "修改組織HRM小助手同步當部門失效時，忽略離職人員設定此單位為主部門的卡控。", "提交日期": "2018-06-04 16:10:18", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f18f82c104a83f9afc5b8529248735226b652658", "commit_訊息": "調整SQL語法增加離職日期判斷", "提交日期": "2018-06-04 15:41:05", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_SQLServer.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "eb2f313dfa3f9718dd547ef164faafef41971f02", "commit_訊息": "修正行動表單預覽pdf檔案當附檔名為大寫時無法預覽的問題", "提交日期": "2018-06-04 14:00:06", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "216ce417199b0a2fd6663c5d6cb9e1031195816b", "commit_訊息": "A00-20180514001 修正 :  資料選取器開窗後，GRID欄位沒有按照設定所顯示", "提交日期": "2018-06-04 10:25:20", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c20ac1fbae3b3081994807fbcd55e714f6ed0c4f", "commit_訊息": "A00-20180514003 修正 Button元件,設定為SQL註冊器開窗 ,在選取資料後,呼叫的方法無作用", "提交日期": "2018-06-01 14:16:15", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/CustomDataChooserTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7052337d71d7db76956a13369425c70973022e87", "commit_訊息": "C01-20180425002 調整 發信及發通知 不會因為Qeueu報錯而Rollback", "提交日期": "2018-05-30 15:30:50", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c9800c991d3dcc289d6ed68a7730d43fee4e0d0", "commit_訊息": "修正RemoteObjectProvider.createWorkflowServerManager method中存在session中的WorkflowServerManagerDelegate session key錯誤問題", "提交日期": "2018-05-30 10:05:09", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6c6b64c614e8d3bc3ae0200829781d74bce7cf53", "commit_訊息": "C01-*********** 修正企業微信日期、時間元件設為disabled後仍可編輯問題", "提交日期": "2018-05-24 15:15:58", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2f49b7f83b404a97fa62752a4d83c9cff29c3b34", "commit_訊息": "修正產品開窗在字數過多時會產生重疊問題", "提交日期": "2018-05-24 13:47:41", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileProductOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "870e8fcd8536a52184a55f061b187eec823f2188", "commit_訊息": "修改OA模組判斷(ProcessID小於3不能繼續派送)", "提交日期": "2018-05-23 10:22:45", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e87aa9b3a9dd24d713f5a2915bbf7e2d49844073", "commit_訊息": "修正加簽進階搜尋功能異常,需先引入material再引pdf的js檔案", "提交日期": "2018-05-18 18:34:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9bb5ea75bce7187661a8377a188c2cba3ef13bcc", "commit_訊息": "C01-20180509002 修正行動任務導航加簽畫面的左上角放大鏡無法搜尋人員", "提交日期": "2018-05-18 18:31:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f1dc4100247f27b9dcf3a6e2ae494857a83fd6d3", "commit_訊息": "A00-20180510004 行動版Grid用FormScript隱藏欄位後，查看頁面多一個逗號", "提交日期": "2018-05-17 17:59:43", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed11491651c4082632d6af65984f0b0e5211f467", "commit_訊息": "修改OA模組判斷(ProcessID小於3不能繼續派送)", "提交日期": "2018-05-17 17:48:26", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cc726f86e29472f8f44c82215e52301871990731", "commit_訊息": "[A00-20180129002]修正簽核流程設計師,修改核決權限關卡的ID項目值時未存入資料庫。", "提交日期": "2018-05-16 11:43:04", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b69be61a28cdcbb76dcba9a50b9ce4e3403b29a2", "commit_訊息": "[C01-20180208001]修正流程負責人在管理流程時，加入收尋自動簽核狀態的單據。", "提交日期": "2018-05-16 10:35:38", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "85839270c45dce03e19e8d6c27efe594d9ca5bd4", "commit_訊息": "修正APP因多語系含特殊字元造成無法發單或簽單問題", "提交日期": "2018-05-15 16:27:12", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "db1857f5e675f9fb0eb886dd59c5d3246c6dde27", "commit_訊息": "Q00-20180503001 修正流程有自動簽核功能時簽核歷程異常問題", "提交日期": "2018-05-09 10:05:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1349b718d6123e7bd609a8d28062fe3e3fcb2b45", "commit_訊息": "C01-20180426004 修正Grid元件繫結日期元件時Grid頁面顯示異常問題", "提交日期": "2018-05-04 16:41:48", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "518c7012f48647db68ea58fce566f9d6f4eab608", "commit_訊息": "修正Grid內設定動態隱藏與顯示元件異常問題", "提交日期": "2018-05-04 13:07:42", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d7b397fe567c6443f19d6d5b381aa25354f1c42", "commit_訊息": "調整鼎捷移動服務取得session的方式", "提交日期": "2018-05-03 13:50:00", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1d94fa545b07f63546e0024d178e73f8b734417c", "commit_訊息": "C01-20180426001 修正鼎捷移動中間層點擊同意或不同意後提交失敗問題", "提交日期": "2018-05-03 11:42:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c099bfb879f736216e7314ea67707bcb8269a47a", "commit_訊息": "在新增表單及新增流程的ID長度卡控，限制字元長度為40。", "提交日期": "2018-04-30 17:54:58", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/IDValidator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0015237f7305310ad249acf4d8521b7916c2918f", "commit_訊息": "C01-20180408002 修正tiptop表單在同意派送後會有串單問題", "提交日期": "2018-04-27 13:45:43", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f4d699abb9433ebdd9ba57acccf33ea95ca304fb", "commit_訊息": "修正客製開窗無法開窗議題", "提交日期": "2018-04-27 12:09:43", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "48549cedeb5491655eb453aabd0dfe3f1b4f093a", "commit_訊息": "C01-20180330002 修正儲存表單後，表單大小恢復成預設值的異常", "提交日期": "2018-04-26 11:09:40", "作者": "<PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "09b3511cfb245469b037da7cb17fb898d9d97871", "commit_訊息": "C01-20180416001 修正Grid在有特殊符號內容時會有符號轉換問題", "提交日期": "2018-04-25 16:55:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "23d8779bbb50575f00235b7a41151be83f146365", "commit_訊息": "調整客製多選開窗流水號", "提交日期": "2018-04-25 16:40:38", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c904593ef7cd997d4da7831db04586d2b30f2f02", "commit_訊息": "[C01-20180301001]修正TipTop發單到BPM,附件無寫入NoCmDocument導致無法開啟的問題。", "提交日期": "2018-04-25 09:45:11", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "957efd8709fddaa52586dc3aae572436701f78b4", "commit_訊息": "修改調閱ISO文件瀏覽開窗權限判斷", "提交日期": "2018-04-23 21:12:59", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5f2b4d724af357dae14dc1b916ab1064e5d0f4a2", "commit_訊息": "修正退回重辦時有預設模式,仍需點選退回模式才能退簽問題", "提交日期": "2018-04-18 09:50:01", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f303b40efbffebee9c7fce07bf0c2c6e734d942b", "commit_訊息": "修正pdfjs議題中錯誤的程式碼", "提交日期": "2018-04-17 17:58:51", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}]}