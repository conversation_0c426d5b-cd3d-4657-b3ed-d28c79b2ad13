{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.7.5.1", "date": "tag 5.7.5.1\nTagger: 施翔耀 <jose<PERSON><PERSON><PERSON>@digiwin.biz>\n\nlast build 108/03/27  10:41:452019-03-27 10:38:43", "message": "調回Linux啟動conf檔", "author": "jerry1218"}, "舊分支": {"branch_name": "5.7.4.2", "date": "tag 5.7.4.2\nTagger: 施翔耀 <jose<PERSON><PERSON><PERSON>@digiwin.biz>\n\nLast build 2019/01/28  17:002019-01-28 17:13:24", "message": "Q00-20190128001 修正用Excel匯入多語系 如果值有('單引號)會匯入失敗", "author": "walter_wu"}, "比較時間": "2025-07-28 18:00:15", "新增commit數量": 189, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "83920ffc7438a1397d485e04bba1e780a8cda2be", "commit_訊息": "調回Linux啟動conf檔", "提交日期": "2019-03-27 10:38:43", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/standalone.conf", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3721ed24f54410ee29a984479ba8d9f57678a8c5", "commit_訊息": "Q00-20190321005 調整update TIPTOP restful的語法", "提交日期": "2019-03-27 09:48:24", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_1.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_1_Check.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_2.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_1.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_1_Check.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_2.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "a58ba23760e61099ee089954c75d8a0ced464c08", "commit_訊息": "Q00-20190321005 調整TT出貨的流程將sessionBean調整為restful", "提交日期": "2019-03-26 16:08:28", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/bpmn/5.25/\\350\\253\\213\\350\\263\\274\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f0bcf95b78c59de3730160ff21ffecc60a6cd3cf", "commit_訊息": "Q00-20190321005 提供回寫TT的restful接口的updateSQL", "提交日期": "2019-03-26 15:38:22", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "005983824e8db3f5a6a9963257e6882d1fb635d9", "commit_訊息": "調整設計工具登入頁排版", "提交日期": "2019-03-26 15:30:10", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "3e6890ebf5259c7c961470c8c6671c27c1767518", "commit_訊息": "Q00-20190321005 因應TT多主機sessionBean回寫，增加回寫TT的restful接口", "提交日期": "2019-03-26 15:28:58", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/TIPTOP.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/TIPTOPMgr.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "e58dedd8ad5c8a7ffea38cd805844890f3525c40", "commit_訊息": "S00-20190213001 增加log-QRCode簽核時，參考系統參數設定驗證時間", "提交日期": "2019-03-26 15:20:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/QRCodeLoginCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e95e3ed3fcc0d765a18bcbae1c8ac90f584774a7", "commit_訊息": "Q00-20190326001 A00-20190326001 修正組織設計師-工作行事曆-新增無回應問題", "提交日期": "2019-03-26 14:14:13", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/JCalendar/jcalendar-1.3.2.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/JCalendar/jcalendar.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/CMPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/JCalendar/jcalendar.jar", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 7}, {"commit_hash": "1e139bdd1664ba2b9ab551ea0a844b7ef16326cc", "commit_訊息": "Q00-20190322002 <二次修正> 還原誤簽上去的程式碼", "提交日期": "2019-03-26 09:07:12", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d24596c9d4d81db957c7d2fe39225b785e4674b2", "commit_訊息": "Q00-20190325002 IMG的智能示警在有處理記錄時的彈出視窗提示內容調整多語系", "提交日期": "2019-03-25 18:52:27", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "18cb3a1a9259be1abf7d91ebf4a4dc245d12012d", "commit_訊息": "Q00-20190325005 IMG的加入重要流程功能註解", "提交日期": "2019-03-25 18:46:26", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fea7ca61d9e1380117c1a71e06cb4aff813cc339", "commit_訊息": "Q00-20190325006 修正離職人員工作轉派 如果未選接收者 就按下批次轉派 開出來的選人窗會請洽系統管理員", "提交日期": "2019-03-25 15:43:56", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "00243f9e0c898fa31ca04d0ea19e36cb129c6538", "commit_訊息": "Q00-20190322002 推播中間層簽核的待辦事項打開來都會是\"找不到工作項目\"", "提交日期": "2019-03-22 20:31:39", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "16b2be13e3814640b61352c42196872c5c0a20b9", "commit_訊息": "Q00-20190322001 同意派送RESTful服務如果有多個關卡活動中且都為同處理人，呼叫接口後會將任一關卡往下派送。", "提交日期": "2019-03-22 20:09:58", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7d7c5424a40fde403675f2d1733b4084ad6ffb54", "commit_訊息": "Q00-20190321003 調整取消訂閱多語系", "提交日期": "2019-03-22 18:29:58", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "6742f3a45e0ee2c616d6b65f01ec98d4c2f2870d", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-03-22 16:12:04", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "8cef50ea9a4f4167293f7d90e15b384f98c6fe77", "commit_訊息": "Q00-20190320002 修正表單的FormOpen階段載入資料並更新Grid,會因為Grid還沒加載完成而導致錯誤。", "提交日期": "2019-03-22 16:09:54", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5b1fc32f37db407f45c0c7d0670c81ac926a06bf", "commit_訊息": "<V57> C01-20181113002 修正 :ESS單上按儲存草稿，會跳出呼叫AppForm網路服務失敗的錯誤訊息", "提交日期": "2019-03-22 16:08:58", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4a30e75131b0afe9b0410aba837aa4243b94f54a", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-03-22 11:50:19", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "54311feb9d78a8d07754016cb68839770b130c74", "commit_訊息": "A00-20190215005 修正千分位顯示異常問題(2)", "提交日期": "2019-03-22 11:49:39", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "13e5aab9e73fd8b511b9ac40bc6edbfefb855760", "commit_訊息": "Q00-20190315008 <二次修正>修正因調整listreader造成IMG處理的流程過濾功能無法使用的問題", "提交日期": "2019-03-22 11:44:02", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "315fabb888a99acf01f1f6b8aba7d3f1ee0ed931", "commit_訊息": "Q00-20190215002 修正web表單設計師響應式表單在APP未啟用時一樣會作行動版表單初始化問題", "提交日期": "2019-03-22 10:39:37", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8596097b8c1a2196821cafe4e6e0ce10bd32d92c", "commit_訊息": "Q00-20190215001 修正web表單設計師絕對位置表單在APP未啟用時一樣會作行動版表單初始化問題", "提交日期": "2019-03-22 10:37:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "23de70e0b12609c522d5d53e8a52589d8b878ce6", "commit_訊息": "Q00-20190321007 修正響應式表單設計器使用複製元件，行動版設計器沒有效果", "提交日期": "2019-03-22 10:02:35", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b3ca4bd0d67df3033d6a236a467766b36e376d48", "commit_訊息": "Q00-20190321006 修正響應式表單設計器復原上一動後，行動版相對位置設計器的欄位模版會無法新增。", "提交日期": "2019-03-22 09:38:41", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f88c084575bb7c410eab6ebc84d9003df57fb356", "commit_訊息": "Q00-20190321008 修正一般表單設計器使用Ctrl+z復原上一動，行動版相對位置設計器的欄位模版會出現好幾個", "提交日期": "2019-03-22 09:36:30", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "87a287d8cc8921fb4c1c26de3ddff3665506f288", "commit_訊息": "Q00-20190321002 調整腳本樣本行動版\"外部網址開窗\"", "提交日期": "2019-03-21 20:13:37", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "b1a15c0a0977722cf7ffcce12d435776087de7e0", "commit_訊息": "Q00-20190315007 二次修正因IMG傳遞的processId有引號導致異常", "提交日期": "2019-03-21 20:09:21", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4388fef62bc892877418089ce146bb947a3054df", "commit_訊息": "Q00-20190320001 修正App在Grid資料欄位是純數字時會有呼叫失敗問題", "提交日期": "2019-03-21 18:41:10", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "df8a1cbfb968e1da7021c51d4d2e771fa8ef0ea6", "commit_訊息": "Q00-20190321004 修正IMG加入重要流程重複加入時的提示訊息多語系異常", "提交日期": "2019-03-21 16:17:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "776ebaf8711bdd7a6efaa5cba8cd48cec9eb5471", "commit_訊息": "Q00-20190221001 調整發送郵件內容到行動版裝置的機制", "提交日期": "2019-03-21 15:53:53", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "34f0f29650c03d9eb17b6dc7630cf35b7e0f0c98", "commit_訊息": "Q00-20190315007 修正追蹤流程RESTful服務(取清單接口、統計接口、總計接口)服務異常", "提交日期": "2019-03-20 18:02:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e4e0410da2b2b838d1a236cc5a0832dcfdb7e5f4", "commit_訊息": "C01-20170809003 修正:組織同步執行順序異常,導致新增部門時會找不到部門核決層級", "提交日期": "2019-03-20 14:55:38", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ws/ImportOrgDocBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e27c713379f573088d8d0d830d87f5c0799bc99a", "commit_訊息": "修正流程設計師-一般關卡-處理者新增視窗遺漏的多語系", "提交日期": "2019-03-20 14:33:54", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "6325423233053aad47ef732adf8a0c9d5035173a", "commit_訊息": "<V57>A00-20190130003 調整:新增組織同步時,因DB資料被刪時可以識別的錯誤訊息", "提交日期": "2019-03-20 14:20:53", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7add5ee7cf23c51729d64cdaf08171b0d16db61", "commit_訊息": "調整IMG智能快簽與重要流程功能", "提交日期": "2019-03-20 11:43:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "80368cd719c3664a188f9f38cb9c04afc9d4edfd", "commit_訊息": "移除多餘的log", "提交日期": "2019-03-20 11:19:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2cf6f1ece69b76af58a19de28e4a4452bbdaf896", "commit_訊息": "Q00-20190315006 修正待辦清單RESTful服務取得的行動版直連表單網址打開來一片空白", "提交日期": "2019-03-20 11:12:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d9daf03963cdee880608858c513c6e1403138e2b", "commit_訊息": "Q00-20190315002 修正同意派送RESTFul在參數資料內容有誤時不是預期的回應格式", "提交日期": "2019-03-19 15:31:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7ac35cc6fc0a6200db055dddd9b561ab0e2c1ea", "commit_訊息": "Q00-20190125002 修正App表單元件點擊清除按鈕會觸發取消焦點功能", "提交日期": "2019-03-19 10:27:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c9dbe07ae5719ea8173aed14b817e1731e78f666", "commit_訊息": "Q00-20190307001 新增取未結案處理的流程的接口", "提交日期": "2019-03-18 17:51:44", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bc6064cdfe386479e046027c091dfcd1fea9e375", "commit_訊息": "Q00-20190315008 修正已轉派流程總計RESTful接口platform參數傳入\"mobile\"沒效果", "提交日期": "2019-03-18 17:30:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4c9a6fc3fba139a00623910af97434f15ca8373e", "commit_訊息": "Q00-20190314016 調整IMG快速簽核與詳情的預計關卡過濾服務與通知關卡", "提交日期": "2019-03-18 17:27:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb4c06c8f4a22f638f5367c5e9887ff32da228dc", "commit_訊息": "Q0020190314007 智能快簽功能的進階篩選功能沒效果。", "提交日期": "2019-03-18 17:13:44", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "026d5ed5329de0a398e93defbd56c06470f67e74", "commit_訊息": "C01-20190123004 二次修正 : ISO文件一覽表的ISO文件階層無法顯示", "提交日期": "2019-03-18 17:04:37", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "53ce5be5078e446170225d310ae58e61d20709aa", "commit_訊息": "Q00-20190215004 調整簽核流設計師中行動版表單權限控管會卡控APP序號是否註冊或過期", "提交日期": "2019-03-15 18:48:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/FormSelectDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "838d2ee7eabcb39e3a0864d693646cd36649e9a6", "commit_訊息": "Q00-20190315010 修正資料庫是Oracle的時候  使用流程監控資訊封存  不管是封存資料或是解封存資料都會報錯", "提交日期": "2019-03-15 18:23:49", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "901117fbe90b5ea6c11634624c7ed1a2320a185a", "commit_訊息": "Q00-20190314010 將IMG預測關卡的流程有設定條件運算式註解打開", "提交日期": "2019-03-15 17:37:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "16e6574eb562252849e130d557d3eda47aaf24ce", "commit_訊息": "補上缺少的 QRCode多語系", "提交日期": "2019-03-15 17:26:42", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "87eb1f20f032216075c686a8d0adad3fa36d138a", "commit_訊息": "修正 : E10表單同步異常", "提交日期": "2019-03-15 17:00:11", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "156a326b1e3c9eb8d635ddb46bb3e82378fe37c8", "commit_訊息": "Q0020190314008 智能快簽功能的進階搜尋功能沒效果。", "提交日期": "2019-03-15 16:55:24", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5092e6b47d59390448e68fdf6f5c3b481474aaed", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-03-15 16:53:41", "作者": "劉建德", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "5962d9856170f99fc1b0b977aad1274578d17ce3", "commit_訊息": "Q00-20190215003 調整簽核流設計師中的支援手持裝置選項卡控APP序號註冊或過期", "提交日期": "2019-03-15 16:21:05", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c1da504a8aa48ca1dfac7070973ff72501b3899c", "commit_訊息": "Q00-20190314013 鼎捷移動快速簽核依照工作權限動態產生的按鈕中，沒有\"表單詳情\"按鈕。", "提交日期": "2019-03-15 16:00:14", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1708e28028e70c2951089d9e78c38bb0b12597d1", "commit_訊息": "Q00-20190314005 調整移動消息訂閱管理的多語系", "提交日期": "2019-03-15 10:58:21", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ae270af1a72632764d2ddba620a0b6c3b173d0cb", "commit_訊息": "Q00-20190221005 調整App在服務重啟後若沒有先登入過BPM，直接登入IMG的APP會有403錯誤", "提交日期": "2019-03-14 19:24:55", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b913c017f26507af0c403f8a8f551061d219dd65", "commit_訊息": "Q00-20190225002 IMG快速簽核畫面調整若欄位無資料時不顯示其區塊", "提交日期": "2019-03-14 17:46:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "85c83736a164f999f845394cdc1a505695723a38", "commit_訊息": "Q00-20190314011 openJDK議題,barcode元件在openJDK環境下無法使用,故修改第三方套件barbecue.jar改為可支援的寫法", "提交日期": "2019-03-14 17:24:30", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/lib/Barbecue/barbecue.jar", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b2f800f8f5c66a0c08889c7c7c1ed487384146cc", "commit_訊息": "Q00-20190314005 調整個人移動消息訂閱管理的多語系", "提交日期": "2019-03-14 15:24:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribe.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9160d23631ae30c7959c55c0ab0351b073bf3add", "commit_訊息": "Q00-20190314006 修正SYNC_ISO 文件匯入後，透過文管首頁閱讀檔案開啟時報錯，無法閱讀檔案", "提交日期": "2019-03-14 14:13:18", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "47e302f7298680c7b748622e34746042eb091640", "commit_訊息": "Q00-20190312001 停用管理員的管理消息訂閱頁面的全選功能", "提交日期": "2019-03-14 12:09:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d69becd30bc56d8fcdb631000feae3efbeff5d7d", "commit_訊息": "Q00-*********** 調整取最近含有示警訊息的工作清單功能效能問題", "提交日期": "2019-03-14 10:09:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessInstanceDTOFactoryDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "eada466947730ed4d6b82a38996004723d29c7a2", "commit_訊息": "Q00-20190222001 調整IMG快速簽核畫面的上一關資訊過濾通知與服務關卡", "提交日期": "2019-03-13 19:34:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f749538ece277e7de60c60723d3261c87889fd1f", "commit_訊息": "S00-20171031001 補更新多語系", "提交日期": "2019-03-13 17:45:32", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a4fdc20026f09f2a5e04b11dd76f6d2d42551905", "commit_訊息": "C01-20181220005 修正通知信夾帶附件若檔名超過10字會亂碼", "提交日期": "2019-03-13 11:46:26", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d7b60b9b31a9d0a8364e05cebeeb04dd445e2abe", "commit_訊息": "C01-20190311006 修正因為prepare Statement沒關導致cursor超過限制", "提交日期": "2019-03-13 11:30:36", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPkgCategoryListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5e8ff57acc3dcca0f2c228fc077998c16c018b82", "commit_訊息": "Q00-20190312005 調整IMG首頁統計元件智能示警多語系", "提交日期": "2019-03-12 19:46:47", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "53a4f2e2bebe2bd88dd7757a0f77edf7a84a2082", "commit_訊息": "Q00-20190307002 鼎捷移動整合頁面的統記元件設定中，新增\"智能快簽\"選項。", "提交日期": "2019-03-12 19:42:51", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8975ed97bd0c40e4929b3f5b009dc16420698c63", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-03-12 17:45:25", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "d648bf90d6274f7f7829207f8676a0d4f5f0b3ce", "commit_訊息": "Q00-20190312004 修正C01-20190102002修改後Oracle會報錯", "提交日期": "2019-03-12 17:39:34", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7e71dbcc5b84bb5e175caf7b127a0d04d73a35de", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-03-12 17:10:21", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "070836659fce5bd60e8f48a4d2bb5c5fdef703ed", "commit_訊息": "Q00-20190225004 詳情表單中顯示流程頁面內的簡易流程圖中的任何連結都不應該有任何動作", "提交日期": "2019-03-12 17:08:08", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileBpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1d60ec379d82eebbcd235fcd6767784804266757", "commit_訊息": "Q00-20190312003 修正IMG簽核歷程在代處理時人員名稱的圓圈會跑版", "提交日期": "2019-03-12 17:05:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5339d41c50ba7616ec55ae2135e297df435cb0cf", "commit_訊息": "Q00-20190221002 修正IMG推播如果表單設定詳情簽核時，直連表單畫面一片空白", "提交日期": "2019-03-12 16:39:43", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "96d0a4ef9c0aa2e368a5fe409450fbc07af983dd", "commit_訊息": "Q00-20190311003 調整移動端的JSP移掉未使用的js", "提交日期": "2019-03-12 16:04:03", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "ebfc95a515441fab4d14dc7775966bef868a8a71", "commit_訊息": "Q00-20190222002 修正統計元件設定再新增第四個類型時會無法新增，並提示\"統計組件類型不能重複\"的問題", "提交日期": "2019-03-12 11:14:49", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "15162461926eb61772bdea58714caeaec22d89c9", "commit_訊息": "Q00-20190225003 增加IMG撥電話的直連畫面標題的多語系", "提交日期": "2019-03-12 11:12:11", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "eba06b61450d5100ea608158a7d8d49659377f89", "commit_訊息": "Q00-20190225003 調整IMG撥電話的直連畫面內容符合多語系", "提交日期": "2019-03-12 11:10:05", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "398b5649a290489074167e406dbc0eecc4d6849e", "commit_訊息": "Q00-20190311001 將行動版\"我的關注\"統計筆數調整成與PC一致", "提交日期": "2019-03-12 10:15:52", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4adfbdd7675341d2e5fd84daf86cff05bbe4b972", "commit_訊息": "調整快速簽核中加入重要流程會重複問題", "提交日期": "2019-03-11 18:47:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1d2e85a06516097233ce695f849c4f1caf8646ce", "commit_訊息": "Q00-20190225006 調整行動版樣版中的初始化Dropdown與Listbox元件方法", "提交日期": "2019-03-11 15:50:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "90ecfa47eb3db75565bbfcdb5f69b3fb6dcc2c1a", "commit_訊息": "C01-20190305004 修正IOS日期元件顯示時間後，在中間層會多顯示秒數且日期跟時間中間會多一個T", "提交日期": "2019-03-11 13:58:04", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0e147ec17bec39b9f855898724d6f2da25fc3ef4", "commit_訊息": "Q00-20190221004 調整App<57>Grid明細欄位與接收資料欄位數對不上時顯示空值", "提交日期": "2019-03-11 11:47:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "dd9bf8cce7c3c559b58fef23b8f59f62e0853760", "commit_訊息": "調整IMG詳情表單中可作加入重要流程功能", "提交日期": "2019-03-11 11:41:16", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ded2427e4b7f37ef8a795e73312f8efc81e5d653", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-03-11 10:14:35", "作者": "jerry1218", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "7c85f8945a4a06e01d3f7e428031399d9191ee17", "commit_訊息": "調整5751 Oracle update SQL錯誤", "提交日期": "2019-03-11 10:13:11", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0850e741eeecd040d5c22b4edc6297a4d7c5fa7a", "commit_訊息": "Q00-20190308001 補上調整的多語系", "提交日期": "2019-03-11 10:05:55", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "309be3230ebece6fe7ebd95341cbf3c38f96f569", "commit_訊息": "Q00-20190308001 修正IMG智能示警的處理記錄在英文語系時彈出視窗會跑版", "提交日期": "2019-03-11 10:02:58", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8783cabe7e407a8ac0c783491ab33e4122bd708", "commit_訊息": "Q00-20190308002 修正IMG智能示警的處理紀錄頁面無法完整顯示內容", "提交日期": "2019-03-11 09:57:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8cd834d7b959550195e78d628def18030a26b31a", "commit_訊息": "Q00-20190308003 智能示警的處理紀錄資訊排序方式改為降冪", "提交日期": "2019-03-11 09:51:11", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7e49ff12945d9bde1f3d3fbbdd1c046e7927d4dd", "commit_訊息": "A00-20190215005 修正千分位顯示異常問題", "提交日期": "2019-03-11 09:37:41", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "12cee2ca30636ed3ad940fa84fc037c6e4abedbe", "commit_訊息": "新增IMG詳情表單中可作加入重要流程功能", "提交日期": "2019-03-08 19:06:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "92b9cf91aefcd278b995b70ed33dcb90b4dd5db6", "commit_訊息": "加上 (e) 變成 catch (e) {}", "提交日期": "2019-03-08 16:59:03", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1383e8aced75fa34c286db682b4e7a1b8cb814bc", "commit_訊息": "C01-20180807001 更新WfERP form-default", "提交日期": "2019-03-08 15:44:22", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/BOM \\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI04).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/BOM\\347\\224\\250\\351\\207\\217\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI02).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/E-BOM\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI12).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/PACKING LIST \\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI06).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/PACKING LIST\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDLI43).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/SI\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IPSI04).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/WAFER BANK\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDLI11).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/WAFER \\350\\253\\213\\350\\263\\274\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDLI15).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\344\\273\\230\\346\\254\\276\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACPI03).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\345\\273\\240\\345\\244\\226\\345\\212\\240\\345\\267\\245\\345\\207\\272\\345\\273\\240\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI17).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\345\\273\\240\\345\\244\\226\\345\\212\\240\\345\\267\\245\\345\\223\\201\\351\\201\\213\\345\\233\\236\\351\\200\\262\\345\\273\\240\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI18).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\346\\251\\237\\345\\231\\250\\350\\250\\255\\345\\202\\231\\351\\200\\262\\345\\207\\272\\345\\217\\243\\347\\225\\260\\345\\213\\225\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCHI14).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\347\\225\\260\\345\\213\\225\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCHI08).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\347\\225\\260\\345\\213\\225\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI05).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\350\\262\\250\\345\\223\\201\\345\\207\\272\\345\\273\\240\\344\\277\\256\\347\\220\\206\\346\\252\\242\\346\\270\\254\\346\\210\\226\\346\\240\\270\\346\\250\\243\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI15).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\344\\277\\241\\347\\224\\250\\347\\213\\200\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI11).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\205\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI19).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\205\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI11)[GP25(PR)].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\205\\245\\346\\255\\270\\351\\202\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI20).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\205\\245\\346\\255\\270\\351\\202\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI12)[GP25(PR)].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\205\\266\\344\\273\\226\\345\\207\\272\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI13).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\207\\272\\345\\217\\243\\350\\262\\273\\347\\224\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI10).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\207\\272\\345\\273\\240\\346\\224\\276\\350\\241\\214\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCHI09).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\207\\272\\345\\273\\240\\346\\224\\276\\350\\241\\214\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI12).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI05).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI62).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\212\\240\\345\\267\\245\\346\\240\\270\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(MOCI10).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI19).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI58).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI20).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI59).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\223\\201\\350\\231\\237\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI24).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\240\\261\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI05).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\244\\232\\345\\270\\263\\346\\234\\254\\346\\234\\203\\350\\250\\210\\345\\202\\263\\347\\245\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI62).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\247\\224\\345\\244\\226\\345\\267\\245\\345\\226\\256\\351\\226\\213\\347\\253\\213(IDLI33).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\256\\242\\346\\210\\266\\350\\250\\202\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI06).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\256\\242\\346\\210\\266\\350\\263\\207\\346\\226\\231\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI15).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\345\\272\\253\\345\\255\\230\\347\\225\\260\\345\\213\\225\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI05)[GPSD260030].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\346\\207\\211\\344\\273\\230\\346\\206\\221\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACPI02).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\346\\213\\206\\350\\247\\243\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI06).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\346\\216\\241\\350\\263\\274\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI07)[GP25(PR)].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI08)[GP25(PR)].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\346\\224\\266\\346\\254\\276\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACRI03).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\346\\225\\231\\350\\202\\262\\350\\250\\223\\347\\267\\264\\347\\224\\263\\350\\253\\213\\345\\240\\261\\345\\220\\215\\344\\275\\234\\346\\245\\255(HRSI34).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\346\\226\\260\\345\\256\\242\\346\\210\\266\\347\\224\\263\\350\\253\\213\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI21).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\346\\234\\203\\350\\250\\210\\345\\202\\263\\347\\245\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI10).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\346\\240\\270\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI03).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\346\\264\\276\\350\\273\\212\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI14).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\347\\265\\204\\345\\220\\210\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI05).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\347\\265\\220\\345\\270\\263\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACRI02).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI07).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\250\\223\\347\\267\\264\\347\\224\\263\\350\\253\\213\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(HRSI23).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\251\\242\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI14)[GP25(PR)].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\252\\277\\346\\225\\264\\346\\262\\226\\351\\212\\267\\345\\210\\206\\351\\214\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(FCSI04).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\253\\213\\350\\263\\274\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI05)[GP25(PR)].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\253\\213\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI16)[GP25(PR)].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\262\\250\\351\\201\\213\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI07).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\345\\240\\261\\345\\273\\242\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI08).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\345\\244\\226\\351\\200\\201\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI13).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\212\\230\\350\\210\\212\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI11).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(ASTI24).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\216\\241\\350\\263\\274\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI22).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\224\\266\\345\\233\\236\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI14).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\224\\271\\350\\211\\257\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI06).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\270\\233\\346\\220\\215\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI25).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\347\\247\\273\\350\\275\\211\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI12).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\350\\251\\242\\345\\203\\271\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI20).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\350\\252\\277\\346\\225\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI10).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\350\\253\\213\\350\\263\\274\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI19).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI02).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\351\\200\\262\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI23).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\351\\207\\215\\344\\274\\260\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI07).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\350\\275\\211\\346\\222\\245\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI08)[GP25(PR)].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\351\\200\\200\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI11)[GP25(PR)].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\351\\200\\262\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI09)[GP25(PR)].form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\351\\212\\267\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI08).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\351\\212\\267\\351\\200\\200\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI09).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\351\\240\\220\\347\\256\\227\\346\\214\\252\\347\\224\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI23).form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\\351\\240\\220\\347\\256\\227\\350\\277\\275\\345\\212\\240\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI22).form\"", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 80}, {"commit_hash": "00150372056338e09e1536f6d43b5b941dcdc871", "commit_訊息": "修正ISO文件變更單選擇文件沒有ISOType時會報錯", "提交日期": "2019-03-08 15:10:47", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/DocCmItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb125cad95834eb2799548be8f3c4c2eb730b331", "commit_訊息": "1.移除安裝憑證連結 2.微調設計工具多語系", "提交日期": "2019-03-08 15:06:19", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "d7e8b87bc8edf1628235f8d71767a9cf5d49d8e0", "commit_訊息": "補上因Git Merge異常遺失的多語系", "提交日期": "2019-03-08 14:22:10", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "98a7944ecc8f5c38ba29e3a8a2136009bf81242c", "commit_訊息": "新增管理員移動消息訂閱管理頁面", "提交日期": "2019-03-08 14:19:25", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeResult.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "a1fed2558672e2c725b7c3dc25f563d42cd66231", "commit_訊息": "微調build的說明語句", "提交日期": "2019-03-08 14:17:20", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/build.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f33031b140b357e6bdd8ebeeda60c12e6866f77b", "commit_訊息": "調整管理員維護行動流程消息訂閱的功能", "提交日期": "2019-03-08 14:16:22", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "983350a341596fa53e7ae4d14307af7e0a2de4f3", "commit_訊息": "新增管理員維護行動流程消息訂閱的功能", "提交日期": "2019-03-08 14:15:25", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAllProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "4a5e57cac0c2742bb91774329244a1eff77a0ae4", "commit_訊息": "C01-20190128001 內部主機現有.form壓縮檔 放入出貨光碟only", "提交日期": "2019-03-08 12:51:24", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form-default-t100.zip", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form_\\346\\226\\274\\345\\205\\247\\351\\203\\250\\344\\270\\273\\346\\251\\237\\344\\270\\213\\350\\274\\211\\346\\234\\200\\346\\226\\260\\347\\211\\210.txt\"", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "64cae4a036941948e78c5904e072ac14ee610bd9", "commit_訊息": "<V57>S00-20180908001 修正 :流程預先解析人員 如果是離職加上符號註記,但當連續是兩個服務關卡時會有異常", "提交日期": "2019-03-08 09:42:10", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "94d6071cd2f641e02553a8329310749a55d77e4e", "commit_訊息": "IMG的快速簽核增加智能示警歷史處理記錄畫面", "提交日期": "2019-03-07 20:32:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "8c47063ebf442f7361e61e2e5a77c1109996c8ce", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-03-07 20:16:06", "作者": "劉建德", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "4bbcb68a39a54720c3b32a35754e057c576c1f63", "commit_訊息": "新增加入重要流程多語系", "提交日期": "2019-03-07 20:14:34", "作者": "劉建德", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "87c2acfb8029074313bce9320b78ed3b119fc79e", "commit_訊息": "新增加入重要流程 RESTful 服務API", "提交日期": "2019-03-07 20:12:52", "作者": "劉建德", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "972b8aaac81c883cb12cea90883e9e8bdd2574dc", "commit_訊息": "新增加入重要流程 RESTful 服務API", "提交日期": "2019-03-07 20:12:52", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "65decd1b5e86c55506300d7ad0b75a740d417c36", "commit_訊息": "新增管理員移動消息訂閱管理頁面", "提交日期": "2019-03-07 20:06:14", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeResult.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "e3d374f3676e131a9017e4ee3ac079047380be7d", "commit_訊息": "微調build的說明語句", "提交日期": "2019-03-07 17:34:08", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/build.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "75915685760179fe7c12eaab2dae20c458cb8903", "commit_訊息": "調整管理員維護行動流程消息訂閱的功能", "提交日期": "2019-03-07 17:13:34", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "34eb3aa778920b48e801833145222784529fef68", "commit_訊息": "新增管理員維護行動流程消息訂閱的功能", "提交日期": "2019-03-07 15:17:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAllProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "9137488e6c1333b92e029d47f39ba48f0bdf741d", "commit_訊息": "使用者可以在流程簽核時把流程加入重要流程", "提交日期": "2019-03-07 15:08:35", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "5fd36a78bdbd17c106a438dd06ac63b1798c09fb", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-03-07 10:34:42", "作者": "wayne<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "905cd24f60012a0aebb771ce4a836c2733dc7f5f", "commit_訊息": "Q00-20190307003 調整樹狀開窗時，須將父視窗的資料一併帶回至開窗頁面", "提交日期": "2019-03-07 10:34:15", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e5701021d1f8e203d9837a53cb021369746a9afe", "commit_訊息": "調整IMG統計元件\"我的關注\"筆數計算邏輯", "提交日期": "2019-03-07 10:19:41", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8e38744e12995e8f0438622655441cbe4705ae3e", "commit_訊息": "調整IMG可發起流程可用字段接口", "提交日期": "2019-03-07 09:29:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "01e8862f7f74fd6ce05c631b399b16cf647efce2", "commit_訊息": "新增IMG智能示警歷史處理記錄畫面", "提交日期": "2019-03-06 18:26:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileLibrary.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "a9757115829b2e9fa4443696731a757b9ea9d9e4", "commit_訊息": "調整行動版\"我的關注\"多語系", "提交日期": "2019-03-06 17:42:04", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4d05e9473f9203cfc1458e71d69b91ab63edbd9f", "commit_訊息": "新增行動版\"我的關注\"功能", "提交日期": "2019-03-06 17:10:50", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "d2c9bcabb137137e08c6d20fa44a7563bbb8623d", "commit_訊息": "C01-20190214003 第三次修正", "提交日期": "2019-03-06 16:46:38", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a8b0ef45b09350b24d5e3bb65d4bf5ddc78f37e5", "commit_訊息": "[在移動App上,可以從待辦中把流程加入重要流程] Ajax服務及RESTful服務", "提交日期": "2019-03-06 15:12:08", "作者": "BPM", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "4b7113a246610ba4bad7726dfe8e0011050dab9b", "commit_訊息": "註解上個記錄中，關於行動版\"我的關注\"功能的程式碼", "提交日期": "2019-03-06 10:35:41", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f07a8b3cb1f5adf69d63c5946389a830b0ce5d8e", "commit_訊息": "調整IMG重要流程可用字段接口", "提交日期": "2019-03-06 10:18:48", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDataset.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDetailNew.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldLabel.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "950cf0af46dcae77ce0bdca8ddf5753cdfd29038", "commit_訊息": "調整IMG所有可發起流程增加流程名稱蒐尋", "提交日期": "2019-03-06 09:45:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3decbe80fd3eccc0b75861454b7dab3bf27b1548", "commit_訊息": "C01-20190214003 補修正", "提交日期": "2019-03-06 09:28:22", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a9ba40591eeb9730691c9a4c691d3072a2b28b65", "commit_訊息": "新增取最近含有示警訊息的工作清單功能", "提交日期": "2019-03-05 16:32:44", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessInstanceDTOFactoryDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/CriticalProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "c3e023102cb3b024632a376140f9c533af494654", "commit_訊息": "Jerry Merge : 新增設定檔開啟流程設計器 , 其他語系選項", "提交日期": "2019-03-05 14:52:13", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@designer/NaNaTools.properties", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 1}, {"commit_hash": "51e287008af0dc7f5a6f5e16f147000adf0f43f2", "commit_訊息": "Jerry Merge : 修正設計器應用程式多開(3個以上)失敗問題", "提交日期": "2019-03-05 14:21:40", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/NaNaSimple/bpmToolEntrySimple.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/DesignerMainApp.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/ChooseDesigner.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/LoginCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/LoginDesigner.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/NaNaSimple/bpmToolEntrySimple.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/NaNaSimple/bpmToolEntrySimple.jar", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "cb53fcfcbf776ec643dbc555201b36a18824795f", "commit_訊息": "C01-20190225001 修正企業微信在直接進入表單畫面時會因使用者尚未登入而導致畫面異常問題", "提交日期": "2019-03-05 10:25:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "74e2ba14a4aa0cc2e8572ba5c05c7dcdaa58e8d0", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-02-27 18:25:56", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "fa516dd05e3158fd44c5b97288cb181110a052f9", "commit_訊息": "刪除無意義檔案only", "提交日期": "2019-02-27 18:24:48", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "\"3.Implementation/subproject/bpm-designer/\\345\\267\\262\\347\\237\\245\\345\\225\\217\\351\\241\\214.doc\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"3.Implementation/subproject/process-designer/\\345\\267\\262\\347\\237\\245\\345\\225\\217\\351\\241\\214.doc\"", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 2}, {"commit_hash": "c11075ff97ca2327002c8ac7646cbbde7a7cafba", "commit_訊息": "A00-20190111001 修正使用WorkflowService.invokeProcess發起流程內含附件 清單上不會有附件的迴紋針", "提交日期": "2019-02-27 18:13:44", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2164d7240a2d39273120392eeb122e05f1e1235a", "commit_訊息": "A00-20190226002 doResize導致錯誤修正", "提交日期": "2019-02-27 18:02:25", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aa123cddc9308d805e2cafbfadf8780e8aa8efdc", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-02-27 16:53:20", "作者": "jerry1218", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "eb9bb68e1a8564d609ae90197dee7089a910db68", "commit_訊息": "<PERSON> Merge : 修正launch4j錯誤", "提交日期": "2019-02-27 16:51:45", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/.gitignore", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/.gitignore", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/bin/COPYING", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/bin/ld.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/bin/windres.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/.gitignore", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/bin/COPYING", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/bin/ld.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/bin/windres.exe", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 9}, {"commit_hash": "102d2880be52a66f52a27f325a2ca65eb52e49ec", "commit_訊息": "S00-20171031001 修正上筆修改後 Script沒有成功更新", "提交日期": "2019-02-27 15:20:28", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63d22baa7eef5d52f9356133721d5ce7e7edf05e", "commit_訊息": "<PERSON> - 取消build efgp-pdfViewer", "提交日期": "2019-02-27 14:21:05", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/properties.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "54edf3a723693612adc287a92c380858b3c74b13", "commit_訊息": "<PERSON> : OpenJDK merge", "提交日期": "2019-02-27 10:55:51", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/properties.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/jar-in-jar-loader.zip", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/NaNaSimple/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/ProcessDesignerApp.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/action/OpenProcessPackageAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ActionManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/CMManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/DesignerSecurityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessViewController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/SecurityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/jgoext/view/ProcessViewListener.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BaseEditor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramEditor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/JDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/main/DesignerIFrame.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/main/DesignerMainFrame.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/popmenu/ObjectPopupMenu.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/popmenu/ProcessViewPopupMenu.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/AccessRightController.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/AccessRightController_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/AccessRightController_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/AccessRightController_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/AccessRightController_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate_en_US.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate_vi_VN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate_zh_CN.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate_zh_TW.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/.classpath", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/.project", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/.classpath", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/.project", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/.settings/org.eclipse.core.resources.prefs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/.settings/org.eclipse.core.runtime.prefs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/.settings/org.eclipse.jdt.core.prefs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/Launch4j.url", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/build.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/ConsoleApp.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/ConsoleApp.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/build.bat", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/build.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/l4j/ConsoleApp.ico", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/lib/readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/src/net/sf/launch4j/example/ConsoleApp.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ExitCodeApp/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ExitCodeApp/build.bat", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ExitCodeApp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ExitCodeApp/src/net/sf/launch4j/example/ExitCodeApp.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/SimpleApp.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/SimpleApp.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/build.bat", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/build.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/l4j/SimpleApp.ico", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/l4j/SimpleApp.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/l4j/splash.bmp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/lib/readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/src/net/sf/launch4j/example/SimpleApp.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head/consolehead.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head/guihead.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head/head.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_jni_BETA/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_jni_BETA/head.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_jni_BETA/jniconsolehead.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_jni_BETA/jniguihead.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_jni_BETA/jnihead.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/consolehead/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/consolehead/Makefile.win", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/consolehead/consolehead.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/consolehead/consolehead.dev", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/guihead/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/guihead/Makefile.win", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/guihead/guihead.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/guihead/guihead.dev", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/guihead/guihead.h", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/head.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/head.h", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniconsolehead_BETA/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniconsolehead_BETA/Makefile.win", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniconsolehead_BETA/jniconsolehead.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniconsolehead_BETA/jniconsolehead.dev", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniguihead_BETA/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniguihead_BETA/Makefile.win", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniguihead_BETA/jniguihead.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniguihead_BETA/jniguihead.dev", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniguihead_BETA/jniguihead.h", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jnihead.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jnihead.h", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/resource.h", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/launch4j.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/launch4j.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/launch4j.jfpr", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/launch4jc.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/JGoodies.Forms.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/JGoodies.Looks.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/Nuvola.Icon.Theme.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/XStream.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/ant.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/ant.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/commons-beanutils.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/commons-logging.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/commons.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/formsrt.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/foxtrot.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/foxtrot.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/jgoodies-common.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/jgoodies-forms.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/jgoodies-looks.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/xstream.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/manifest/uac.exe.manifest", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/.classpath", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/.project", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/.settings/org.eclipse.m2e.core.prefs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/assemble-dist.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/assemble-linux.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/assemble-linux64.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/assemble-mac.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/assemble-win32.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/src.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/maven-readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/sign4j/README.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/sign4j/sign4j.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/sign4j/sign4j.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/asterix-o.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/asterix.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/build.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/button_ok.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/cancel16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/down16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/edit_add16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/info.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/new.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/new16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/ok16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/open.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/open16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/run.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/save.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/up16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/launch4j.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/Builder.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/BuilderException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ExecException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/FileChooserFilter.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/KanjiEscapeOutputStream.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/Log.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/Main.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/RcBuilder.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/Util.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/AntClassPath.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/AntConfig.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/AntJre.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/Launch4jTask.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/StringWrapper.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/Binding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/BindingException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/Bindings.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/IValidatable.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/InvariantViolationException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JComboBoxBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JListBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JRadioButtonBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JTextAreaBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JTextComponentBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JToggleButtonBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/OptComponentBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/OptJTextAreaBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/Validator.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/CharsetID.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/ClassPath.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Config.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/ConfigPersister.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/ConfigPersisterException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Describable.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Jre.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/JreVersion.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/LanguageID.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/LdDefaults.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Msg.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/SingleInstance.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Splash.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/VersionInfo.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/BasicForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/BasicForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/ClassPathForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/ClassPathForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/ConfigForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/ConfigForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/EnvironmentVarsForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/EnvironmentVarsForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/HeaderForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/HeaderForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/JreForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/JreForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/MessagesForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/MessagesForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/SingleInstanceForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/SingleInstanceForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/SplashForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/SplashForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/VersionInfoForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/VersionInfoForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/AbstractAcceptListener.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/BasicFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/BrowseActionListener.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/ClassPathFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/ConfigFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/EnvironmentVarsFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/FileChooser.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/GlassPane.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/HeaderFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/JreFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/MainFrame.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/MessagesFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/SingleInstanceFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/SplashFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/VersionInfoFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/uninst.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/MinGW.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/crt2.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libadvapi32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libgcc.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libkernel32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libmingw32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libmsvcrt.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libshell32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libuser32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/MinGW.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/crt2.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libadvapi32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libgcc.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libkernel32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libmingw32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libmingwex.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libmoldname.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libmsvcrt.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libshell32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libuser32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/web/bullet.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/web/changelog.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/web/docs.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/web/index.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/web/launch4j-use.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/web/launch4j.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/web/links.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/Launch4j/web/style.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build.bat", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/cp.bat", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/jar-in-jar-loader.zip", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Axis/activation.jar", "修改狀態": "重新命名", "狀態代碼": "R080"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Axis/axis.jar", "修改狀態": "重新命名", "狀態代碼": "R093"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Axis/commons-discovery.jar", "修改狀態": "重新命名", "狀態代碼": "R083"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Axis/jaxrpc.jar", "修改狀態": "重新命名", "狀態代碼": "R075"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Axis/saaj.jar", "修改狀態": "重新命名", "狀態代碼": "R076"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Axis/wsdl4j.jar", "修改狀態": "重新命名", "狀態代碼": "R086"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/BrowserLauncher/BrowserLauncher.jar", "修改狀態": "重新命名", "狀態代碼": "R084"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Bsf/bsf.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Bsf/bsh.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Bsf/js.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Bsf/jython.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Dom4J/dom4j-1.6.1-changed_serialization.jar", "修改狀態": "重新命名", "狀態代碼": "R082"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Forms/forms-1.1.0.jar", "修改狀態": "重新命名", "狀態代碼": "R064"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JCalendar/jcalendar-1.3.2.jar", "修改狀態": "重新命名", "狀態代碼": "R087"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JCalendar/jcalendar.jar", "修改狀態": "重新命名", "狀態代碼": "R089"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JCalendar/kunststoff.jar", "修改狀態": "重新命名", "狀態代碼": "R084"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JDiagram/JDiagram-4.1.4.jar", "修改狀態": "重新命名", "狀態代碼": "R091"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JGo/JGo.jar", "修改狀態": "重新命名", "狀態代碼": "R093"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JGo/JGoLayout.jar", "修改狀態": "重新命名", "狀態代碼": "R082"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/JGo/JGoSVG.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/icons.zip", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/optional/Filters.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/optional/MultipleGradientPaint.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/optional/swing-layout.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/optional/swing-worker.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/swingx-0.9.1.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Jag/jag.jar", "修改狀態": "重新命名", "狀態代碼": "R093"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-beanutils.jar", "修改狀態": "重新命名", "狀態代碼": "R086"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-collections.jar", "修改狀態": "重新命名", "狀態代碼": "R087"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-lang.jar", "修改狀態": "重新命名", "狀態代碼": "R089"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-logging.jar", "修改狀態": "重新命名", "狀態代碼": "R084"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/JakartaOJB/antlr-2.7.6.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JakartaOJB/db-ojb.jar", "修改狀態": "重新命名", "狀態代碼": "R066"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Jython/jython.jar", "修改狀態": "重新命名", "狀態代碼": "R093"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Log4J/log4j.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/LookAndFeel/looks-2.1.4.jar", "修改狀態": "重新命名", "狀態代碼": "R063"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/LookAndFeel/panel-skin.jar", "修改狀態": "重新命名", "狀態代碼": "R094"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Mail/activation.jar", "修改狀態": "重新命名", "狀態代碼": "R083"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Mail/mail.jar", "修改狀態": "重新命名", "狀態代碼": "R072"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Msv/isorelax.jar", "修改狀態": "重新命名", "狀態代碼": "R093"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Msv/msv.jar", "修改狀態": "重新命名", "狀態代碼": "R094"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Msv/relaxngDatatype.jar", "修改狀態": "重新命名", "狀態代碼": "R079"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Msv/xmlgen.jar", "修改狀態": "重新命名", "狀態代碼": "R090"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Msv/xsdlib.jar", "修改狀態": "重新命名", "狀態代碼": "R094"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Tyrex/tyrex.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/XStream/xpp3_min.jar", "修改狀態": "重新命名", "狀態代碼": "R082"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/XStream/xstream.jar", "修改狀態": "重新命名", "狀態代碼": "R058"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Xerces/resolver.jar", "修改狀態": "重新命名", "狀態代碼": "R073"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Xalan/serializer.jar", "修改狀態": "重新命名", "狀態代碼": "R087"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Xerces/xercesImpl.jar", "修改狀態": "重新命名", "狀態代碼": "R087"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Xerces/xml-apis.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/DesignerMainApp.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/ChooseDesigner.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/LoginCache.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/DesignerChooseController.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/DesignerChooseDialog.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginController.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp_en_US.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp_zh_CN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp_zh_TW.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_en_US.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_zh_CN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_zh_TW.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_en_US.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_zh_CN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_zh_TW.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/tool-icon.ico", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SystemConfigManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/SystemConfigManagerClientDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_side_util/RemoteCallConnection.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/remote_call/Administrator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/src/com/dsc/nana/user_interface/apps/crmcfg/controller/CrmCfgMainFrameController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/control/AbstractDesignerSystemManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/extend_swing/AbstractDesignerDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/model/ToolEntryLoginTemp.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/subdesigner/AbsSubDesignerController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/view/dialog/DesignerLoginController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/view/dialog/DesignerLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/DesignerLoginDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/DesignerLoginDialog_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/DesignerLoginDialog_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/DesignerLoginDialog_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/efgp-pdfViewer/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-designer/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-designer/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/FormDesigner.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/ServerAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/SystemController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-importer/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-importer/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/jar-in-jar-loader.zip", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/NaNa/conf/NaNaLog.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/NaNaSimple/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/OrgDesigner.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/ToolAction.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/mainframe/MainMenuBar.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/mainframe/OrgMainFrame.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/com/dsc/nana/user_interface/apps/org_importer/control/ImporterMainController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/persistence/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/persistence/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/.classpath", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/.project", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/.settings/org.eclipse.core.resources.prefs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/.settings/org.eclipse.core.runtime.prefs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/.settings/org.eclipse.jdt.core.prefs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/Launch4j.url", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/build.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/ConsoleApp.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/ConsoleApp.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/build.bat", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/build.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/l4j/ConsoleApp.ico", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/lib/readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/src/net/sf/launch4j/example/ConsoleApp.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ExitCodeApp/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ExitCodeApp/build.bat", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ExitCodeApp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/ExitCodeApp/src/net/sf/launch4j/example/ExitCodeApp.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/SimpleApp.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/SimpleApp.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/build.bat", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/build.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/l4j/SimpleApp.ico", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/l4j/SimpleApp.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/l4j/splash.bmp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/lib/readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/src/net/sf/launch4j/example/SimpleApp.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/demo/readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head/consolehead.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head/guihead.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head/head.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_jni_BETA/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_jni_BETA/head.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_jni_BETA/jniconsolehead.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_jni_BETA/jniguihead.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_jni_BETA/jnihead.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/consolehead/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/consolehead/Makefile.win", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/consolehead/consolehead.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/consolehead/consolehead.dev", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/guihead/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/guihead/Makefile.win", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/guihead/guihead.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/guihead/guihead.dev", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/guihead/guihead.h", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/head.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/head.h", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jniconsolehead_BETA/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jniconsolehead_BETA/Makefile.win", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jniconsolehead_BETA/jniconsolehead.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jniconsolehead_BETA/jniconsolehead.dev", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jniguihead_BETA/.gitignore", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jniguihead_BETA/Makefile.win", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jniguihead_BETA/jniguihead.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jniguihead_BETA/jniguihead.dev", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jniguihead_BETA/jniguihead.h", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jnihead.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/jnihead.h", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/head_src/resource.h", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/launch4j.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/launch4j.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/launch4j.jfpr", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/launch4jc.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/JGoodies.Forms.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/JGoodies.Looks.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/Nuvola.Icon.Theme.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/XStream.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/ant.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/ant.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/commons-beanutils.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/commons-logging.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/commons.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/formsrt.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/foxtrot.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/foxtrot.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/jgoodies-common.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/jgoodies-forms.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/jgoodies-looks.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/lib/xstream.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/manifest/uac.exe.manifest", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/.classpath", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/.project", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/.settings/org.eclipse.m2e.core.prefs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/assembly/assemble-dist.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/assembly/assemble-linux.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/assembly/assemble-linux64.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/assembly/assemble-mac.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/assembly/assemble-win32.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/assembly/src.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/maven-readme.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/maven/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/sign4j/README.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/sign4j/sign4j.c", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/sign4j/sign4j.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/asterix-o.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/asterix.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/build.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/button_ok.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/cancel16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/down16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/edit_add16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/info.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/new.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/new16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/ok16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/open.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/open16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/run.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/save.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/images/up16.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/launch4j.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/Builder.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/BuilderException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ExecException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/FileChooserFilter.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/KanjiEscapeOutputStream.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/Log.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/Main.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/RcBuilder.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/Util.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/AntClassPath.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/AntConfig.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/AntJre.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/Launch4jTask.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/StringWrapper.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/Binding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/BindingException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/Bindings.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/IValidatable.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/InvariantViolationException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JComboBoxBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JListBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JRadioButtonBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JTextAreaBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JTextComponentBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JToggleButtonBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/OptComponentBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/OptJTextAreaBinding.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/Validator.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/CharsetID.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/ClassPath.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Config.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/ConfigPersister.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/ConfigPersisterException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Describable.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Jre.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/JreVersion.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/LanguageID.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/LdDefaults.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Msg.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/SingleInstance.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Splash.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/VersionInfo.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/BasicForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/BasicForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/ClassPathForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/ClassPathForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/ConfigForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/ConfigForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/EnvironmentVarsForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/EnvironmentVarsForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/HeaderForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/HeaderForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/JreForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/JreForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/MessagesForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/MessagesForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/SingleInstanceForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/SingleInstanceForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/SplashForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/SplashForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/VersionInfoForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/VersionInfoForm.jfrm", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/AbstractAcceptListener.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/BasicFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/BrowseActionListener.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/ClassPathFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/ConfigFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/EnvironmentVarsFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/FileChooser.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/GlassPane.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/HeaderFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/JreFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/MainFrame.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/Messages.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/MessagesFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/SingleInstanceFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/SplashFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/VersionInfoFormImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/messages.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/messages_es.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/uninst.exe", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api/MinGW.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api/crt2.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api/libadvapi32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api/libgcc.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api/libkernel32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api/libmingw32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api/libmsvcrt.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api/libshell32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api/libuser32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/MinGW.LICENSE.txt", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/crt2.o", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libadvapi32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libgcc.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libkernel32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libmingw32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libmingwex.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libmoldname.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libmsvcrt.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libshell32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libuser32.a", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/web/bullet.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/web/changelog.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/web/docs.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/web/index.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/web/launch4j-use.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/web/launch4j.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/web/links.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/Launch4j/web/style.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/build-exe.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/jar-in-jar-loader.zip", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/NaNaSimple/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/ADMApp.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/AdmSignOnManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/main/ADMMainFrame.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/ProcessDesignerApp.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/CMManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/DesignerController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/DesignerSecurityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/SecurityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/main/DesignerIFrame.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/main/DesignerMainFrame.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/tool-icon.ico", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/src/com/dsc/nana/user_interface/apps/authority/controller/SysAuthorityController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/com/dsc/nana/user_interface/apps/rsrcbundle/controller/RsrcBundleMainController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/com/dsc/nana/user_interface/apps/syscfg/controller/SysCfgMainFrameController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/.gitignore", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/build.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DesignerDownloadAction.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-designerDownload-config.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DesignerDownload/DesignerDownloadMain.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DesignerDownload/java-1.8.0-openjdk-1.8.0.201-1.b09.ojdkbuild.windows.x86_64.msi", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/ToolSuite.jsp", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-awt-util.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-dom.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-svg-dom.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-svggen.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-util.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-xml.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-core.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-extra.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-pro-intl.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-pro.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-viewer.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/ICEpdf/levigo-jbig2-imageio.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/J2EE/j2ee.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JIntellitype/JIntellitype.dll", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JIntellitype/JIntellitype64.dll", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JIntellitype/jintellitype-1.3.9.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JTaskpane/swingx.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-codec.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-httpclient.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-io.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-pool.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Jaxen/jaxen.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Log4J/log4j.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Sdo/sdo2_1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Trinity/TrinityServiceEJB.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Xalan/xalan.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/lib/Xerces/xml-apis.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@java/jdk1.8.0_151/bin/jconsole.exe", "修改狀態": "重新命名", "狀態代碼": "R058"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@java/jdk1.8.0_151/jre/lib/security/US_export_policy.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@java/jdk1.8.0_151/jre/lib/security/local_policy.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/standalone.conf", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/standalone.conf.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_1.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_1.sql", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 898}, {"commit_hash": "507fcb8c779ece8e4673a2957a5a71885c524c8a", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-02-27 10:39:57", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "099bb0511bab99e4948771d3f1a1e5bb030effe5", "commit_訊息": "新增img草稿清單二期接口", "提交日期": "2019-02-27 10:39:27", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/AdvancedSearch.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "efadf3df1fb8969356470a52e3cb9221618e7d0d", "commit_訊息": "A00-20190215002 修正T100送簽單據後，關卡解析失敗回傳失敗的XML，但流程仍然產生", "提交日期": "2019-02-26 14:26:57", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/AbstractNewTiptopMethod.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "73a37d292e2caa908150a60f332b156385674e07", "commit_訊息": "A00-20190219002 修正T100發起RWD表單有SubTab元件時會報錯-增加其他整合的例外修正", "提交日期": "2019-02-26 14:12:07", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0605b17824c44e9da1050cad3d675fb97ad8d04b", "commit_訊息": "隱藏IMG追蹤流程快速簽核進階按鈕", "提交日期": "2019-02-26 13:49:11", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f0523d7adf62267540cca890499fcd75ad0c5487", "commit_訊息": "Q00-20190226001 修正表單設計中間層標記多欄位時，最右邊標記的欄位在IMG中間層上不會顯示", "提交日期": "2019-02-26 13:47:44", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9706d4197d5b86e7fa9b95c23a1881b2d6d18f3a", "commit_訊息": "A00-20190219002 修正T100發起RWD表單有SubTab元件時會報錯", "提交日期": "2019-02-25 17:25:48", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "409af830a1840f32da0cabb0f817a626fc34cf6b", "commit_訊息": "<V57>Q00-20190223001 修正 :文件制作索引失敗,因缺少引用的jar檔", "提交日期": "2019-02-25 15:19:32", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8889900667da3c3f91df9a265578afa58567432d", "commit_訊息": "S00-20190213001 增加QRCode簽核時，參考系統參數設定驗證時間", "提交日期": "2019-02-25 14:15:18", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/QRCodeLoginCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 10}, {"commit_hash": "ee66ebc8b64f78b7df008d7c7130b13642a8b386", "commit_訊息": "修正鼎捷移動快速簽核同意按鈕點擊沒反應的問題", "提交日期": "2019-02-22 17:21:29", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88138af26e0a35ba39f875a7e8a6da9f783e2cfc", "commit_訊息": "新增預測關卡、上一關卡撥電話的進階按鈕與提示訊息", "提交日期": "2019-02-22 17:02:13", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "a3db69915a4513c55c541acc3629da0fb019eb26", "commit_訊息": "修正恢復訂閱管理如果清單中有\"只支援行動簽核的流程\"流程名稱會顯示為空", "提交日期": "2019-02-22 17:01:17", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7515948ca8cc7afa0c02ed1cb60a53b39d395ced", "commit_訊息": "C01-20190123004 ISO文件一覽表的ISO文件階層無法顯示", "提交日期": "2019-02-22 16:09:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/ISODocLevel.hbm.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/ISODocType.hbm.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/isoModule/DocForReportViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "bde12de6ab3c17bc698644be688545355f5bf678", "commit_訊息": "新增BPM撥號畫面", "提交日期": "2019-02-22 10:15:54", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/phonecall.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "063c8f950f5f0f883cc258c1fe3fb3727a07d1f8", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-02-22 09:52:11", "作者": "BPM", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "62fe16dabd636265335e38b1316a4817d4da8fe6", "commit_訊息": "修正過濾中間層按鈕的錯誤", "提交日期": "2019-02-22 09:50:11", "作者": "BPM", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "784053e608c9fc11bd41e5a2bbe6b6c4453ba207", "commit_訊息": "調整IMG查看附件方式 --改使用鼎捷移動提供的開窗方式預覽附件檔案", "提交日期": "2019-02-22 09:16:51", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "ccf0986f2bf11b1f9b753f3a96f36a0ff9c6e147", "commit_訊息": "修正鼎捷移動快速簽核預計關卡聯絡人Label沒有多語系問題", "提交日期": "2019-02-21 17:47:48", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "442e59dda887266f1a5a111593f52e0149618a5d", "commit_訊息": "調整最常處理流程的功能", "提交日期": "2019-02-21 17:20:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "ba1943174176295878719590b130602c12d7aa42", "commit_訊息": "補上表單取消訂閱畫面的多語系", "提交日期": "2019-02-21 16:52:02", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9c5198ee03f27357118f7ea913a05548ab178d8e", "commit_訊息": "調整表單取消訂閱畫面的樣式", "提交日期": "2019-02-21 16:48:24", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "b841bfe739c33a345d00c32801d3664f208f5110", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-02-21 11:21:39", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "72637c097599259210cde9acecd5dfd284bf4045", "commit_訊息": "C01-20190214003 修正在 追蹤流程=>已轉派的工作 中，查看表單資料右上方沒有\"顯示流程\"的按鈕", "提交日期": "2019-02-21 11:05:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8f4e70d6a470d2ddb065fe762afb0a660e8715c9", "commit_訊息": "新增行動流程恢復訂閱管理頁面", "提交日期": "2019-02-21 09:42:48", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribe.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "ddc2f7349bace9398a00308b5f22479bb03a3f3a", "commit_訊息": "C01-20190218003 修正產品開窗預設值過濾組織名稱沒效果的問題", "提交日期": "2019-02-20 18:15:55", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ccafc43b6913731595ba8c8c1ceb91995e9c9645", "commit_訊息": "二次修正 <V57>C01-20190213001 調整:移除多表單自動儲存功能，該功能不符合應用場景，且後端容易發生物件修改時報出Cannotlock的錯誤", "提交日期": "2019-02-20 15:24:57", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "af29d5473bc94942b03edc271cfa853232b03dd7", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-02-20 15:14:53", "作者": "施翔耀", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "3193517c50ad2ea690da5dc80943c99bf52cbefa", "commit_訊息": "<V57>A00-20180725001 修正:ESS流程只能加簽通知關卡，但修改模式下確可以選到會辦", "提交日期": "2019-02-20 15:14:27", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ee90b4e4f0742e74a330b321080c5b39779a179e", "commit_訊息": "調整最常處理流程的功能 1.調整篩選API增加處理工作欄位 2.常處理流程增加優先處理工作欄位 3.判斷用戶是否為部門主管功能改用public 4.增加常處理流程使用的多語系", "提交日期": "2019-02-20 14:54:51", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "7e7dc90aa322532de0d52ff13d6bdf59e7e99696", "commit_訊息": "<V57>C01-20190213001  調整 :移除多表單自動儲存功能 ，該功能不符合應用場景，且後端容易發生物件修改時報出 CannotLock的錯誤", "提交日期": "2019-02-20 14:39:32", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "60d98b84c12711e9b5d720f006abfe5d8102c430", "commit_訊息": "調整最常處理流程的功能", "提交日期": "2019-02-19 18:44:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbstractPageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListResultsTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "4808fde9b20b9fa8445d27035885c33323e09c9f", "commit_訊息": "補上鼎捷移動快速簽核預計關卡資訊的多語系", "提交日期": "2019-02-19 11:16:20", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "e12470f5c2e6345f260a655aab183281ab6c2d71", "commit_訊息": "將01/18註解的功能還原(遺漏部分)", "提交日期": "2019-02-19 10:03:44", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0aed4fb0c87fe939f9ebea4fca777654ece98fac", "commit_訊息": "將01/19註解的功能還原(遺漏部分)", "提交日期": "2019-02-19 09:58:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "edce2208962cf117eb7f41aa47fa86835f87d51c", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-02-18 19:17:23", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "da807c77d0badb4d43f104dbac4d1b75090386d9", "commit_訊息": "A00-20190128001 修正TextArea設定高度與必填 高度會失效", "提交日期": "2019-02-18 18:17:13", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "768d58a8c031f94d3a88556e4c5cc3f28000c41a", "commit_訊息": "用戶可以透過智能快簽的簽核歷程撥打電話給關卡處理人,中間層可以透過參數控制不撈出表單資料", "提交日期": "2019-02-18 17:00:32", "作者": "BPM", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "006d38654be1175c305e1d70ad5250a2f362f07b", "commit_訊息": "A00-20190129004 修正TextBox的唯讀，只要設定顯示小數點後N位數就會發生  顏色無法正確顯示", "提交日期": "2019-02-15 15:01:46", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0191e9b4df0031d3f50768c3dcc2c38b192ec80f", "commit_訊息": "Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-02-14 10:45:49", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "b3117aaf5fbf7c1077ee74221a8c8812d892c8c7", "commit_訊息": "將01/19註解的功能還原 1.IMG待辦詳情表單畫面轉由他人處理功能 2.IMG待辦詳情表單畫面簽核歷程上一關卡與當前關卡功能", "提交日期": "2019-02-14 09:52:44", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "43caba79c483ea235061efaccca6d14b4762fc77", "commit_訊息": "補修正C01-20190107001 C01-20190109005", "提交日期": "2019-02-13 18:38:14", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3ae6fc6f301854bba491bbcf0b51095e0020ce8c", "commit_訊息": "新增IMG行動表單上取消訂閱推播功能", "提交日期": "2019-02-13 15:58:49", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmWorkItemDataVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "61148c935d72d9128a4338faff232050d7d78bab", "commit_訊息": "將01/18註解的功能還原", "提交日期": "2019-02-13 15:47:28", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWinUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "6d6e9c1ce50f1783e4b46f4fada0c3444360b237", "commit_訊息": "新增IMG推播消息取消訂閱功能", "提交日期": "2019-02-13 14:00:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileMessageSubscription.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileMessageSubscriptionDTO.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_SQLServer.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 16}, {"commit_hash": "c3bdca3b74b37c4ac4125686a8d428937a6afa6c", "commit_訊息": "將01/19註解的功能還原", "提交日期": "2019-02-13 10:36:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "27187d54b9a208a5ddec49aceb8130cfcf016619", "commit_訊息": "C01-20190212001 修正附件管理按鈕在IPhone XR,IPhone XS上跑版問題", "提交日期": "2019-02-12 14:24:38", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad857442f176485f167a3dc10ef620983614b8c9", "commit_訊息": "更新ESS表單 --HR同仁協助調整表單內容 --將單身明細的欄位名稱補上與表單上相同的欄位名稱", "提交日期": "2019-02-12 14:15:32", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF05\\345\\212\\240\\347\\217\\255\\350\\250\\210\\345\\212\\203\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF17\\351\\212\\267\\345\\201\\207\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF20\\345\\207\\272\\345\\267\\256\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF21\\345\\207\\272\\345\\267\\256\\347\\231\\273\\350\\250\\230.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF24\\350\\252\\277\\350\\226\\252\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF28\\344\\272\\272\\345\\212\\233\\351\\234\\200\\346\\261\\202\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF31\\346\\213\\233\\350\\201\\230\\350\\250\\210\\347\\225\\253.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF32\\346\\207\\211\\350\\201\\230\\344\\272\\272\\345\\223\\241\\351\\235\\242\\350\\251\\246.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF33\\346\\207\\211\\350\\201\\230\\344\\272\\272\\345\\223\\241\\347\\255\\206\\350\\251\\246.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF50\\347\\217\\255\\346\\254\\241\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF51\\345\\212\\240\\347\\217\\255\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213(\\345\\244\\232\\346\\231\\202\\346\\256\\265\\345\\244\\232\\344\\272\\272).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF60\\350\\254\\233\\345\\270\\253\\350\\263\\207\\346\\240\\274\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF62\\345\\237\\271\\350\\250\\223\\351\\240\\220\\347\\256\\227\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF63\\345\\237\\271\\350\\250\\223\\351\\234\\200\\346\\261\\202\\346\\216\\241\\351\\233\\206.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF64\\345\\237\\271\\350\\250\\223\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF66\\345\\237\\271\\350\\250\\223\\350\\251\\225\\344\\274\\260.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF74\\350\\263\\207\\346\\272\\220\\347\\224\\263\\351\\240\\230.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF75\\350\\263\\207\\346\\272\\220\\346\\255\\270\\351\\202\\204.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF04B\\345\\212\\240\\347\\217\\255\\347\\224\\263\\350\\253\\213(\\346\\211\\271\\351\\207\\217).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF23B\\350\\252\\277\\350\\201\\267\\347\\224\\263\\350\\253\\213(\\346\\211\\271\\351\\207\\217).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF26B\\347\\215\\216\\346\\207\\262\\347\\224\\263\\350\\253\\213(\\346\\211\\271\\351\\207\\217).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF30\\350\\243\\234\\345\\210\\267\\345\\215\\241\\347\\224\\263\\350\\253\\213(\\346\\211\\271\\351\\207\\217).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF71\\350\\253\\213\\345\\201\\207\\347\\224\\263\\350\\253\\213(\\346\\211\\271\\351\\207\\217).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF77\\350\\226\\252\\350\\263\\207\\347\\265\\220\\346\\236\\234\\345\\257\\251\\346\\240\\270.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF80_\\345\\212\\240\\347\\217\\255\\350\\250\\210\\345\\212\\203\\346\\230\\216\\347\\264\\260\\346\\222\\244\\351\\212\\267.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF52C2\\347\\217\\255\\346\\254\\241\\350\\256\\212\\346\\233\\264.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF52\\346\\212\\225\\347\\217\\255\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF53\\346\\216\\222\\347\\217\\255\\347\\242\\272\\350\\252\\215.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 28}, {"commit_hash": "cc840d8f7135e2c2c06111aa42bd48edbb2d275b", "commit_訊息": "C01-20190107002 補上如果是自動簽核 列印時 狀態已處理之後面沒有(自動)兩字", "提交日期": "2019-01-30 18:36:46", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "741a2ccdc4ade65bec8442a84386bf3efb919490", "commit_訊息": "C01-20190123004 修正關卡退回重辦後，後續派送關卡新增為兩個代辦", "提交日期": "2019-01-30 09:32:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05a260da6ad1065018a4846d609fd7018366ac35", "commit_訊息": "C01-20190107001 修正Grid資料過多會蓋到簽核意見的問題", "提交日期": "2019-01-29 19:00:19", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}]}