{"company_id": "06100414", "company_name": "建保精密", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06100414_建保精密\\01客戶基本資料", "files": [{"filename": "[建保]連線資訊.txt", "raw_content": "FortiClient (之後都是用鼎新連線精靈，但怕以後會用到，先保留)\r\nServer : **************\r\nPort : 10443\r\nID : Kenpol\r\nPassword : Kenpol~123456\r\n\r\nEFGP正式機AP、DB及EFGP測試機AP、DB連線資訊如下:\r\n用途\r\nEFGP KPTW-EF ************\r\nadministrator / admin#DSC\r\nweb password:efgp99admin\r\nEFGP_TEST KPTW-TS ************\r\nadministrator /admin#DSC    Kp@20180921#\r\n\r\nEFGP站台，測試機: administrator/ efgp99admin\r\n\r\n\r\nMSSQL帳密 sa/sql#DSC\r\n\r\nTipTop\r\n正式區: http://************/web/ws/r/aws_efsrv?WSDL\r\n\r\n測試區: http://************/web/ws/r/aws_efsrv_toptest?WSDL\r\n\r\n\r\n10/16更新\r\nHR\r\nKPTW-HR ************ administrator/Kp@20180921#\r\n\r\nEFGP\r\nKPTW-EF\r\nESS \r\nM-Cloud     ************ administrator/Kp@20180921#\r\n\r\nEFGP_TEST\r\nKPTW-TS    ************ administrator/Kp@20180921#\r\n\r\n\r\n\r\n", "structured_data": {"host": "**************", "port": "10443", "id": "<PERSON><PERSON>", "password": "Kenpol~123456", "web password": "efgp99admin", "efgp站台，測試機": "administrator/ efgp99admin", "正式區": "http://************/web/ws/r/aws_efsrv?WSDL", "測試區": "http://************/web/ws/r/aws_efsrv_toptest?WSDL"}, "source_path": "C1.客戶維護相關\\06100414_建保精密\\01客戶基本資料\\[建保]連線資訊.txt", "file_size": 766, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:31.995562"}], "total_files": 1, "processed_at": "2025-08-26T10:46:31.995571"}