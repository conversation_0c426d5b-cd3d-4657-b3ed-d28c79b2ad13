{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.8.3_20230504", "date": "2023-03-27 17:07:58", "message": "[Web]Q00-20230327002 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱[補]", "author": "wayne<PERSON>"}, "舊分支": {"branch_name": "release_5.8.8.3", "date": "2022-07-21 16:48:19", "message": "[ESS]Q00-20220721001 修正ESS儲存草稿後並沒有成功新增到草稿上[補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 11:07:53", "新增commit數量": 204, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "230b8e4d39624a4d6aa5fc7879e1cb4e34017895", "commit_訊息": "[Web]Q00-20230327002 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱[補]", "提交日期": "2023-03-27 17:07:58", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "acd514f06405687d89419a564cae69e050bda8f5", "commit_訊息": "[Web]Q00-20230327002 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱", "提交日期": "2023-03-27 13:54:51", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/ProgramDefinitionDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "fee04e8d826b1a1a9c3a69410342d3c0afc55a9b", "commit_訊息": "[Web] Q00-20230425003 修正上傳附件表單，表單異常，新增防呆", "提交日期": "2023-04-25 17:48:06", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7b00b34ebb1e8f53cd4deb169be97562b5fbc179", "commit_訊息": "[Web]Q00-20230414006 修正新增片與內容帶有反斜線會造成片語頁面異常 [補修正]", "提交日期": "2023-04-18 11:51:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/PhraseManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/taglib/WriteTag.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "1c8fad8a437438a4fb088f443a79d875ca81adc9", "commit_訊息": "[Web]Q00-20230414006 修正新增片與內容帶有反斜線會造成片語頁面異常", "提交日期": "2023-04-14 16:45:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76f619e0c43f52249bfd0d1c0d3276cbad52aa8c", "commit_訊息": "[Web]Q00-20230324002 優化上傳附件功能，防止重複點擊上傳按鈕", "提交日期": "2023-03-24 17:08:21", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "02d897bc37179c402bb3d27e9d3525f9e1ccd308", "commit_訊息": "[Web]Q00-20230116001 上傳附件功能，優化使用者提示", "提交日期": "2023-01-16 11:01:34", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "363521b82f027468f430e13d2c661493867db5f4", "commit_訊息": "[Web]Q00-20221006004 上傳附件功能，優化使用者提示，且上傳過程不可點擊關閉按鈕。", "提交日期": "2022-10-25 15:25:10", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed775c159980ab6699d3cd2a9a52a7110270de5d", "commit_訊息": "[WEB]Q00-20221101002 修正絕對定位表單SerialNumber元件CSS取到RWD設定", "提交日期": "2022-11-01 12:00:20", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "12ebce8abc6319ee57f5104adb37fcda203fde6b", "commit_訊息": "[TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]", "提交日期": "2023-03-15 10:29:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "97f22ac0637b89e72d4fb3216b446f298945a7ce", "commit_訊息": "[TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]", "提交日期": "2023-03-15 10:28:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9ba8c40b5eae616ac8f7561ade3d2fc4934e4621", "commit_訊息": "[TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]", "提交日期": "2023-03-14 16:31:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0d7be9f44093b700aec35e70ca96589a381fe341", "commit_訊息": "[Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題[補]", "提交日期": "2023-02-08 16:46:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1d9c79716d10aa72a1d734049ab6bc66cd023310", "commit_訊息": "[Web]A00-20230204001 修正追蹤流程中，查看已轉派工作且已處理的單據，點擊回到工作清單卻呈現工作通知清單的問題", "提交日期": "2023-02-07 13:15:34", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a9a1686a1cf9436c421dba3793a7e3a4acaba49b", "commit_訊息": "[Web]Q00-20230130002 修正待辦清單有設定流程條件，使用者簽核完跳至下一個流程會找到非條件的流程", "提交日期": "2023-01-30 17:44:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d817c10c2243f472fae3120b7d073cc7971dc5b3", "commit_訊息": "[Web]Q00-*********** 修正一般使用者簽核 T100 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊", "提交日期": "2022-11-18 14:25:40", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysNewTiptopToolDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopToolBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "5edcfafe9ab7d5c6c89174b178f7a82b975df259", "commit_訊息": "[Web]Q00-***********修正員工工作轉派的轉派意見若只有輸入單一個反斜線，則使用者的待辦事項無法呈現的問題", "提交日期": "2022-10-12 13:49:55", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6b24f55cb3bfaf227eeacd8b97d4b44009470b93", "commit_訊息": "[流程引擎]A00-*********** 此單號先前調整邏輯廢除，改為調整判斷附件權限為共用方法，可讓前後端共同呼叫使用", "提交日期": "2022-09-16 15:28:43", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDTOFactoryDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "c669b8796f9105faf4b7bfbe97576254c11f3fad", "commit_訊息": "[流程引擎]A00-*********** 調整寄信時依照「附件權限設定的套用範圍為人員」時，判斷是否需要夾帶附件，在其餘範圍情境下則不主動夾帶附件", "提交日期": "2022-09-14 17:52:35", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e2a38317cc2f3f450dae30120f5ecd2c876bab4f", "commit_訊息": "Revert \"[流程引擎]A00-*********** 此單號先前調整邏輯廢除，改為調整判斷附件權限為共用方法，可讓前後端共同呼叫使用\"", "提交日期": "2023-03-15 09:51:01", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDTOFactoryDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "0d7939118d0b8b42daad5b9f479815c0f012f4b8", "commit_訊息": "[流程引擎]A00-*********** 此單號先前調整邏輯廢除，改為調整判斷附件權限為共用方法，可讓前後端共同呼叫使用", "提交日期": "2022-09-16 15:28:43", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDTOFactoryDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "515f8ec84dbccb3acca2b3c5ff90aad2d905992a", "commit_訊息": "[Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題[補修正]", "提交日期": "2023-01-30 15:11:00", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e8e77c47c849d82660ef9f6ec15eb5386d67a7c6", "commit_訊息": "[Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題", "提交日期": "2023-01-18 09:06:30", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "166d22c7c53c8d4ab0401cc9e74292e7a64cdbd7", "commit_訊息": "[WEB]S00-20220315002-在追蹤流程以及監控流程下，結案的流程會按照不同的結案結果呈現。", "提交日期": "2022-12-28 18:04:44", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbd0adcf26adb3d5d9e588d2f5491da7f8cfd9ff", "commit_訊息": "Revert \"[Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題[補修正]\"", "提交日期": "2023-03-15 09:41:05", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "a5a905b914b1e3dd46d4d94207c3bbc80a21aaa6", "commit_訊息": "[Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題[補修正]", "提交日期": "2023-01-30 15:11:00", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2555ed1a88c446b18af7e231fee6d9de6d3e6af5", "commit_訊息": "[Web] Q00-20230313002 修正SelectElement，Style屬性異常問題", "提交日期": "2023-03-13 15:37:27", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "06c4cf2d89b0c1c0a4386a8a095f210a0f13e08a", "commit_訊息": "[WEB]Q00-20221128006調整絕對定位表單RadioButton顏色更清楚。", "提交日期": "2022-11-28 17:03:57", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-form-component.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f6c27356fd7fea40fec6ec1b104fef45e756e6bb", "commit_訊息": "[BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息", "提交日期": "2023-03-09 16:38:22", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cdd25c05617cc5f80483c341c2f931ce8f622dfd", "commit_訊息": "[Web] Q00-20230308002 修正設定小數點後幾位功能，提示文字應加在外顯元件上", "提交日期": "2023-03-08 12:01:14", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a1c55fad6d24a5b7c55d070950ba2609c9a46638", "commit_訊息": "[表單設計師]Q00-20221226001 修正 TextBox 元件，若資料型態設定為浮點數且小數點後幾位，會導致沒有對齊的問題", "提交日期": "2022-12-26 10:17:44", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "93980633e69c18f56c152830d2cabdb38c492413", "commit_訊息": "[Web]Q00-20230222004 修正 TextBox 元件的進階設定，若設定小數點後幾位且保存方式為實際值，實際值會完全顯示的問題", "提交日期": "2023-03-02 15:06:41", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5916a367b2b312e193e47e3556646959d6554f4f", "commit_訊息": "[WEB] V00-20230214004 修正設置浮點數無法過濾特殊字元符號問題", "提交日期": "2023-02-15 10:44:12", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c3ec2703426b3981b77240445ff83f78772aa4c6", "commit_訊息": "[Web]Q00-20230112004 修正 TextBox 元件的進階設定，若設定資料型態為浮點數且顯示千分位會導致顯示值異常的問題", "提交日期": "2023-01-12 17:49:56", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "53b93aba72417d10dd1eb1360bc75812171a7b36", "commit_訊息": "[WEB] Q00-20230207002 調整絕對定位表單設置必填，隱藏標籤提示異常問題", "提交日期": "2023-02-14 11:40:10", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "434c49895271f0a37a7198a12e9b03a7252419e6", "commit_訊息": "[WEB]A00-20230110001 修正時間元件驗證異常錯誤。", "提交日期": "2023-01-10 18:11:21", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e28002bd935728177e6afc5a83849ed958a65e59", "commit_訊息": "[Web]Q00-20221108001修正輸入元件設置必填後，沒勾選隱藏標籤原label標籤會出現undefined", "提交日期": "2022-11-08 15:25:39", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "80346a634c78d182ce04a3d4fe2df9dcc751d06f", "commit_訊息": "[Web]Q00-20221020004 修正 TextBox 元件進階功能的運算規則，若將已綁定的欄位值輸入後又刪除，會顯示 NaN 的問題", "提交日期": "2022-10-26 09:06:05", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "38837ebc8609a0b329276fa5467a22ad9ec30505", "commit_訊息": "[Web]S00-20220711001Textbox元件設置整數及浮點數自動進位輸入值。", "提交日期": "2022-10-25 15:06:58", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "579f9e2c48edb7b0a9e296368fbd8f4e119066fc", "commit_訊息": "[Web]A00-20220811001 修正表單若TextBox元件設定浮點數且顯示實際值時會有偏移值問題", "提交日期": "2022-08-16 14:49:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "650452749f2e43f49bf020b0d825fd4e178d3528", "commit_訊息": "[表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持\"-\"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(修)", "提交日期": "2022-10-11 09:20:02", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a2bd094ce69d8727c192e128b59478301180d6e3", "commit_訊息": "[表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持\"-\"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)", "提交日期": "2022-09-21 10:40:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc184ccfe8fdbb8532672b1f6331561810a33bf0", "commit_訊息": "[表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持\"-\"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。", "提交日期": "2022-09-20 16:57:50", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "af5fd84d627003b28a2f0cc850947543a460e0ea", "commit_訊息": "[表單設計師]S00-20220707005系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷。", "提交日期": "2022-09-16 14:46:47", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8fc6b27a5c1cdd99704319c4d132cfebe80af8cc", "commit_訊息": "[Web]Q00-20220808003修正使用產品表單中的Date元件，並搭配TextBox元件的進階功能，資料型態整數中的時間區間運算，當遇到元件ID有使用下底線時，會導致TextBox元件無法正常運算", "提交日期": "2022-08-08 17:43:54", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9c5bab52386baa3142038ac120fc304593fa9d2e", "commit_訊息": "[Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現 [補修正]", "提交日期": "2022-12-19 14:35:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d56ea686752d6960b4dc54b314bc117fcafcb8c", "commit_訊息": "[Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現", "提交日期": "2022-12-15 10:31:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "93a8ff9f4119acbe3aa1f0d30ff2a856af3dce4c", "commit_訊息": "[TIPTOP]Q00-20230223002 修正拋單附件為一個以上時，cleanDocument接口無法刪除TIPTOP附件暫存檔", "提交日期": "2023-02-23 14:45:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "****************************************", "commit_訊息": "Revert \"[TIPTOP]Q00-20230223002 修正拋單附件為一個以上時，cleanDocument接口無法刪除TIPTOP附件暫存檔\"", "提交日期": "2023-02-23 16:48:13", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d1107ab1641c25fa8010140f9ae00606569c68ab", "commit_訊息": "[TIPTOP]Q00-20230223002 修正拋單附件為一個以上時，cleanDocument接口無法刪除TIPTOP附件暫存檔", "提交日期": "2023-02-23 14:45:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e34289dc03cc62b04c2540c0914ea15c58b821c6", "commit_訊息": "[DT]C01-20221201005 優化Web化資料使用權限管理頁面開啟緩慢問題", "提交日期": "2022-12-20 16:47:16", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "01dac4837e4bb53c0b591fea7526874fe9ef90f4", "commit_訊息": "[系統管理工具]A00-*********** 若管理員將有組織設計師權限的人員離職，並且移除組織及部門，會導致使用權限設定沒有畫面的問題", "提交日期": "2022-10-27 18:59:08", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WizardAuthorityManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/WizardAuthorityManagerClientDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/OrgWizardAuthorityScopeController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/toolauth/OrgAuthConfPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "a8842efe181f821eb41a762ebea127ab7561a812", "commit_訊息": "[組織同步] Q00-*********** 修正HRM助手更新User資料時，沒有取系統變數(補修正)", "提交日期": "2023-02-23 10:18:01", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "647e2bd43d4364b8638192678b21462f232fd7fd", "commit_訊息": "[流程引擎]Q00-20230222002 修正核決關卡設定與流程關卡處理者相同時自動簽核，且流程有兩個以上的核決關卡時，只有核決關卡展開的第一關有自動簽核，後續關卡皆未自動簽核", "提交日期": "2023-02-22 15:49:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "788ffd638941695091fe0d3e42ca0db3354431e1", "commit_訊息": "[組織同步] Q00-*********** 修正HRM助手更新User資料時，沒有取系統變數", "提交日期": "2023-02-21 15:53:39", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ded874971f41103ea7ffcb1a2342faf5044a0221", "commit_訊息": "[Web]Q00-*********** 修正缺席紀錄資料過多，造成檢視簽核歷程時間過久", "提交日期": "2023-01-30 16:04:15", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "ac9f45779b774a0973adf937f91e1f8439a0aeb3", "commit_訊息": "[流程引擎]Q00-20221020003 修正核決關卡參考自定義關卡，且自定義關卡沒有掛載任何表單時，導致流程無法往下繼續派送", "提交日期": "2023-02-15 10:59:36", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5eb9b0e87505fb542aee838c9ec79334cba0c50f", "commit_訊息": "[WEB] A00-20230207001 調整水平線樣式變更時的邏輯，改為修改樣式後複製一個再刪除舊的", "提交日期": "2023-02-14 16:34:58", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d36d81aa7155c9999e5319a41c81354f7d65491b", "commit_訊息": "[流程引擎]Q00-20221221003 調整如果流程前兩關發起時間相同，會造成自動簽核為流程中有相同簽核者(不含發起者)就會沒有作用 [補修正]", "提交日期": "2022-12-26 11:59:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "43ee777969eb02a945817a802d8f6356f1ebf835", "commit_訊息": "[流程引擎]Q00-20221221003 調整如果流程前兩關發起時間相同，會造成自動簽核為流程中有相同簽核者(不含發起者)就會沒有作用", "提交日期": "2022-12-21 17:40:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6fd57296f31bed108a510653ca154cb31211a2a3", "commit_訊息": "[Web]Q00-20230110005 修正左側功能列點擊模擬使用者會重新刷新左側功能列", "提交日期": "2023-01-10 17:26:18", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "efbeb78eab85cf3f43aae8a3725632cff183d144", "commit_訊息": "[表單設計師]A00-20230131001 修正表單設計師的 Date 元件，若在基本設定中的輸入框有設定預設值，儲存後再次簽出表單，該 Date 元件於畫面呈現時，其顯示值會有不正確的問題", "提交日期": "2023-02-01 16:57:59", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6fb5f98ac81d6687da5dd8990e4879f10269f7c7", "commit_訊息": "[Web]Q00-20230113003 修正簽核意見內容有輸入換行，但通知信的簽核意見內容卻沒有換行的問題", "提交日期": "2023-01-13 18:04:07", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a4a2b36da9d25d7d1b941d05979f3d775cba33a2", "commit_訊息": "[流程引擎]Q00-20221121001 修正流程寄信內容是整張表單，且表單元件為浮點數且為空的狀況會派送失敗", "提交日期": "2022-11-21 18:02:16", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "288a086acd9525ada081f671ba0ab595b9310514", "commit_訊息": "[WEB] A00-20230111001 調整系統排程機制，不須重啟BPM即生效新排程設定。", "提交日期": "2023-01-13 16:18:00", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SystemScheduleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "86d70e81f845cf0406e8a34090cd002ee195795a", "commit_訊息": "[WEB]Q00-20221013001修正簽核意見沒有換行符號。", "提交日期": "2022-10-13 08:37:30", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/StringUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4813dbfe8a7764329ea01b5cb93beabd074724ac", "commit_訊息": "[表單設計師]Q00-20230113002 隱藏 SubTab 元件右鍵的菜單，已確認 SubTab 元件與其頁籤在最初 5.7.2.1 版就不支援複製的功能", "提交日期": "2023-01-13 14:27:37", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1f4ae06ee0b33afbee176a834000691d55606529", "commit_訊息": "[WEB]A00-20221014001修正簽核意見斷行顯示。", "提交日期": "2022-10-17 08:52:43", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f47a29b17ab46eaf5f63035224bfaab853891ab9", "commit_訊息": "[WorkFlow]Q00-20230112003 修正取簽核歷程未帶入FormId就導致歷程無法取得", "提交日期": "2023-01-12 17:26:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBWFRequestRecordDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7d6807addc74416c2e085bf342cf2cf944dfd183", "commit_訊息": "[Web]A00-20230106001 修正 DialogInputLabel 元件，若在流程設計師中的表單存取控管設定權限為 Disable，於表單畫面中欄位還可修改的問題", "提交日期": "2023-01-07 16:04:43", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d55610cfe1ecdc65a10d6d2f2de7ccd4fec71840", "commit_訊息": "[TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]", "提交日期": "2023-01-06 10:44:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e76877e14e62f6f37758c3c5a25cdae6256cba89", "commit_訊息": "[Web]Q00-20221213004 在Users資料被移除造成流程實例開啟異常的狀況下，優化異常訊息", "提交日期": "2022-12-13 11:45:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "511a4ffca9839c8a029eec29b976f06bf5b497c3", "commit_訊息": "[BPM APP]C01-20220826004 調整企業微信同步時獲取部門成員與子部門列表的接口為新接口", "提交日期": "2022-10-14 17:02:04", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/wechat/MobileWeChatService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "6fbd7bd6935f652eb35d3ed63f34d9c71dc05dc7", "commit_訊息": "[WorkFlow]Q00-20230105002 修正回傳WorkFlow處理者沒有被更新成功 [補修正]", "提交日期": "2023-01-07 11:18:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0a8e4e4f494bd333c9f02414b9883361e4496bd9", "commit_訊息": "[Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作[補]", "提交日期": "2022-07-28 14:39:59", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "92571abeeb45af7869acdcfa02d1c6014f5a20fd", "commit_訊息": "[Web]Q00-20221102001 修正checkbox設計時，若有勾選「最後一個選項額外產生輸入框」，表單中checkbox呈現與列印不一致的問題", "提交日期": "2022-11-02 14:53:14", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b572e3b0233adc22196698133c58441506de8acd", "commit_訊息": "[Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作。", "提交日期": "2022-07-27 12:07:05", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4c32c248c943cc9a34aa1367c053bf3ac44c5bd3", "commit_訊息": "[Web]Q00-20220826002修正模組定義若底下無程式定義，不會顯示在模組程式維護頁面的問題[補]", "提交日期": "2022-08-29 10:42:15", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "de5d57403ff03a345a2359b093001615680d85b0", "commit_訊息": "[Web]Q00-20220826002修正模組定義若底下無程式定義，不會顯示在模組程式維護頁面的問題", "提交日期": "2022-08-26 18:47:21", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a4e67f90ec904086ea77a1e32d2f8da81dfb0690", "commit_訊息": "[WEB]Q00-20221213001 Update與Delete模組程式維護後，才需一併更新NavigatorMenu。", "提交日期": "2022-12-13 10:23:46", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f7c588e9af820d69cf269de5c11b70db18e86ed8", "commit_訊息": "[Web]Q00-20220825002 調整模組程式維護作業加入系統語系供使用者設定", "提交日期": "2022-09-07 13:48:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/ProgramViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageModule-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "e60349e50493ebeded7d9d944d71f3b5e937d65e", "commit_訊息": "Revert \"[WEB]Q00-20221213001 Update與Delete模組程式維護後，才需一併更新NavigatorMenu。\"", "提交日期": "2023-01-06 10:30:37", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d8ceb8e520b29c7011bcd358181421efcac940c9", "commit_訊息": "[WEB]Q00-20221213001 Update與Delete模組程式維護後，才需一併更新NavigatorMenu。", "提交日期": "2022-12-13 10:23:46", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "81ba1c307e5cec434313930286e39c8a4f9ec9a8", "commit_訊息": "[表單設計師]Q00-20230105004 修正 RWD 表單設計師若選取文本時，右側 scrollbar 沒有突顯匹配選定的文本", "提交日期": "2023-01-05 21:07:45", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormScriptEditor.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f87ead54cb688d46eef342efa5c11448fae613e2", "commit_訊息": "[WorkFlow]Q00-20230105002 修正回傳WorkFlow處理者沒有被更新成功", "提交日期": "2023-01-05 16:04:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad65ebba50b7a1dc566fc09b3fbffa7b9108ea1e", "commit_訊息": "[DT]Q00-20230104005 修正Web化系統管理工具資料使用權限管理撈表單分類時因為prepare Statement沒關導致cursor超過限制", "提交日期": "2023-01-04 17:47:10", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormCategoryListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e17f0dca8a1631e3742efb112f2bb4caf107c532", "commit_訊息": "[TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯", "提交日期": "2023-01-04 17:38:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "79364a27ab7f175db27f6dc59e5b54430ed408c7", "commit_訊息": "[WEB]Q00-20221223001 流程資料查詢頁面無法下載附件", "提交日期": "2022-12-23 10:28:42", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dd23fbab9f537128a7eedd43baa13e93e51086dd", "commit_訊息": "[Web]Q00-20221124001 調整使用BPM外部URL連結跳轉畫面時表單內的表單名稱以多語系顯示", "提交日期": "2022-11-24 13:35:19", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "52464e31834f889bcaf74e1eda4308d42c33f7b4", "commit_訊息": "[流程引擎]Q00-20230104003 修正寄送Mail在沒有CC收件人情況下不應副本給收件人的問題", "提交日期": "2023-01-04 11:29:46", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3705864ca10fd1f4f5151b490cba9dc1a42d612c", "commit_訊息": "[Web]Q00-20230103002 修正系統設定使用 LDAP 驗證，若使用者沒有設定 LDAP 驗證，從通知信連結進入 BPM 登入頁時，帳號欄位沒有自動帶入 UserId 的問題", "提交日期": "2023-01-03 18:17:06", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65a079b637b6e4639c45a1b45e49ffbc70f82cf3", "commit_訊息": "[Web]Q00-20220906004 修正系統管理員登入BPM不應該發雙因素模組的驗證信", "提交日期": "2022-09-07 08:38:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d08ec6ac12bf0005fa590ef7c8cb4f6a45219220", "commit_訊息": "[BPM APP]C01-20221226004 修正絕對位置表單且行動版絕對位置在轉RWD表單時報錯的問題", "提交日期": "2022-12-29 20:43:36", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/AbsoluteFormBluePrint.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0f8d344909f8033dd1af9a35e48ea586adbdcf06", "commit_訊息": "[DT]C01-20221214005 修正Web化系統管理工具的系統郵件在Oracle環境下不存在帳號時會頁面異常問題", "提交日期": "2022-12-14 16:40:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SystemConfigMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c2918d91a985850cede8435f3b5af861eda45a3c", "commit_訊息": "[DT]C01-20221115006 修正Web化系統管理工具流程主機設定在編輯儲存後導致其他使用者登入BPM後顯示空白畫面問題", "提交日期": "2022-11-17 16:01:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SystemConfigMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e6624b0501eaf1c415b35790503b663ae9a2993d", "commit_訊息": "[表單設計師]Q00-20221004001 修正DialogInputLabel元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容", "提交日期": "2022-10-04 10:28:05", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ecb87d97273272924d557e3f881edc51437a93dd", "commit_訊息": "[WorkFlow]Q00-20221221001 調整workflow整合WF熱鍵同步表單增加欄位Id的卡控判斷；當傳入的XML若表單元件沒有Id時，直接回傳因欄位沒有Id，所以同步失敗的xml", "提交日期": "2022-12-21 14:17:19", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "807c19fb1e6ae7d000446e31626831bd1d4306b4", "commit_訊息": "[WorkFlow]Q00-20221012003 調整WF傳狀態Action為5時，回寫關卡須回傳狀態8回去", "提交日期": "2022-10-12 15:45:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "281a422a9b1101d68b0147a931f2e387ed8e257c", "commit_訊息": "[WorkFlowERP]S00-*********** 調整WorkFLow取簽核歷程及取簽核頁面URL邏輯", "提交日期": "2022-09-30 09:04:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WorkFlowDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IWFRequestRecordDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBWFRequestRecordDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/TiptopSystemIntegrationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "ed4c9639af3d0e121677d14c606c21f2a294ed95", "commit_訊息": "[WorkFlow]Q00-20220818004 優化易飛，WorkFlow回傳的Debug訊息", "提交日期": "2022-08-18 14:18:06", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b43ef295a4869849279cff4c8cb91acfa4c72552", "commit_訊息": "[流程引擎]Q00-20221212003 修正併簽流程；若其中一個分支直接退回到分支以前的關卡且流程設定被退回時逐關通知，其他分支執行中關卡也一併被關閉的異常", "提交日期": "2022-12-12 17:43:31", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "54b87cbd6d6194f2384ffe4d34d618e04d7d7c82", "commit_訊息": "[Web]Q00-20221118002 修正附件太多導致往下派送失敗", "提交日期": "2022-11-18 14:57:29", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e6cf51dff66360f2a5acfb738e2647a0af6ae1ab", "commit_訊息": "[流程引擎]Q00-20221108003 修正流程引擎的加簽函式功能「addCustomParallelAndSerialActivity」，加簽出來的關卡的表單未依照「參考關卡」呈現對應的「表單元件顯示」狀態", "提交日期": "2022-11-08 16:22:44", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7b32949a7b850ee8f473ccc4b6ef16f1781ac4ec", "commit_訊息": "[流程引擎]Q00-20221209002 T100拋單若第一關與第二關的建立時間相同，自動簽核選擇與前一關相同簽核者就會無效", "提交日期": "2022-12-09 17:09:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/WorkItemTimeComparator.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "6863395d23b06e91a0723a2bf4fa181cded70a04", "commit_訊息": "[流程引擎]Q00-20221208002 修正流程最後一個關卡為服務任務，且系統參數「traceprocess.view.workitem.with.first.activity」設定為false時，系統管理員透過追蹤流程進入流程時，會提示查不到此流程的資料", "提交日期": "2022-12-08 16:37:51", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "af468566ac6d706c51bf2aad607764601cdae4e3", "commit_訊息": "[WEB]Q00-20221208003 修正加簽關卡不會自動帶入簽核意見且要相容自訂關卡後會被清除簽核意見問題。(補修正)", "提交日期": "2022-12-08 17:00:39", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f2b539b54cbf628c1700d4179e43f1d18bcef0f", "commit_訊息": "[WEB]Q00-20221208003 修正加簽關卡不會自動帶入簽核意見。", "提交日期": "2022-12-08 16:31:48", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "607b0ae29af293e8a068cd3d1e3c8d54c65e305a", "commit_訊息": "[Web]Q00-20221206001 修正BPM頁面在Chrome的網址列案Enter會導頁到錯誤頁面", "提交日期": "2022-12-06 15:14:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/action/ActionServlet.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6ba3f3e81488abbe7d9abc72b96daec3cfb1c848", "commit_訊息": "[流程引擎]Q00-20221117001 修正自動簽核在多人處理關卡上沒有效果", "提交日期": "2022-11-17 09:23:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cff159d573e33b76387954d614d09e2a9fd59315", "commit_訊息": "[流程引擎]Q00-20221201001 修正核決關卡的處理者若符合自動簽核時，核決關卡偶發無法繼續派送下去", "提交日期": "2022-12-01 14:06:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0907f94d1483c411a6496314a11171a5e41a011b", "commit_訊息": "[流程引擎]Q00-20221128001 調整系統排程設定在新增、編輯、刪除後檢查所有排程的首次執行時間若小於當前時間時更新首次執行時間避免觸發即時執行排程機制", "提交日期": "2022-11-28 12:12:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/schedule/TimerFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e18ed10743bda1f22ff8990d312ae898c5d3e2d0", "commit_訊息": "[流程引擎]Q00-20221129001修正修正nchar欄位型態錯誤比對問題，導致轉存表單存空值。", "提交日期": "2022-11-29 15:12:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "031e65b4cf55c661940218c9aa04400676860cfd", "commit_訊息": "[雙因素模組]Q00-20221129002 修正點擊BPM頁面的流程代理期間，會跳出兩步驟驗證設定的認證頁面", "提交日期": "2022-11-29 15:47:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "618230db46715b7930b893baf83fb5380cab4fc3", "commit_訊息": "[Web]Q00-20220921002 調整「必須上傳新附件」邏輯，只要存在一筆以上的附件並且符合在該「關卡名稱」上傳的附件，即可通過該驗證", "提交日期": "2022-09-21 17:49:58", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fdf0d7d2bc2be6afa2c4044fa3343892aefc7ede", "commit_訊息": "[系統管理工具]A00-20221117001 修正儲存流程因為Application Server位址沒有填上PORT導致失敗", "提交日期": "2022-11-21 15:55:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5ff015efbb3edc7d61a73a9bcdceb0c324887f62", "commit_訊息": "[Web]Q00-20221026002 新增判斷二階快取應確認來源位置是否為本地端(localhost / 127.0.0.1)若是則不須額外清除。", "提交日期": "2022-10-26 14:13:48", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4725775c678cf55ed115b2c5a63104a1901a24b2", "commit_訊息": "[流程引擎]Q00-20220803002 調整流程主機呼叫其他流程主機清除系統快取的服務，若其他主機無法連線時，逾時時間由20秒改為1秒[補]", "提交日期": "2022-08-08 16:01:52", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9d06316b7e2beb6aca4908801303280018a0aebb", "commit_訊息": "[流程引擎]Q00-20220803002 調整流程主機呼叫其他流程主機清除系統快取的服務，若其他主機無法連線時，逾時時間由20秒改為1秒", "提交日期": "2022-08-08 14:51:00", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c804bea80e982709d0718fef6abf5077bcc64e8c", "commit_訊息": "[Web]Q00-20221116003 修正 Checkbox、RadioButton 元件，若文字過多造成換行時，勾選按鈕會有偏移的問題", "提交日期": "2022-11-16 17:52:27", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "170f8e1c6af1ea01252a746671db58263bbbf0f9", "commit_訊息": "[Web]Q00-20221114005絕對定位表單及RWD表單，統一可設定背景色設定。", "提交日期": "2022-11-14 18:21:16", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/LinkElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ae51035370816744415294b1fd12d4f59177c03f", "commit_訊息": "[Web]S00-20220714004刪除元件時，判斷此元件是否與Grid繫結[補修正]", "提交日期": "2022-10-07 18:11:16", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2db202ab0f4c32500a2babcae741f4697de739d8", "commit_訊息": "[Web]S00-20220714004刪除元件時，判斷此元件是否與Grid繫結", "提交日期": "2022-09-13 14:27:14", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c6115683759eca2cea27c61114052a9f106a6f99", "commit_訊息": "[WEB]Q00-20221014001為相容56版上來的客戶，沒有唯讀背景顏色設定，補防呆", "提交日期": "2022-10-19 17:09:54", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "52091852a93dd7b5294ff03899767aa36ba341d7", "commit_訊息": "[WEB]Q00-20221014001為相容56版上來的客戶，沒有唯讀背景顏色設定。", "提交日期": "2022-10-14 12:52:12", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "2faaf37a29ff619ab44cb44e3951058e5ad9107b", "commit_訊息": "[WEB]S00-20220524004新增線上使用者最大閒置時間系統變數給系統管理員可控制", "提交日期": "2022-09-28 10:55:38", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "51e8ad6bb4fad27c0f05734a68c87d0a17a4472a", "commit_訊息": "[Web]S00-20220810001簽核意見是否顯示管理員[補修正]", "提交日期": "2022-09-15 22:01:27", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "6d2853b18baa01af228b2ce1f03df3653ab68abe", "commit_訊息": "[WEB]Q00-20221013002:修正表單欄位有設定 \"唯讀\"時的欄位顏色，顯示卻都為背景顏色。", "提交日期": "2022-10-13 11:28:13", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "58086526ed18bcd5c8afc51685deccc56530afb7", "commit_訊息": "[Web]Q00-20221114002修正表單設計師Barcode元件異常問題。", "提交日期": "2022-11-14 12:28:50", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0ed15eda061b8b68ea443f53efef6d53f3ffe6a8", "commit_訊息": "[BPM APP]C01-20221018001 修正移動端Grid元件因換行符號導致無法正常顯示Grid資料的問題", "提交日期": "2022-11-11 17:34:54", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "3079323773b9d2201c15114a1eebb94d8e84e896", "commit_訊息": "[BPM APP]C01-20220927008 修正移動端Grid顯示畫面上按鈕重疊問題", "提交日期": "2022-11-03 18:57:05", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ecbd8e54278338b890e219462cca230fc6df143a", "commit_訊息": "Revert \"[BPM APP]C01-20221018001 修正移動端Grid元件因換行符號導致無法正常顯示Grid資料的問題\"", "提交日期": "2022-11-21 16:50:30", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "4fae114e9cfe8dca0a005633897d0046e1c4471a", "commit_訊息": "[BPM APP]C01-20221018001 修正移動端Grid元件因換行符號導致無法正常顯示Grid資料的問題", "提交日期": "2022-11-11 17:34:54", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "2b7bf63bb19bfbf0b73b1e22e6fca646c830995a", "commit_訊息": "[Web]S00-20220720003 修正輸入元件設置必填，隱藏標籤後提示為元件ID。", "提交日期": "2022-10-25 15:45:35", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c7802df4294692d0ee1c613d1b1e06b3f7fec424", "commit_訊息": "[Web]Q00-*********** 修正一般使用者簽核 TIPTOP 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊", "提交日期": "2022-11-17 17:41:12", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/IPrsMappingKeyDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/OJBPrsMappingKeyDAO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "22b58059a8077a4442c9a592d08df8992c650f57", "commit_訊息": "[Web]Q00-*********** 修正通知關卡指定離職人員時，離職交接人沒有作用。", "提交日期": "2022-11-04 15:59:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74b4360ef3fb6556f89b97a989774100c6a42957", "commit_訊息": "[流程引擎]Q00-20221109001 調整流程圖點選核決權限關卡，核決關卡改以關卡建立時間排序", "提交日期": "2022-11-09 17:27:22", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "11e6adb45506a04632392e2a165b8adc4b1a70e7", "commit_訊息": "[Web]Q00-20221019001 修正響應式表單Grid元件設定凍結欄位時縮放瀏覽器時會出現跑版的問題", "提交日期": "2022-10-26 11:14:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-fixed-columns-1.18.3.js", "修改狀態": "重新命名", "狀態代碼": "R099"}], "變更檔案數量": 4}, {"commit_hash": "5b8d38d02b3a00d95a285153fc8a160fed25a270", "commit_訊息": "[流程引擎]A00-20221103001 修正流程繼續派送後或有通知關卡會重複寄信", "提交日期": "2022-11-03 17:41:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0e7f5c20a8680aa2aee3898e15240b5f3a0f0e1b", "commit_訊息": "[流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯", "提交日期": "2022-09-14 10:34:55", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba4d8affe0114b6c86175019d6e00d4381b8b983", "commit_訊息": "[流程引擎]Q00-20220823003 讓亂數產生的ID增加動態加簽CustomDecisionRule的開頭關鍵字，前面流程圖解析邏輯段也要新增", "提交日期": "2022-09-06 14:52:00", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "52efb2c3b43594a3b3693e513b02612b5552efa2", "commit_訊息": "[流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容", "提交日期": "2022-08-24 11:48:03", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "73a88b3d00743a7c92f14cd2f20bf37e742a8016", "commit_訊息": "[Web]Q00-20220805002 調整log訊息，當流程向後派送，後面關卡解析的使用者找不到或是沒有主部門時，增加log訊息", "提交日期": "2022-08-05 17:26:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "441524c3a7179ab4737ba83d00514a60c22b88ce", "commit_訊息": "[流程引擎]Q00-20221031001 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]", "提交日期": "2022-11-02 18:07:03", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "48a96d5a3746c5f1bb67d7e356213b0a0e62d1f6", "commit_訊息": "[流程引擎]Q00-20221031001 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]", "提交日期": "2022-10-31 17:41:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "42818fa9994cb4a573f6f877522cb8e8ece3da58", "commit_訊息": "[流程引擎]Q00-20221031001 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常", "提交日期": "2022-10-31 16:23:44", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f6cf9aca4e8b6d6abb1775d8deb2f2f27a7228d", "commit_訊息": "[流程引擎]Q00-20221028002 修正Oracle資料庫，若流程有設計執行服務任務並將回傳值回寫至流程變數時，服務任務會報錯的異常", "提交日期": "2022-10-28 15:21:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6ba77f1a20387cf07dc83be7e66963a352a9d69a", "commit_訊息": "[流程引擎]Q00-20221025003 調整當核決關卡解析時；若解析人員在同一個組織下有多個兼職部門，且兼職部門的職務核決層級的level都相同時，則以該人員的主部門作為解析部門", "提交日期": "2022-10-25 17:29:45", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f89eb6f8f9d73379b412f9b46b60828abfa58dd0", "commit_訊息": "[流程引擎]Q00-20220915001 修正簡易流程圖若流程有設計迴圈型且線的條件剛好為兩個Gateway互為下一關時，加入防呆避免系統Crash", "提交日期": "2022-09-15 17:24:04", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9319cdafab536fa131a275a53eba3a41fd1b20a1", "commit_訊息": "[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。", "提交日期": "2022-07-29 16:30:05", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "af8d96b54d172f3bb70c9dc5edfbe93a20138958", "commit_訊息": "[流程設計師]Q00-20221103003 修正流程定義/事件處理/流程完成/網頁應用程式，第一次點擊編輯時畫面空白的問題", "提交日期": "2022-11-03 18:09:04", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/FormalParametersCellEditorRenderer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4c70bc058519e01c5ff9dd8eb4fe5b3f5dd3478d", "commit_訊息": "[流程引擎]Q00-20220922001 調整流程撈取工作通知內容機制", "提交日期": "2022-09-22 18:04:54", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8f2cd5b727b6279ccbdcc9801c1100523ce63da4", "commit_訊息": "[WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能", "提交日期": "2022-08-30 08:34:29", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d4fc80f7d30528eba647048cc361f2615d22ee7d", "commit_訊息": "[流程引擎]Q00-20220825001 修正5883版本，當流程有執行通知關卡時，有機率會無法繼續派送至下一個關卡", "提交日期": "2022-08-25 14:52:12", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "187a01a450096335cfddb6d9cd5fe01ffe310fd9", "commit_訊息": "[Web]Q00-20220916001 修正在透過SQLCommand取得的值為null時與原先回傳值不同的問題", "提交日期": "2022-09-16 13:48:45", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70e20589de1e6380bb8db41580767a701319f95d", "commit_訊息": "[Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息", "提交日期": "2022-09-08 14:06:21", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6edfad84b4870f18fcd0bb2fe7dc3fe9f145d6a7", "commit_訊息": "[Web]Q00-20220901001 增加可區別簡易與複雜SQL查詢判斷，若為簡易SQL則執行原邏輯、複雜SQL則使用類子查詢方式", "提交日期": "2022-09-01 15:53:47", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2deb154ee2a298613b746629a232ae4eb0bbe18e", "commit_訊息": "[Web]Q00-20220808001修正從我的最愛點擊流程，第二次點擊時，等待時間的問題", "提交日期": "2022-08-08 14:57:44", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe7c7baf1ce5752ce812ed26436650f9fe0d8b41", "commit_訊息": "[Web]Q00-20220805001 修正作業程序書沒有顯示核決層級關卡的作業名稱", "提交日期": "2022-08-05 14:06:24", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/CreateProcessDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b9cb004af2a64969ef876ad4c31bbb6cfc592551", "commit_訊息": "[Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。[補]", "提交日期": "2022-08-05 12:00:16", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5566de4bb5e48c00c76623456788a328fc411ed6", "commit_訊息": "[Web]Q00-20220804003修正流程進版後，使用者若未重新登入，從分類進入該流程，畫面就會空白，並新增提示訊息的多語系內容", "提交日期": "2022-08-04 22:21:33", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1e9b5d8170bb0d910e5203e6de597d1ac0241c81", "commit_訊息": "[Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。", "提交日期": "2022-08-03 16:48:09", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6795bce2ae26f731debaeb8ca4026e1402c0317f", "commit_訊息": "[Web]A00-20220802001 修正無法開啟SAP維護作業", "提交日期": "2022-08-02 11:27:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6ccd01e01bc1c1786268d563b53eea3cec1093c1", "commit_訊息": "[Web]Q00-20220801002修正在流程圖的核決關卡內容打開單身需要縮才會顯示資料", "提交日期": "2022-08-01 16:26:26", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "83103c693dfd40577952a070155d39958a92d988", "commit_訊息": "[Web]Q00-20220729004 修正如果絕對位置表單Grid連續空的兩關第二關儲存表單時會連FieldValue的Grid根節點都消失", "提交日期": "2022-07-29 17:03:53", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dabeec206d6b9a5f9a806cb49d66c67556423a82", "commit_訊息": "[WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法[補修正]", "提交日期": "2022-07-29 14:34:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "25fccf5a90a6f950c56666f5f7cb3607bd9dcc76", "commit_訊息": "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]", "提交日期": "2022-07-29 14:20:21", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e3bef8cc761f81a01effc33d7be1ee9d8feb72e8", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock[補修正]", "提交日期": "2022-07-29 14:02:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e555a7073db8475d347c12b24cc3d24f9aa3e12a", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock", "提交日期": "2022-07-28 15:20:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4540890d84bf7a1e42ecd0bf5a9203059a18b2ff", "commit_訊息": "[Web]Q00-20220727003 修正Gird元件在關卡設置隱藏時開啟表單會彈出null訊息的問題", "提交日期": "2022-07-27 17:51:33", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f34e47c07ca38c7f170b2fd8adab8c4b43218e4c", "commit_訊息": "[WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法", "提交日期": "2022-07-27 10:41:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a3b1113437428848fdd84f283df82e236033d638", "commit_訊息": "[內部]Q00-20220726001 調整DB取法避免用Id找ProcessPackage撈出一大堆全部取回來", "提交日期": "2022-07-26 18:17:44", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/JpaService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a72ee76cb0a71d142418b56c6c28d55a56e901af", "commit_訊息": "[Web]Q00-20220726002 修正匯入Excel檔案且內容有單引號時會出現錯誤而無法匯入", "提交日期": "2022-07-26 18:16:29", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "11d33d2a0bca063e9ea892dbe6254b39d055b395", "commit_訊息": "[Web]S00-20220129003 調整當表單元件的Label內容過長時完整顯示出Label內容", "提交日期": "2022-07-25 15:44:06", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "543e0e8af97c45360faf25f1459bffe3231ef745", "commit_訊息": "[Web]A00-20220720001 舊版本客製開窗語法在使用模糊查詢時恢復可支援GroupBy語法", "提交日期": "2022-07-25 10:55:23", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9be41fcb0b6aee939a84d3ac1430e7ee6cdff237", "commit_訊息": "[Web]Q00-20220725001 調整流程逾時通知在自定義選擇待辦事項URL時會顯示N.A問題", "提交日期": "2022-07-25 10:22:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "93ee140a29d77f76a79f042bfeaf54db8b79ffaa", "commit_訊息": "[WorkFlow]]Q00-20221014006 調整WorkFlow拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能", "提交日期": "2022-10-14 17:38:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "3f43409161dc86f2a819a1ef1bf852969b442131", "commit_訊息": "[web]S00-20220613001 LDAP登入驗證不可變更密碼且不彈窗，系統帳號驗證登入維持原設定。(補修正)", "提交日期": "2022-09-27 10:51:44", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "99dfdbb65fea26882b837f2ad91eaf1d303c8bd1", "commit_訊息": "[web]S00-20220613001 LDAP登入驗證不可變更密碼且不彈窗，系統帳號驗證登入維持原設定。", "提交日期": "2022-09-26 15:02:43", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "84b31f877699a2b66381cc0e56a3e1c52838c8b1", "commit_訊息": "[Web]Q00-20220908002 關注欄位維護作業設定條件其驗證動作，調整取得的流程包裹是最新而且是發行狀態的版本", "提交日期": "2022-09-08 15:12:59", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "219ec0e544b0e5181758553dd372f62f55baf13c", "commit_訊息": "[WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。", "提交日期": "2022-10-06 14:35:32", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "952e2a312ef78e285340a7a5091ca97760896d3c", "commit_訊息": "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況", "提交日期": "2022-07-29 00:04:37", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0b8e5a0e715d3cc068eead2fda9dd4d390f75930", "commit_訊息": "[ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視", "提交日期": "2022-10-06 13:39:46", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6e49bd4e6c24218ad20ef9accbb6f27c54092b79", "commit_訊息": "[WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限\"沒有作用(補修正，增加可讀性)", "提交日期": "2022-10-06 08:57:33", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9bd4e833688ab6f32960e161dc6436ed474c52d3", "commit_訊息": "[WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限\"沒有作用", "提交日期": "2022-10-04 15:26:58", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6d1e492310f26356f137c1ddf4bb400249494fc9", "commit_訊息": "[Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊", "提交日期": "2022-09-20 11:19:53", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba8b04b0f684772273c2221d8ef1d06727bac06a", "commit_訊息": "[Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]", "提交日期": "2022-09-30 15:47:30", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "914b16ce2ac8e95f7a7296f3b59a785ed7d368d0", "commit_訊息": "[流程引擎]S00-20220722001新增批次通知信件主旨內容", "提交日期": "2022-08-23 15:27:14", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3fd41c94a6424069892033852aadb2a5f325cdf6", "commit_訊息": "[Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題", "提交日期": "2022-08-11 12:56:57", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "50a9d56980d442453795eb0ed72d5f472c03b1b2", "commit_訊息": "[Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題", "提交日期": "2022-08-10 18:34:57", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "877997712019745dd8c4455892c709e3de1b7917", "commit_訊息": "[Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示", "提交日期": "2022-07-29 16:49:53", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aebbd71392ecf3a06f077f5d682427042618ff22", "commit_訊息": "[Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行", "提交日期": "2022-07-28 17:32:17", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6b1d14f80cfe1bb3eacb992d74c89626125fa23", "commit_訊息": "[Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員", "提交日期": "2022-09-30 12:24:25", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "de0d3ebdc8b2af539490605dfc6cfb0ea6456c6e", "commit_訊息": "[T100]Q00-20220927001 修正T100表單轉RWD會產生多餘的Script內容", "提交日期": "2022-09-27 08:36:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ebd9678095c9cadf9210f3e21557b4117f3b6b6c", "commit_訊息": "[流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤", "提交日期": "2022-08-18 11:43:00", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71aa2f2a2d3926391c9d9e2570b3345b475d88b0", "commit_訊息": "Revert \"[內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題\"", "提交日期": "2022-09-22 14:48:45", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/design_tool_web/SystemManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a2cc011c161988a25b2e04dbcdfdc95857b6dedb", "commit_訊息": "\\\\解決build問題", "提交日期": "2022-09-22 14:17:24", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MFAConfigManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bba87ab56dcf5ae63b8cd9bee5982f3de8f6aa4d", "commit_訊息": "//因系統權限管理頁面開啟緩慢問題暫時把系統管理打開", "提交日期": "2022-09-22 13:35:57", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6919e0e16020bf366b2d034cf58367450b80c63c", "commit_訊息": "[TIPTOP]A00-*********** 新增TIPTOP整合設定，當夾帶附件型態為http,根據TIPTOP附件主機的port號取得附件", "提交日期": "2022-09-20 14:55:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "068fc48aa0f6e962ddf7aade573e2c2b329fe5c3", "commit_訊息": "[內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題", "提交日期": "2022-09-15 10:23:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/design_tool_web/SystemManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "c6557868b263216f2e9c200fe70f496ace493633", "commit_訊息": "[TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久", "提交日期": "2022-08-19 17:57:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "668493fea9bced6d17164564040c7334c9a637ce", "commit_訊息": "[Web]S00-20220810001簽核意見是否顯示管理員", "提交日期": "2022-08-19 10:49:46", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "06e9149b26113fcd318a6c0c026a2fd291a5bd10", "commit_訊息": "[流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]", "提交日期": "2022-08-18 17:50:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "320137581f657a4131ec7aa2604ee7d042b347e7", "commit_訊息": "[流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]", "提交日期": "2022-08-18 17:47:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8037c65f3dc300d41af2dddf23592991d71230df", "commit_訊息": "[流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到", "提交日期": "2022-08-18 17:41:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}]}