# Release Notes - BPM

## 版本資訊
- **新版本**: release_5.8.3.2
- **舊版本**: release_5.8.3.1
- **生成時間**: 2025-07-18 11:45:10
- **新增 Commit 數量**: 82

## 變更摘要

### lorenchang (2 commits)

- **2022-06-26 22:52:34**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.3.2
  - 變更檔案: 25 個
- **2020-04-29 16:25:10**: [流程引擎]Q00-20200429001 修正5.8版(Oracle)在使用者同時登入時會導致登入後出現請洽系統管理員(WizardAuthority OptimisticLockException)
  - 變更檔案: 2 個

### cherryliao (4 commits)

- **2020-06-23 13:03:19**: [BPM APP]Q00-20200623001 修正IMG應用角標數與流程數量不一致
  - 變更檔案: 1 個
- **2020-06-09 16:48:00**: [BPM APP]Q00-20200312001 調整formBuilder解析不出表單內容時顯示的錯誤訊息
  - 變更檔案: 3 個
- **2020-05-27 10:57:28**: [BPM APP]C01-20200522003 調整撈取中間層無資料時的判斷機制
  - 變更檔案: 1 個
- **2020-04-30 11:23:34**: [內部]移除不必要的log
  - 變更檔案: 1 個

### 林致帆 (23 commits)

- **2020-06-23 13:00:33**: [流程引擎]A00-20200609003 流程若批次轉派失敗，增加Log判斷確認是否為流程設計師的關卡設定未調整[補修正]
  - 變更檔案: 1 個
- **2020-06-23 12:10:33**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2020-06-23 12:10:08**: [流程引擎]A00-20200609003 流程若批次轉派失敗，增加Log判斷確認是否為流程設計師的關卡設定未調整[補修正]
  - 變更檔案: 1 個
- **2020-06-22 10:17:30**: [Web]Q00-*********** 修正關注項目模組中的"關注類別維護"查詢開窗帶回的值為undefined
  - 變更檔案: 1 個
- **2020-06-22 10:15:10**: [Web]Q00-*********** 修正關注項目模組中的"關注指標重要性維護"查詢開窗帶回的值為undefined
  - 變更檔案: 1 個
- **2020-06-18 11:42:46**: [流程引擎]A00-20200609003 流程若批次轉派失敗，增加Log判斷確認是否為流程設計師的關卡設定未調整
  - 變更檔案: 1 個
- **2020-06-18 09:36:40**: [流程引擎]A00-20200609002 修正使用者有缺席紀錄，流程卻無法轉派給代理人
  - 變更檔案: 1 個
- **2020-06-11 15:39:22**: [流程引擎]A00-20200401001 修正DialogInputLabel為自定義型態時，未填值會造成FieldValues存進底線符號
  - 變更檔案: 1 個
- **2020-06-09 09:00:55**: [Web]A00-20200519001 調整列印表單在顯示附件的Grid內容增加在線閱覽的屬性名稱及圖示
  - 變更檔案: 3 個
- **2020-06-03 16:34:55**: [Web]A00-20200316001 修正資料庫為Oracle,BPM首頁進入系統設定頁面異常
  - 變更檔案: 1 個
- **2020-05-29 19:01:10**: [流程引擎]A00-20200421001修正流程取回重辦後，核決層級關卡參考的前一關卡的人員若為最高等級，派送到核決層級關卡會與取回重辦前的核決層級關卡相同[補修正]
  - 變更檔案: 2 個
- **2020-05-29 18:34:45**: [流程引擎]A00-20200421001修正流程取回重辦後，核決層級關卡參考的前一關卡的人員若為最高等級，派送到核決層級關卡會與取回重辦前的核決層級關卡相同[補修正]
  - 變更檔案: 1 個
- **2020-05-25 16:34:35**: [Web]A00-20200313001 修正IE在有兩個TextBox元件為顯示千分位,新增至Grid上無法binding回去
  - 變更檔案: 2 個
- **2020-05-22 15:29:21**: [流程引擎]A00-20200421001修正流程取回重辦後，核決層級關卡參考的前一關卡的人員若為最高等級，派送到核決層級關卡會與取回重辦前的核決層級關卡相同[補修正]
  - 變更檔案: 1 個
- **2020-05-21 15:33:10**: [流程引擎]C01-20200508005 修正因連線數關閉超時，造成開窗緩慢
  - 變更檔案: 1 個
- **2020-05-21 14:30:06**: [流程引擎]A00-20200421001修正流程取回重辦後，核決層級關卡參考的前一關卡的人員若為最高等級，派送到核決層級關卡會與取回重辦前的核決層級關卡相同
  - 變更檔案: 1 個
- **2020-05-18 17:23:24**: [流程引擎]Q00-20200518001 修正執行批次寄信排程，無法收到信件
  - 變更檔案: 1 個
- **2020-05-13 13:59:29**: [流程引擎]C01-20191216001 調整若使用匿名驗證方式，必需傳入null，以免mail.jar誤判為非匿名[補修正]
  - 變更檔案: 1 個
- **2020-05-07 19:07:49**: [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料[補修正]
  - 變更檔案: 1 個
- **2020-05-05 16:25:56**: [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料[補修正]
  - 變更檔案: 1 個
- **2020-04-28 18:18:34**: A00-20200217001 修正T100單據aist310無法拋單到EFGP
  - 變更檔案: 1 個
- **2020-04-28 18:13:02**: [E10]S00-20200303001 調整E10組織同步內容[補修正]
  - 變更檔案: 2 個
- **2020-04-28 08:42:04**: [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料
  - 變更檔案: 1 個

### yamiyeh10 (6 commits)

- **2020-06-23 10:56:29**: [BPM APP]Q00-20200623003 修正當英文語系時在移動端上Grid元件的新增資料與icon跑版問題
  - 變更檔案: 1 個
- **2020-06-23 10:49:13**: [BPM APP]Q00-20200617002 修正在移動端查看表單時附件按鈕文字沒有顯示多語系問題
  - 變更檔案: 2 個
- **2020-06-20 16:03:34**: [BPM APP]Q00-20200619007 修正在行動端上的追蹤已發起流程沒有顯示撤銷按鈕問題
  - 變更檔案: 1 個
- **2020-06-08 15:02:25**: [BPM APP]Q00-20200514003 修正行動表單的附件列表在查看附件時往下滑動會出現刪除按鈕問題
  - 變更檔案: 1 個
- **2020-06-05 17:07:16**: [BPM APP]Q00-20200526001 修正IMG在待辦詳情畫面在錯誤頁面時不應顯示浮動按鈕議題
  - 變更檔案: 1 個
- **2020-06-04 17:43:40**: [BPM APP]Q00-20200511001 修正IMG在流程追蹤裡查看絕對位置表單時主旨會被遮擋無法查看問題
  - 變更檔案: 2 個

### 王鵬程 (10 commits)

- **2020-06-22 14:41:59**: [Web]A00-*********** 修正在IE中無法使用企業流程監控
  - 變更檔案: 1 個
- **2020-06-16 11:57:00**: [表單設計師]C01-20200612001 修正舊版表單有下拉元件(ComboBox)，版更到V5831後發起該表單會出現錯誤
  - 變更檔案: 1 個
- **2020-06-08 18:29:10**: [Web]A00-20200603002 修正流程解析到離職人員時，會被一直加上(X)且更動到資料庫
  - 變更檔案: 1 個
- **2020-06-04 18:12:22**: [Web]A00-20200603005 修正DialogInputLabel元件使用樹狀顯示並加上過濾條件時，在畫面上開窗會彈出錯誤
  - 變更檔案: 1 個
- **2020-06-01 18:56:59**: [Web]A00-20200514001 修正當使用者在第二次取回重辦且只有一個關卡時，會一直導回取回重辦的清單頁面。
  - 變更檔案: 1 個
- **2020-05-19 16:03:35**: [Web]A00-20200512004 修正在IE中流程中關卡人員出現兩次(含)以上並簽核至少兩關後，取回重辦會出現HTTP400錯誤
  - 變更檔案: 1 個
- **2020-05-13 18:00:00**: [Web]C01-20200320002 修正DialogInputMulti開窗勾選後，換了查詢條件後原本被勾選的卻沒被勾選
  - 變更檔案: 1 個
- **2020-04-29 18:53:11**: [Web]C01-*********** 修正進入模組程式維護頁面，程式的URL中有<和>符號時，點選該列後並儲存，再次進來顯示會異常
  - 變更檔案: 1 個
- **2020-04-28 14:53:59**: [Web]A00-20200423001 修正RWD的Grid排序問題
  - 變更檔案: 1 個
- **2020-04-28 14:15:16**: [Web]C01-20200131002 修正 檢視核決權限表型式的關卡頁面未放大最大時，在最下方關卡資訊有資料時卻無法顯示
  - 變更檔案: 1 個

### waynechang (10 commits)

- **2020-06-22 11:59:52**: [流程引擎]C01-*********** 修正撤銷流程時，會有部分table造成DBLock，導致系統無法運作
  - 變更檔案: 1 個
- **2020-06-19 18:43:07**: [流程引擎]Q00-20200619003 修正Dialog-Custom元件設定invisible時，往下派送會異常
  - 變更檔案: 1 個
- **2020-06-09 16:15:51**: [流程引擎]C00-*********** 修正撤銷流程時，會有部分table造成DBLock，導致系統無法運作
  - 變更檔案: 1 個
- **2020-06-08 17:12:06**: [Web]C01-20200603003 修正chrome83版後，流程掛多表單情況下簽核會派送失敗
  - 變更檔案: 1 個
- **2020-05-29 14:05:53**: [Web]Q00-20200529001 修正資料選取器選取後會自動到最上列，調整為選取後讓畫面保持不動
  - 變更檔案: 1 個
- **2020-05-28 16:00:15**: [ISO]C01-20200526002 修正ISO新增、變更單上傳檔案為PDF時，BCL8轉檔有可能會失敗議題
  - 變更檔案: 1 個
- **2020-05-20 14:27:34**: [Web]A00-20200504002 修正加簽平行會簽關卡時，沒有BpmnType屬性，會導致流程圖開啟異常
  - 變更檔案: 1 個
- **2020-05-14 16:09:34**: [Web]Q00-20200514001 取得代辦清單(v2.process.workitemlist.get)API-增加回傳workItemOID
  - 變更檔案: 2 個
- **2020-04-30 16:19:38**: [Web]A00-20200429002 修正檢核gird資料服務異常
  - 變更檔案: 1 個
- **2020-04-30 11:04:38**: [Web]C01-*********** 優化產品部門開窗增加分頁功能
  - 變更檔案: 5 個

### walter_wu (11 commits)

- **2020-06-19 15:10:27**: [Web]Q00-20200619004 新加的多語系不該有越南語  應留空白讓顧問方便統一翻譯
  - 變更檔案: 1 個
- **2020-06-05 17:49:23**: [Web]A00-20200504001 修正流程上放附件，在流程畫面會被截斷
  - 變更檔案: 1 個
- **2020-05-21 18:56:17**: [Portal]Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤[補]
  - 變更檔案: 2 個
- **2020-05-19 18:46:10**: [在線閱覽]Q00-20200519001 修正附件為在線閱覽卻可以在上傳畫面下載
  - 變更檔案: 1 個
- **2020-05-18 14:30:51**: [Portal]Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤[補]
  - 變更檔案: 1 個
- **2020-05-12 15:44:36**: A00-20200410002 修正員工轉派或離職轉派超過5XX筆按下轉派後畫面卡死
  - 變更檔案: 2 個
- **2020-05-08 15:35:08**: [Portal]Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤[補]
  - 變更檔案: 1 個
- **2020-05-07 15:55:45**: Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤
  - 變更檔案: 3 個
- **2020-05-07 11:35:03**: [Web]C01-20200428002 修正流程統計類功能 Oracle日期查詢錯誤 統計結果多一天
  - 變更檔案: 1 個
- **2020-04-22 08:46:40**: C01-20200331004 修正如果表單DIALOG對話窗型式元件在流程設定隱藏下一關資料會遺失
  - 變更檔案: 1 個
- **2020-05-05 17:46:55**: # WARNING: head commit changed in the meantime
  - 變更檔案: 2 個

### yanann_chen (12 commits)

- **2020-06-18 18:06:03**: [流程引擎]C01-20200602004 修正問題: 追蹤流程清單接口使用"發單人姓名"為搜尋條件，查詢結果異常
  - 變更檔案: 1 個
- **2020-06-18 17:07:28**: [Web]A00-20200612001 修正問題: BPM URL查詢單一流程實例的所有表單資料顯示異常
  - 變更檔案: 2 個
- **2020-06-18 15:37:33**: [表單設計師]C01-20200617002 修正問題: Grid欄位多語系因含有單引號(')導致Grid編輯功能異常
  - 變更檔案: 1 個
- **2020-06-17 16:36:53**: [Web]Q00-20200617001 補上多語系: printallformdata.button.printform
  - 變更檔案: 1 個
- **2020-06-11 17:35:56**: [T100]Q00-20200611002 修正問題: T100表單同步未更新CheckBox選項多語系
  - 變更檔案: 1 個
- **2020-06-11 17:29:25**: [T100]A00-20200504003 修正問題: T100表單同步未更新Grid欄位多語系
  - 變更檔案: 1 個
- **2020-06-11 15:25:08**: [Web]Q00-20200611001 修正Chrome 83版相關問題
  - 變更檔案: 1 個
- **2020-05-13 16:59:49**: [組織設計師]A00-20200506001 修正問題: 調離員工後檢視員工資料發生錯誤
  - 變更檔案: 1 個
- **2020-05-04 12:01:34**: [流程設計師]A00-20200410003 XPDL轉BPMN加入防呆
  - 變更檔案: 1 個
- **2020-04-30 15:24:51**: [流程引擎]A00-20200331001 修正問題: 於組織設計師使用者基本資料窗新增人員隸屬組織資料報錯 調整處理順序並加上防呆
  - 變更檔案: 1 個
- **2020-04-30 10:53:41**: [Web]C01-*********** 修正問題: 系統排程設定"每週"無效 新增排程時，設定"每週"無效[補]
  - 變更檔案: 1 個
- **2020-04-28 16:06:28**: [流程引擎]C01-20191223002 修正問題: 主管首頁逾時關卡統計計算錯誤 修正SQL command[補]
  - 變更檔案: 2 個

### 詩雅 (2 commits)

- **2020-06-09 11:03:26**: [BPM APP]C01-20200605001調整grid修改按鈕機制
  - 變更檔案: 2 個
- **2020-05-27 11:09:46**: [BPM APP]Q00-20200526002調整流程追蹤者身分設定
  - 變更檔案: 3 個

### wusnnn (2 commits)

- **2020-04-28 15:05:29**: [BPM] 修正無法正確辨識副檔名的問題
  - 變更檔案: 5 個
- **2020-04-28 14:56:24**: [BPM]ReadExceltoStringFormat支援xlsx格式
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.3.2
- **Commit ID**: `701d332353900ee1b490a8c1529f2ba0f37bfb80`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:52:34
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [BPM APP]Q00-20200623001 修正IMG應用角標數與流程數量不一致
- **Commit ID**: `c4e88e6ac64f77bb6d4c133e48e6f307e91e0785`
- **作者**: cherryliao
- **日期**: 2020-06-23 13:03:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 3. [流程引擎]A00-20200609003 流程若批次轉派失敗，增加Log判斷確認是否為流程設計師的關卡設定未調整[補修正]
- **Commit ID**: `93c7b4f8a8878a7e140b66219321b965f2018ff4`
- **作者**: 林致帆
- **日期**: 2020-06-23 13:00:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 4. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `5da0acaf3fffe46bbc416db1109c45aeba3005e1`
- **作者**: 林致帆
- **日期**: 2020-06-23 12:10:33
- **變更檔案數量**: 0

### 5. [流程引擎]A00-20200609003 流程若批次轉派失敗，增加Log判斷確認是否為流程設計師的關卡設定未調整[補修正]
- **Commit ID**: `983d54f6d52d03f4dae16acfc6df27261a80eb6e`
- **作者**: 林致帆
- **日期**: 2020-06-23 12:10:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 6. [BPM APP]Q00-20200623003 修正當英文語系時在移動端上Grid元件的新增資料與icon跑版問題
- **Commit ID**: `0cd8aff519e8f20405eef8296f94aa87742714a5`
- **作者**: yamiyeh10
- **日期**: 2020-06-23 10:56:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 7. [BPM APP]Q00-20200617002 修正在移動端查看表單時附件按鈕文字沒有顯示多語系問題
- **Commit ID**: `57be47158cc6c6858ac421f58fc703f3f3dc0060`
- **作者**: yamiyeh10
- **日期**: 2020-06-23 10:49:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java`

### 8. [Web]A00-*********** 修正在IE中無法使用企業流程監控
- **Commit ID**: `7f9e0993ed16092ffd1acae2485234351f23c42c`
- **作者**: 王鵬程
- **日期**: 2020-06-22 14:41:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`

### 9. [流程引擎]C01-*********** 修正撤銷流程時，會有部分table造成DBLock，導致系統無法運作
- **Commit ID**: `01ea328c459866b2810895c28879682e7ba6e6f1`
- **作者**: waynechang
- **日期**: 2020-06-22 11:59:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 10. [Web]Q00-*********** 修正關注項目模組中的"關注類別維護"查詢開窗帶回的值為undefined
- **Commit ID**: `4b596f032ccbb4890deda2f89940814527837411`
- **作者**: 林致帆
- **日期**: 2020-06-22 10:17:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`

### 11. [Web]Q00-*********** 修正關注項目模組中的"關注指標重要性維護"查詢開窗帶回的值為undefined
- **Commit ID**: `7d27321f5290e204f77e3f4cbf5f52ea6e87eb44`
- **作者**: 林致帆
- **日期**: 2020-06-22 10:15:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`

### 12. [BPM APP]Q00-20200619007 修正在行動端上的追蹤已發起流程沒有顯示撤銷按鈕問題
- **Commit ID**: `90e55629c062fdaa0784c7093b0c7bd6791df0d4`
- **作者**: yamiyeh10
- **日期**: 2020-06-20 16:03:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`

### 13. [流程引擎]Q00-20200619003 修正Dialog-Custom元件設定invisible時，往下派送會異常
- **Commit ID**: `2672e16f7b410962f7db55c40ed57906eb93247c`
- **作者**: waynechang
- **日期**: 2020-06-19 18:43:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 14. [Web]Q00-20200619004 新加的多語系不該有越南語  應留空白讓顧問方便統一翻譯
- **Commit ID**: `b9ad52e2af086088fec81759d3d2a39a1a22a3fa`
- **作者**: walter_wu
- **日期**: 2020-06-19 15:10:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 15. [流程引擎]C01-20200602004 修正問題: 追蹤流程清單接口使用"發單人姓名"為搜尋條件，查詢結果異常
- **Commit ID**: `c65dd1c7c407b3b7df371f07eafad5c2907ba49b`
- **作者**: yanann_chen
- **日期**: 2020-06-18 18:06:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 16. [Web]A00-20200612001 修正問題: BPM URL查詢單一流程實例的所有表單資料顯示異常
- **Commit ID**: `f8b253fd39b97f2e0c3fd444ec39e11be0da884b`
- **作者**: yanann_chen
- **日期**: 2020-06-18 17:07:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 17. [表單設計師]C01-20200617002 修正問題: Grid欄位多語系因含有單引號(')導致Grid編輯功能異常
- **Commit ID**: `bb81b17b0a1f70de60caa771e068257216ed53cf`
- **作者**: yanann_chen
- **日期**: 2020-06-18 15:37:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 18. [流程引擎]A00-20200609003 流程若批次轉派失敗，增加Log判斷確認是否為流程設計師的關卡設定未調整
- **Commit ID**: `4259cc2ca485979ea9e57db455ae7fdc7d993eef`
- **作者**: 林致帆
- **日期**: 2020-06-18 11:42:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 19. [流程引擎]A00-20200609002 修正使用者有缺席紀錄，流程卻無法轉派給代理人
- **Commit ID**: `3e09b737181d00aff1a9028e7038903e350ed136`
- **作者**: 林致帆
- **日期**: 2020-06-18 09:36:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java`

### 20. [Web]Q00-20200617001 補上多語系: printallformdata.button.printform
- **Commit ID**: `c701315ca55930fa6ab62ad0bc8690179389a5d3`
- **作者**: yanann_chen
- **日期**: 2020-06-17 16:36:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 21. [表單設計師]C01-20200612001 修正舊版表單有下拉元件(ComboBox)，版更到V5831後發起該表單會出現錯誤
- **Commit ID**: `cc3fba75be298fbaf5e8c98e94e8226b8feae34f`
- **作者**: 王鵬程
- **日期**: 2020-06-16 11:57:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 22. [T100]Q00-20200611002 修正問題: T100表單同步未更新CheckBox選項多語系
- **Commit ID**: `6869bd962b89f22eb03d8ad94d1d6ef34d55b4ed`
- **作者**: yanann_chen
- **日期**: 2020-06-11 17:35:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/T100FormMerge.java`

### 23. [T100]A00-20200504003 修正問題: T100表單同步未更新Grid欄位多語系
- **Commit ID**: `3ef499eef8150ee755f0543ea1bb965176ed598f`
- **作者**: yanann_chen
- **日期**: 2020-06-11 17:29:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/T100FormMerge.java`

### 24. [流程引擎]A00-20200401001 修正DialogInputLabel為自定義型態時，未填值會造成FieldValues存進底線符號
- **Commit ID**: `60dd1cbc260cc90cea713e87ede0e946bbbbec41`
- **作者**: 林致帆
- **日期**: 2020-06-11 15:39:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 25. [Web]Q00-20200611001 修正Chrome 83版相關問題
- **Commit ID**: `0ecf43d468afd7873be12c2730877862abf649f9`
- **作者**: yanann_chen
- **日期**: 2020-06-11 15:25:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 26. [BPM APP]Q00-20200312001 調整formBuilder解析不出表單內容時顯示的錯誤訊息
- **Commit ID**: `5eb5fb66d4f8f8a5529b2642306ae0bebf9e0267`
- **作者**: cherryliao
- **日期**: 2020-06-09 16:48:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileResigendServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`

### 27. [流程引擎]C00-*********** 修正撤銷流程時，會有部分table造成DBLock，導致系統無法運作
- **Commit ID**: `be7b9874c040a2b325595c460c5921c3275f4426`
- **作者**: waynechang
- **日期**: 2020-06-09 16:15:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 28. [BPM APP]C01-20200605001調整grid修改按鈕機制
- **Commit ID**: `78dcee6df65d4d7a212650d38badb466fc36db50`
- **作者**: 詩雅
- **日期**: 2020-06-09 11:03:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 29. [Web]A00-20200519001 調整列印表單在顯示附件的Grid內容增加在線閱覽的屬性名稱及圖示
- **Commit ID**: `8f92f9ec2c36f5dd3511611296de5f0fb7248bd0`
- **作者**: 林致帆
- **日期**: 2020-06-09 09:00:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`

### 30. [Web]A00-20200603002 修正流程解析到離職人員時，會被一直加上(X)且更動到資料庫
- **Commit ID**: `50ef2e75f9ed9f8d06b2cbeae2a0839c944f8347`
- **作者**: 王鵬程
- **日期**: 2020-06-08 18:29:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 31. [Web]C01-20200603003 修正chrome83版後，流程掛多表單情況下簽核會派送失敗
- **Commit ID**: `2c29a83ec349015f959ff2c0f527f46cfdb14a70`
- **作者**: waynechang
- **日期**: 2020-06-08 17:12:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 32. [BPM APP]Q00-20200514003 修正行動表單的附件列表在查看附件時往下滑動會出現刪除按鈕問題
- **Commit ID**: `a54054985cf706f53ec1c62cfc117c410a351619`
- **作者**: yamiyeh10
- **日期**: 2020-06-08 15:02:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 33. [Web]A00-20200504001 修正流程上放附件，在流程畫面會被截斷
- **Commit ID**: `9b2d0fa4261d870063d3295d43f63e7ada540277`
- **作者**: walter_wu
- **日期**: 2020-06-05 17:49:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AttachmentHandler.jsp`

### 34. [BPM APP]Q00-20200526001 修正IMG在待辦詳情畫面在錯誤頁面時不應顯示浮動按鈕議題
- **Commit ID**: `471439be38cd3c6d246a695c1155d3dd5a1d95c0`
- **作者**: yamiyeh10
- **日期**: 2020-06-05 17:07:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 35. [Web]A00-20200603005 修正DialogInputLabel元件使用樹狀顯示並加上過濾條件時，在畫面上開窗會彈出錯誤
- **Commit ID**: `073c1bbd7933e639ab1c44f9f3eb83341a713fc0`
- **作者**: 王鵬程
- **日期**: 2020-06-04 18:12:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`

### 36. [BPM APP]Q00-20200511001 修正IMG在流程追蹤裡查看絕對位置表單時主旨會被遮擋無法查看問題
- **Commit ID**: `b02c957c182c96a0d83e8cbecdc8de97cd424f73`
- **作者**: yamiyeh10
- **日期**: 2020-06-04 17:43:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`

### 37. [Web]A00-20200316001 修正資料庫為Oracle,BPM首頁進入系統設定頁面異常
- **Commit ID**: `e2450bbd98d267f85724089f9e346dd99aededcc`
- **作者**: 林致帆
- **日期**: 2020-06-03 16:34:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java`

### 38. [Web]A00-20200514001 修正當使用者在第二次取回重辦且只有一個關卡時，會一直導回取回重辦的清單頁面。
- **Commit ID**: `44359606a1f612e79dbfe1a7c5728d57e202e64c`
- **作者**: 王鵬程
- **日期**: 2020-06-01 18:56:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 39. [流程引擎]A00-20200421001修正流程取回重辦後，核決層級關卡參考的前一關卡的人員若為最高等級，派送到核決層級關卡會與取回重辦前的核決層級關卡相同[補修正]
- **Commit ID**: `e8edb28b96ec4884e86f4bd81a1987b3c8a9f5ac`
- **作者**: 林致帆
- **日期**: 2020-05-29 19:01:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReexecuteActivityAction.java`

### 40. [流程引擎]A00-20200421001修正流程取回重辦後，核決層級關卡參考的前一關卡的人員若為最高等級，派送到核決層級關卡會與取回重辦前的核決層級關卡相同[補修正]
- **Commit ID**: `05ee59365d45397d0072be674eeee92c334eed4b`
- **作者**: 林致帆
- **日期**: 2020-05-29 18:34:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReexecuteActivityAction.java`

### 41. [Web]Q00-20200529001 修正資料選取器選取後會自動到最上列，調整為選取後讓畫面保持不動
- **Commit ID**: `76699e87198ed366c46a3692eade2870b5a16f40`
- **作者**: waynechang
- **日期**: 2020-05-29 14:05:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 42. [ISO]C01-20200526002 修正ISO新增、變更單上傳檔案為PDF時，BCL8轉檔有可能會失敗議題
- **Commit ID**: `7b8d13c370b38b9e54c065cb8a556231112f0827`
- **作者**: waynechang
- **日期**: 2020-05-28 16:00:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 43. [BPM APP]Q00-20200526002調整流程追蹤者身分設定
- **Commit ID**: `6276397e2450368ffecf4a420f537fc9319fe0fa`
- **作者**: 詩雅
- **日期**: 2020-05-27 11:09:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`

### 44. [BPM APP]C01-20200522003 調整撈取中間層無資料時的判斷機制
- **Commit ID**: `e44f31ec13745768dac3f7c64e73deefbf0d2941`
- **作者**: cherryliao
- **日期**: 2020-05-27 10:57:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 45. [Web]A00-20200313001 修正IE在有兩個TextBox元件為顯示千分位,新增至Grid上無法binding回去
- **Commit ID**: `324a3fdafd688c66862ff5edde8abb064502505b`
- **作者**: 林致帆
- **日期**: 2020-05-25 16:34:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 46. [流程引擎]A00-20200421001修正流程取回重辦後，核決層級關卡參考的前一關卡的人員若為最高等級，派送到核決層級關卡會與取回重辦前的核決層級關卡相同[補修正]
- **Commit ID**: `3649707a9af4f96967edf8ea590d81dbc6745224`
- **作者**: 林致帆
- **日期**: 2020-05-22 15:29:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 47. [Portal]Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤[補]
- **Commit ID**: `f75fc535b09291a74cb46f2662af823814aaabd8`
- **作者**: walter_wu
- **日期**: 2020-05-21 18:56:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 48. [流程引擎]C01-20200508005 修正因連線數關閉超時，造成開窗緩慢
- **Commit ID**: `32fb84e418f695c699703c81f2c5d1cafe11003f`
- **作者**: 林致帆
- **日期**: 2020-05-21 15:33:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/ConnectionFactory.java`

### 49. [流程引擎]A00-20200421001修正流程取回重辦後，核決層級關卡參考的前一關卡的人員若為最高等級，派送到核決層級關卡會與取回重辦前的核決層級關卡相同
- **Commit ID**: `f473fa922a2f786a346da1d8f90f65ced433d371`
- **作者**: 林致帆
- **日期**: 2020-05-21 14:30:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 50. [Web]A00-20200504002 修正加簽平行會簽關卡時，沒有BpmnType屬性，會導致流程圖開啟異常
- **Commit ID**: `27d1cf269a4f732fcf5206a7e5f2c925a01af2eb`
- **作者**: waynechang
- **日期**: 2020-05-20 14:27:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`

### 51. [在線閱覽]Q00-20200519001 修正附件為在線閱覽卻可以在上傳畫面下載
- **Commit ID**: `660bef839f6fe7f46d63c3d57eb897d722f5033b`
- **作者**: walter_wu
- **日期**: 2020-05-19 18:46:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 52. [Web]A00-20200512004 修正在IE中流程中關卡人員出現兩次(含)以上並簽核至少兩關後，取回重辦會出現HTTP400錯誤
- **Commit ID**: `1f0ffe910b10321c7bca3be6533edd93e52e6d5a`
- **作者**: 王鵬程
- **日期**: 2020-05-19 16:03:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 53. [流程引擎]Q00-20200518001 修正執行批次寄信排程，無法收到信件
- **Commit ID**: `9b6c3ad36efa6923efb3e69cb5f78ed26f7e2a73`
- **作者**: 林致帆
- **日期**: 2020-05-18 17:23:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 54. [Portal]Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤[補]
- **Commit ID**: `61597509ea53dc1d1bb74c7f81179ca18bc88065`
- **作者**: walter_wu
- **日期**: 2020-05-18 14:30:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 55. [Web]Q00-20200514001 取得代辦清單(v2.process.workitemlist.get)API-增加回傳workItemOID
- **Commit ID**: `d9279dbd007a2a8ab7f185e81952ff458b060e0c`
- **作者**: waynechang
- **日期**: 2020-05-14 16:09:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 56. [Web]C01-20200320002 修正DialogInputMulti開窗勾選後，換了查詢條件後原本被勾選的卻沒被勾選
- **Commit ID**: `4779be7338a5abcfccaae350f10de09fc5402fa5`
- **作者**: 王鵬程
- **日期**: 2020-05-13 18:00:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/dataChooser/ResultObjectForDataChooser.java`

### 57. [組織設計師]A00-20200506001 修正問題: 調離員工後檢視員工資料發生錯誤
- **Commit ID**: `da654555ceecb20c9920447b562dc76a7745d071`
- **作者**: yanann_chen
- **日期**: 2020-05-13 16:59:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/TransferUserFromOrgUnitAction.java`

### 58. [流程引擎]C01-20191216001 調整若使用匿名驗證方式，必需傳入null，以免mail.jar誤判為非匿名[補修正]
- **Commit ID**: `8ffd0c19fcf4b50aacb581821643774039fc7e12`
- **作者**: 林致帆
- **日期**: 2020-05-13 13:59:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java`

### 59. A00-20200410002 修正員工轉派或離職轉派超過5XX筆按下轉派後畫面卡死
- **Commit ID**: `e35ba2967eb4e539456561663d828fe2b8906624`
- **作者**: walter_wu
- **日期**: 2020-05-12 15:44:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 60. [Portal]Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤[補]
- **Commit ID**: `48fa5f571c17690ea72d60aa1c1ac5c394bbf491`
- **作者**: walter_wu
- **日期**: 2020-05-08 15:35:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PortalIntegrationEFGP.java`

### 61. [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料[補修正]
- **Commit ID**: `233ac4044dc7d229e6fcda86b99788a841106aed`
- **作者**: 林致帆
- **日期**: 2020-05-07 19:07:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgMgr.java`

### 62. Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤
- **Commit ID**: `349ef00278c01c19370d6fe353cabf3ebdb90b59`
- **作者**: walter_wu
- **日期**: 2020-05-07 15:55:45
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PortalIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 63. [Web]C01-20200428002 修正流程統計類功能 Oracle日期查詢錯誤 統計結果多一天
- **Commit ID**: `7b4c2f482ccedba1fe6eb1ab6526194ed739532c`
- **作者**: walter_wu
- **日期**: 2020-05-07 11:35:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`

### 64. C01-20200331004 修正如果表單DIALOG對話窗型式元件在流程設定隱藏下一關資料會遺失
- **Commit ID**: `467f17e325e5363e973e44f17cef20839a942f05`
- **作者**: walter_wu
- **日期**: 2020-04-22 08:46:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 65. # WARNING: head commit changed in the meantime
- **Commit ID**: `e08ebe65daf528e30fc38fe02429057f0301e9eb`
- **作者**: walter_wu
- **日期**: 2020-05-05 17:46:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 66. [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料[補修正]
- **Commit ID**: `4e0769945fc797cfd5a4b7d6a80c795f7f686d47`
- **作者**: 林致帆
- **日期**: 2020-05-05 16:25:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgMgr.java`

### 67. [流程設計師]A00-20200410003 XPDL轉BPMN加入防呆
- **Commit ID**: `d75964e687f8e9ab0db6ba4a7d32886139197834`
- **作者**: yanann_chen
- **日期**: 2020-05-04 12:01:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java`

### 68. [Web]A00-20200429002 修正檢核gird資料服務異常
- **Commit ID**: `1ee9325ec1abef872437043cbb94cd715c4ac5ad`
- **作者**: waynechang
- **日期**: 2020-04-30 16:19:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java`

### 69. [流程引擎]A00-20200331001 修正問題: 於組織設計師使用者基本資料窗新增人員隸屬組織資料報錯 調整處理順序並加上防呆
- **Commit ID**: `08a1857b3b7bfaaa7404bf73170fb0e44f3f6c09`
- **作者**: yanann_chen
- **日期**: 2020-04-30 15:24:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 70. [內部]移除不必要的log
- **Commit ID**: `e3a0101b3516c4d2069bd5ac1a0a290e0d05b56b`
- **作者**: cherryliao
- **日期**: 2020-04-30 11:23:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 71. [Web]C01-*********** 優化產品部門開窗增加分頁功能
- **Commit ID**: `75514deb6b221ef6f5c341e773811580e384b413`
- **作者**: waynechang
- **日期**: 2020-04-30 11:04:38
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/OrgUnitListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`

### 72. [Web]C01-*********** 修正問題: 系統排程設定"每週"無效 新增排程時，設定"每週"無效[補]
- **Commit ID**: `882023b70f15790d362562f60b03d068a2032447`
- **作者**: yanann_chen
- **日期**: 2020-04-30 10:53:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`

### 73. [Web]C01-*********** 修正進入模組程式維護頁面，程式的URL中有<和>符號時，點選該列後並儲存，再次進來顯示會異常
- **Commit ID**: `acf75152c746db5d005ebcac586a3f066d7bfc12`
- **作者**: 王鵬程
- **日期**: 2020-04-29 18:53:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`

### 74. [流程引擎]Q00-20200429001 修正5.8版(Oracle)在使用者同時登入時會導致登入後出現請洽系統管理員(WizardAuthority OptimisticLockException)
- **Commit ID**: `b7b9ea47e9fa62a3e80298c9394e5a715458be84`
- **作者**: lorenchang
- **日期**: 2020-04-29 16:25:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/WizardAuthority.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/ToolAuthController.java`

### 75. A00-20200217001 修正T100單據aist310無法拋單到EFGP
- **Commit ID**: `c7594aeed4c5f078321f34a811557ecbb65bd4a8`
- **作者**: 林致帆
- **日期**: 2020-04-28 18:18:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`

### 76. [E10]S00-20200303001 調整E10組織同步內容[補修正]
- **Commit ID**: `8e207b4016b20690442df0e9ed778b2f3f11f06d`
- **作者**: 林致帆
- **日期**: 2020-04-28 18:13:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@e10/syncorg/conf/sync-def_MSSQL.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@e10/syncorg/conf/sync-def_ORACLE.xml`

### 77. [流程引擎]C01-20191223002 修正問題: 主管首頁逾時關卡統計計算錯誤 修正SQL command[補]
- **Commit ID**: `9f3720722dbdbede211925070aaad3501038ac03`
- **作者**: yanann_chen
- **日期**: 2020-04-28 16:06:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.3.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.3.1_DML_Oracle_1.sql`

### 78. [BPM] 修正無法正確辨識副檔名的問題
- **Commit ID**: `f487837bac7c1783d40543b95afe7dbeecef2881`
- **作者**: wusnnn
- **日期**: 2020-04-28 15:05:29
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileExcelImporter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 79. [BPM]ReadExceltoStringFormat支援xlsx格式
- **Commit ID**: `de5c17232f17457b8586084baa7daa1f44c5aea9`
- **作者**: wusnnn
- **日期**: 2020-04-28 14:56:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`

### 80. [Web]A00-20200423001 修正RWD的Grid排序問題
- **Commit ID**: `f9f412ca9e6fd706c222273ae1a8f60153ec59e2`
- **作者**: 王鵬程
- **日期**: 2020-04-28 14:53:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 81. [Web]C01-20200131002 修正 檢視核決權限表型式的關卡頁面未放大最大時，在最下方關卡資訊有資料時卻無法顯示
- **Commit ID**: `2426920ea979f418a23bc1b8ca20c301ce1646c7`
- **作者**: 王鵬程
- **日期**: 2020-04-28 14:15:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`

### 82. [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料
- **Commit ID**: `0ad264bcffd141421b86a933d896394e7f1b7a83`
- **作者**: 林致帆
- **日期**: 2020-04-28 08:42:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgMgr.java`

