# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.9.3_20231017
- **舊版本**: release_5.8.9.3
- **生成時間**: 2025-07-18 10:49:50
- **新增 Commit 數量**: 68

## 變更摘要

### 林致帆 (10 commits)

- **2023-10-12 15:30:00**: [表單設計師]Q00-20231012004 修正表單有textBox髒資料，匯入轉RWD表單匯入失敗
  - 變更檔案: 1 個
- **2023-09-05 10:16:20**: [T100]S00-20220513001 T100取簽核歷程新增是否為代理人標籤
  - 變更檔案: 2 個
- **2023-09-22 10:42:52**: [Web]Q00-20230922002 修正附件名稱帶有單引號，導致附件點擊次數無法增加
  - 變更檔案: 1 個
- **2023-09-19 13:42:07**: [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"[補修正]
  - 變更檔案: 2 個
- **2023-09-18 14:02:51**: [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"
  - 變更檔案: 2 個
- **2023-09-08 09:24:46**: [SAP]Q00-20230908001 調整因欄位值取得異常造成呼叫SAP產品失敗
  - 變更檔案: 1 個
- **2023-08-31 09:16:24**: [Web]Q00-*********** 調整若附件為在線閱覽狀態，在線閱覽開關，也要能下載附件
  - 變更檔案: 3 個
- **2023-08-30 10:43:55**: [TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷
  - 變更檔案: 1 個
- **2023-08-29 15:50:48**: [ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗
  - 變更檔案: 2 個
- **2023-08-28 16:57:53**: [SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤
  - 變更檔案: 1 個

### 邱郁晏 (12 commits)

- **2023-10-04 14:56:04**: [T100] Q00-20231004004 修正手寫元件造成拋單失敗，新增判斷略過手寫元件
  - 變更檔案: 1 個
- **2023-10-11 14:21:33**: [Web] V00-20231011001 修正轉存表單日期格式異常問題，支持常用格式。
  - 變更檔案: 1 個
- **2023-10-04 12:04:19**: [Web] V00-20231004001 修正匯出表單，日期格式(yy/M/d)被判斷為異常格式問題。
  - 變更檔案: 1 個
- **2023-09-21 17:05:57**: [組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號(補修正)
  - 變更檔案: 1 個
- **2023-09-20 13:44:52**: [組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號。
  - 變更檔案: 1 個
- **2023-09-12 10:23:55**: [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法(補)
  - 變更檔案: 1 個
- **2023-09-12 10:06:27**: [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法
  - 變更檔案: 1 個
- **2023-09-12 17:23:00**: [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
  - 變更檔案: 1 個
- **2023-09-04 10:53:42**: [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
  - 變更檔案: 1 個
- **2023-08-31 15:41:41**: [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題
  - 變更檔案: 1 個
- **2023-09-06 12:03:56**: [Web] Q00-20230906001 修正系統通知為自定義URL時，出現異常錯誤，調整寫法並新增錯誤處理機制。
  - 變更檔案: 1 個
- **2023-08-24 13:43:54**: [Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。
  - 變更檔案: 1 個

### yamiyeh10 (3 commits)

- **2023-10-04 14:11:21**: [WEB]Q00-20231004003 修正在手機瀏覽器以及RWD窄畫面上沒有附件檔名的URL連結導致無法下載問題
  - 變更檔案: 2 個
- **2023-09-20 16:38:36**: [DT]Q00-20230920002 修正Web系統權限管理員上儲存按鈕後畫面會一直loading的問題
  - 變更檔案: 1 個
- **2023-09-20 12:01:52**: [DT]A00-20230919001 修正Web流程管理工具中事件處理在流程儲存後會消失問題
  - 變更檔案: 1 個

### cherryliao (4 commits)

- **2023-10-04 10:09:25**: [Web]Q00-20231004001 修正TextBox設定數字轉繁體文字在列印表單時顯示簡體文字的問題
  - 變更檔案: 1 個
- **2023-09-15 10:22:25**: [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題[補]
  - 變更檔案: 1 個
- **2023-09-08 16:39:12**: [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題
  - 變更檔案: 1 個
- **2023-08-30 11:47:29**: [Web]Q00-*********** 調整上傳附件畫面樣式與附件資訊無法呈現的問題
  - 變更檔案: 2 個

### 周权 (12 commits)

- **2023-09-26 14:32:49**: [Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]
  - 變更檔案: 2 個
- **2023-09-25 17:24:13**: [Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。
  - 變更檔案: 2 個
- **2023-08-24 13:39:10**: [Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题[补修正]
  - 變更檔案: 1 個
- **2023-08-24 09:32:17**: [Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题
  - 變更檔案: 1 個
- **2023-09-22 14:57:46**: [Web] Q00-20230922004 在没有添加grid按钮却使用grid方法时，新增防呆，防止focus报错
  - 變更檔案: 1 個
- **2023-09-14 16:07:26**: [Web]Q00-20230914001 修正RadioButton，checkbox元件帶入值到grid时报错，新增防呆。
  - 變更檔案: 1 個
- **2023-09-12 15:29:48**: [Web]Q00-20230912003 编辑或新增系统排程时，增加排程生效时间最大日期卡控设定。
  - 變更檔案: 3 個
- **2023-09-11 15:39:19**: [Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。
  - 變更檔案: 1 個
- **2023-09-08 10:14:12**: [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。[补修正]
  - 變更檔案: 2 個
- **2023-09-07 11:31:32**: [Web]Q00-20230907001 读取自定义background,Banner,logo图片时，新增图片格式防呆。
  - 變更檔案: 1 個
- **2023-09-06 13:19:12**: [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。
  - 變更檔案: 1 個
- **2023-09-05 18:01:25**: [Web]Q00-20230905004 修正关卡无處理人員时，签名图档报错问题，添加防呆。
  - 變更檔案: 1 個

### waynechang (6 commits)

- **2023-10-03 17:39:22**: [SAP]Q00-20231003003 修正SAP整合作業-SAP欄位對應設定，新增整合設定頁面當選擇完表單後，無法載入表單元件
  - 變更檔案: 2 個
- **2023-09-26 16:01:14**: [在線閱覽]Q00-20230926001 修正在線閱讀檔案設定不可下載，在待辦表單頁面點擊閱讀檔案的頁面，仍可以下載PDF閱讀檔的異常
  - 變更檔案: 1 個
- **2023-09-07 10:31:49**: [流程引擎]Q00-20230907002 修正核決關卡內的關卡向後加簽關卡後，又再刪除加簽的關卡時，核決關卡繼續派送時會發生異常
  - 變更檔案: 1 個
- **2023-08-29 16:50:50**: [流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常
  - 變更檔案: 1 個
- **2023-08-29 10:32:40**: [流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據
  - 變更檔案: 2 個
- **2023-08-23 15:27:37**: [Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常
  - 變更檔案: 3 個

### pinchi_lin (8 commits)

- **2023-09-28 17:56:28**: [DT]Q00-20230928003 修正從Web化流程管理工具入版後的流程圖在Swing中開啟時流程圖會被截斷問題
  - 變更檔案: 1 個
- **2023-09-26 17:58:38**: [DT]Q00-20230926002 修正Web流程管理工具儲存流程因找不到ActivityType導致儲存失敗問題
  - 變更檔案: 1 個
- **2023-09-26 17:48:09**: [DT]Q00-20230926003 修正Web流程管理工具的流程圖進版後有條件的連接線顏色變黑色問題
  - 變更檔案: 1 個
- **2023-09-26 17:44:07**: [DT]Q00-20230926004 修正Web流程管理工具的流程圖進版後連接線上的名稱消失問題
  - 變更檔案: 1 個
- **2023-09-21 16:13:03**: [DT]Q00-20230921001 修正Web流程管理工具中參與者選職務或職稱後儲存再開啟其值會消失的問題
  - 變更檔案: 1 個
- **2023-09-18 14:40:32**: [DT]Q00-20230918002 修正Web流程管理工具中連接線編輯後儲存流程再開啟會變成藍色的問題
  - 變更檔案: 1 個
- **2023-09-01 19:47:59**: [DT]A00-20230901001 修正Web流程管理工具中設定流程負責人跟流程逾時儲存後會消失問題
  - 變更檔案: 1 個
- **2023-08-28 11:15:49**: [DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題
  - 變更檔案: 1 個

### 刘旭 (4 commits)

- **2023-09-27 17:19:26**: [web]Q00-20230927003 預覽列印時，頁籤元件顯示文字為白色，但實際列印變成黑色。 问题修复
  - 變更檔案: 1 個
- **2023-09-18 09:24:41**: [web]Q00-20230913001 沒有未閱讀的工作通知，但右上角鈴鐺還是一直有紅點點问题修复
  - 變更檔案: 2 個
- **2023-08-29 14:02:09**: [web]Q00-20230829003 列印時附件資訊會超出邊界问题修复
  - 變更檔案: 1 個
- **2023-08-25 17:38:15**: [web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复
  - 變更檔案: 1 個

### liuyun (8 commits)

- **2023-09-22 11:21:43**: [Web] Q00-20230922001 修正流程管理>流程派送异常处理页面的全选按钮失效
  - 變更檔案: 1 個
- **2023-09-22 10:27:58**: [Web] Q00-20230922003 修正表单设计师>进入响应式表单F12 not found报错
  - 變更檔案: 1 個
- **2023-09-11 16:08:36**: [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】
  - 變更檔案: 2 個
- **2023-09-07 17:54:10**: [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正设定未修改密码强制退出【补修正】
  - 變更檔案: 4 個
- **2023-08-25 15:40:09**: [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面
  - 變更檔案: 3 個
- **2023-09-15 11:09:32**: [Web] Q00-20230915001 修正 SQL注册器点击资料返回到新增页面，数据错误带回显示
  - 變更檔案: 1 個
- **2023-09-11 11:47:04**: [Web] Q00-20230911001 修正 时间元件提示 限制輸入日期或格式yyyy/MM/dd (HH:mm) 错误
  - 變更檔案: 1 個
- **2023-09-06 11:11:10**: [Web] A00-20230904001 修正将HorizontalLine元件设定为invisible隐藏后，上传附件后刷新表单会空白
  - 變更檔案: 1 個

### kmin (1 commits)

- **2023-08-31 14:47:24**: 打包用
  - 變更檔案: 2 個

## 詳細變更記錄

### 1. [表單設計師]Q00-20231012004 修正表單有textBox髒資料，匯入轉RWD表單匯入失敗
- **Commit ID**: `f5b181b0fb37aa98758b066724a737d7a2d0842d`
- **作者**: 林致帆
- **日期**: 2023-10-12 15:30:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 2. [T100] Q00-20231004004 修正手寫元件造成拋單失敗，新增判斷略過手寫元件
- **Commit ID**: `0e06874d13a0247e42e7812e52620c7f27bdf16c`
- **作者**: 邱郁晏
- **日期**: 2023-10-04 14:56:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 3. [WEB]Q00-20231004003 修正在手機瀏覽器以及RWD窄畫面上沒有附件檔名的URL連結導致無法下載問題
- **Commit ID**: `266c5f2638566cd340f0073a429382aea76b4bed`
- **作者**: yamiyeh10
- **日期**: 2023-10-04 14:11:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 4. [Web] V00-20231011001 修正轉存表單日期格式異常問題，支持常用格式。
- **Commit ID**: `873ea01a3965a369c5174a58cf1abbc67680f8d0`
- **作者**: 邱郁晏
- **日期**: 2023-10-11 14:21:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 5. [Web] V00-20231004001 修正匯出表單，日期格式(yy/M/d)被判斷為異常格式問題。
- **Commit ID**: `a4cfae325dcd47ae0219d75a4a203d1cbf4ef777`
- **作者**: 邱郁晏
- **日期**: 2023-10-04 12:04:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 6. [Web]Q00-20231004001 修正TextBox設定數字轉繁體文字在列印表單時顯示簡體文字的問題
- **Commit ID**: `23fa935ff54e6acaffbae18056b1b85cacbee415`
- **作者**: cherryliao
- **日期**: 2023-10-04 10:09:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 7. [Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]
- **Commit ID**: `b0cd56a4c4394613c2121c6b57bbd6e0cd3c0c4f`
- **作者**: 周权
- **日期**: 2023-09-26 14:32:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 8. [Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。
- **Commit ID**: `499f79d8aea409f6381f69ca47585429f375312a`
- **作者**: 周权
- **日期**: 2023-09-25 17:24:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 9. [Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题[补修正]
- **Commit ID**: `12f3c8c72f6ae5c9d8f53c90722be953166a8502`
- **作者**: 周权
- **日期**: 2023-08-24 13:39:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 10. [Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题
- **Commit ID**: `db2688d4318c7af76af0e9bea23c548a629a5461`
- **作者**: 周权
- **日期**: 2023-08-24 09:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 11. [SAP]Q00-20231003003 修正SAP整合作業-SAP欄位對應設定，新增整合設定頁面當選擇完表單後，無法載入表單元件
- **Commit ID**: `1c0857b653f8e3532fbc1d57dae9e6a0b8648f30`
- **作者**: waynechang
- **日期**: 2023-10-03 17:39:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp`

### 12. [DT]Q00-20230928003 修正從Web化流程管理工具入版後的流程圖在Swing中開啟時流程圖會被截斷問題
- **Commit ID**: `f6ff06040667e7f310884134c1b5bff2046574a4`
- **作者**: pinchi_lin
- **日期**: 2023-09-28 17:56:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 13. [web]Q00-20230927003 預覽列印時，頁籤元件顯示文字為白色，但實際列印變成黑色。 问题修复
- **Commit ID**: `fd81e5cec621012c141bd295942f76440117bf07`
- **作者**: 刘旭
- **日期**: 2023-09-27 17:19:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SubTabElement.java`

### 14. [DT]Q00-20230926002 修正Web流程管理工具儲存流程因找不到ActivityType導致儲存失敗問題
- **Commit ID**: `84c40844c933817f27b210d50918399c4b8ef837`
- **作者**: pinchi_lin
- **日期**: 2023-09-26 17:58:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 15. [DT]Q00-20230926003 修正Web流程管理工具的流程圖進版後有條件的連接線顏色變黑色問題
- **Commit ID**: `2aa04710165d3b86229e2fe26d0e61a2aba081a9`
- **作者**: pinchi_lin
- **日期**: 2023-09-26 17:48:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 16. [DT]Q00-20230926004 修正Web流程管理工具的流程圖進版後連接線上的名稱消失問題
- **Commit ID**: `4791c5e879dcab0752914f3990cef0b6080ee193`
- **作者**: pinchi_lin
- **日期**: 2023-09-26 17:44:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 17. [在線閱覽]Q00-20230926001 修正在線閱讀檔案設定不可下載，在待辦表單頁面點擊閱讀檔案的頁面，仍可以下載PDF閱讀檔的異常
- **Commit ID**: `29ab55e8f507ab48ed31050044b2889bc9012ffc`
- **作者**: waynechang
- **日期**: 2023-09-26 16:01:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 18. [T100]S00-20220513001 T100取簽核歷程新增是否為代理人標籤
- **Commit ID**: `243f0dee28fcd78c01dc355e5ea1ebdeea2cd690`
- **作者**: 林致帆
- **日期**: 2023-09-05 10:16:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/newtiptop/model/NewTiptopXmlTag.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`

### 19. [Web] Q00-20230922004 在没有添加grid按钮却使用grid方法时，新增防呆，防止focus报错
- **Commit ID**: `89cd1b054764b60e4d9fc1bd4af233cbda0116e4`
- **作者**: 周权
- **日期**: 2023-09-22 14:57:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 20. [Web] Q00-20230922001 修正流程管理>流程派送异常处理页面的全选按钮失效
- **Commit ID**: `849272149d02c8c88a944b12c8c9f07e07f6bfbb`
- **作者**: liuyun
- **日期**: 2023-09-22 11:21:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp`

### 21. [Web]Q00-20230922002 修正附件名稱帶有單引號，導致附件點擊次數無法增加
- **Commit ID**: `e2a961c69ca53882f902a4b2c5780c0b62f12999`
- **作者**: 林致帆
- **日期**: 2023-09-22 10:42:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 22. [Web] Q00-20230922003 修正表单设计师>进入响应式表单F12 not found报错
- **Commit ID**: `7d7e7b251a1cbb609289e4b2a03c760c508123ad`
- **作者**: liuyun
- **日期**: 2023-09-22 10:27:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 23. [組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號(補修正)
- **Commit ID**: `16cd4611bfd70d7fc616a2c79cf4cf6239800dd0`
- **作者**: 邱郁晏
- **日期**: 2023-09-21 17:05:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

### 24. [DT]Q00-20230921001 修正Web流程管理工具中參與者選職務或職稱後儲存再開啟其值會消失的問題
- **Commit ID**: `f8404cd5b3f7d7cf2bedd01f370900297e73bd4b`
- **作者**: pinchi_lin
- **日期**: 2023-09-21 16:13:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 25. [DT]Q00-20230920002 修正Web系統權限管理員上儲存按鈕後畫面會一直loading的問題
- **Commit ID**: `c505588e3f0b6265145c0da461f5cdfbd0fd9f21`
- **作者**: yamiyeh10
- **日期**: 2023-09-20 16:38:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java`

### 26. [組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號。
- **Commit ID**: `4a4a32ad287ed0d2f080d30b26516b16ab9bd884`
- **作者**: 邱郁晏
- **日期**: 2023-09-20 13:44:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

### 27. [DT]A00-20230919001 修正Web流程管理工具中事件處理在流程儲存後會消失問題
- **Commit ID**: `b77ad2a05e9dd1f4e1cc36a9f2993fc0242c044e`
- **作者**: yamiyeh10
- **日期**: 2023-09-20 12:01:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 28. [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"[補修正]
- **Commit ID**: `ed3c3700c858b38d0c7ea39c4a8d3ae08e846af4`
- **作者**: 林致帆
- **日期**: 2023-09-19 13:42:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 29. [DT]Q00-20230918002 修正Web流程管理工具中連接線編輯後儲存流程再開啟會變成藍色的問題
- **Commit ID**: `11bcd57876342d6dba5b2e9c59d5f2cc20ba1c42`
- **作者**: pinchi_lin
- **日期**: 2023-09-18 14:40:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 30. [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"
- **Commit ID**: `d87cb6df57f8577ed09e5ac875957d9a26f508b3`
- **作者**: 林致帆
- **日期**: 2023-09-18 14:02:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 31. [web]Q00-20230913001 沒有未閱讀的工作通知，但右上角鈴鐺還是一直有紅點點问题修复
- **Commit ID**: `159fcb0154095ae0f17ff8904322b71dbab2cd95`
- **作者**: 刘旭
- **日期**: 2023-09-18 09:24:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 32. [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】
- **Commit ID**: `a74f2c89c9a72bc332b5ce748f2ca0c6e0e24341`
- **作者**: liuyun
- **日期**: 2023-09-11 16:08:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 33. [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正设定未修改密码强制退出【补修正】
- **Commit ID**: `36ba303dce5da0c92ced190573e26c5ab3d576d2`
- **作者**: liuyun
- **日期**: 2023-09-07 17:54:10
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 34. [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面
- **Commit ID**: `d83f57a6e0c67ad479d43287fcbc2e07a80e78d2`
- **作者**: liuyun
- **日期**: 2023-08-25 15:40:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 35. [Web] Q00-20230915001 修正 SQL注册器点击资料返回到新增页面，数据错误带回显示
- **Commit ID**: `0b0eee29550a12feff3945b8fa4a5ebee9608db5`
- **作者**: liuyun
- **日期**: 2023-09-15 11:09:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp`

### 36. [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題[補]
- **Commit ID**: `e5bbbc2bfd84cf10a5a41fdc952a6fb589756a43`
- **作者**: cherryliao
- **日期**: 2023-09-15 10:22:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 37. [Web]Q00-20230914001 修正RadioButton，checkbox元件帶入值到grid时报错，新增防呆。
- **Commit ID**: `8bad3e313c81ff1176821fcf0d2a8e8b7b699bc9`
- **作者**: 周权
- **日期**: 2023-09-14 16:07:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 38. [Web]Q00-20230912003 编辑或新增系统排程时，增加排程生效时间最大日期卡控设定。
- **Commit ID**: `3ce886ded44b84dd6da4ed224f1fe92c53f58751`
- **作者**: 周权
- **日期**: 2023-09-12 15:29:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 39. [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法(補)
- **Commit ID**: `b7af9bb3ab4fbec5ab05bb6d4035cf3f357a4e91`
- **作者**: 邱郁晏
- **日期**: 2023-09-12 10:23:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 40. [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法
- **Commit ID**: `1c6d4f2abad238d9b424702b482757ba602e6687`
- **作者**: 邱郁晏
- **日期**: 2023-09-12 10:06:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 41. [Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。
- **Commit ID**: `2a249f8c450b6fcbc70a198e10e2a75766225daf`
- **作者**: 周权
- **日期**: 2023-09-11 15:39:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`

### 42. [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
- **Commit ID**: `bd50740a44f0ef78967b45dd36f416f141b77b76`
- **作者**: 邱郁晏
- **日期**: 2023-09-12 17:23:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 43. [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
- **Commit ID**: `5ac3b5dec2f5d3ee78d2db6e267b9c9db3f9a5f8`
- **作者**: 邱郁晏
- **日期**: 2023-09-04 10:53:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 44. [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題
- **Commit ID**: `c574ec137870e563b59669ce40cc6ff52a5f5cc3`
- **作者**: 邱郁晏
- **日期**: 2023-08-31 15:41:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 45. [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題
- **Commit ID**: `ad68936dee6a03a4c9f9582304298e76779aa498`
- **作者**: cherryliao
- **日期**: 2023-09-08 16:39:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 46. [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。[补修正]
- **Commit ID**: `5cf50118ad353688beb07bbc27986b8b3ac05330`
- **作者**: 周权
- **日期**: 2023-09-08 10:14:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`

### 47. [SAP]Q00-20230908001 調整因欄位值取得異常造成呼叫SAP產品失敗
- **Commit ID**: `20b08849dc32bb291a1c771427bda3f7a555954b`
- **作者**: 林致帆
- **日期**: 2023-09-08 09:24:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`

### 48. [Web]Q00-20230907001 读取自定义background,Banner,logo图片时，新增图片格式防呆。
- **Commit ID**: `ffb260bbb2c54195929376f607925ad7fc6ccd4f`
- **作者**: 周权
- **日期**: 2023-09-07 11:31:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`

### 49. [流程引擎]Q00-20230907002 修正核決關卡內的關卡向後加簽關卡後，又再刪除加簽的關卡時，核決關卡繼續派送時會發生異常
- **Commit ID**: `7dea21018ff84f649cce05eed470cd4c4a1beb1e`
- **作者**: waynechang
- **日期**: 2023-09-07 10:31:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 50. [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。
- **Commit ID**: `e699a42126c703329ef58e96c2b657ae20675adc`
- **作者**: 周权
- **日期**: 2023-09-06 13:19:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`

### 51. [Web] Q00-20230906001 修正系統通知為自定義URL時，出現異常錯誤，調整寫法並新增錯誤處理機制。
- **Commit ID**: `4a3fe8f78be2406373a59e4933ea3d472d5e0eb6`
- **作者**: 邱郁晏
- **日期**: 2023-09-06 12:03:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java`

### 52. [Web] Q00-20230911001 修正 时间元件提示 限制輸入日期或格式yyyy/MM/dd (HH:mm) 错误
- **Commit ID**: `29767c102033fb3b27fb7a0909f338d0650aee9f`
- **作者**: liuyun
- **日期**: 2023-09-11 11:47:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 53. [Web]Q00-20230905004 修正关卡无處理人員时，签名图档报错问题，添加防呆。
- **Commit ID**: `63bc842520f9344ddd0c495ec22b77270b53536b`
- **作者**: 周权
- **日期**: 2023-09-05 18:01:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 54. [Web] A00-20230904001 修正将HorizontalLine元件设定为invisible隐藏后，上传附件后刷新表单会空白
- **Commit ID**: `4412bd5a36a8723fdb9d2c7b9843ee27bdfe35fe`
- **作者**: liuyun
- **日期**: 2023-09-06 11:11:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 55. [DT]A00-20230901001 修正Web流程管理工具中設定流程負責人跟流程逾時儲存後會消失問題
- **Commit ID**: `a459de8d88f4afe86a63758b5f5fce19e63561bb`
- **作者**: pinchi_lin
- **日期**: 2023-09-01 19:47:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 56. 打包用
- **Commit ID**: `721c74a695e0e0a37765d71963ebca50e5fab6b8`
- **作者**: kmin
- **日期**: 2023-08-31 14:47:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/lib/Json/json.jar`

### 57. [Web]Q00-*********** 調整若附件為在線閱覽狀態，在線閱覽開關，也要能下載附件
- **Commit ID**: `7edf85fb3aaae049bc59fd0591ea66848d1cef12`
- **作者**: 林致帆
- **日期**: 2023-08-31 09:16:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 58. [Web]Q00-*********** 調整上傳附件畫面樣式與附件資訊無法呈現的問題
- **Commit ID**: `48fecbee1144a6d2fe423dd3f4ad4ae8e1f605f5`
- **作者**: cherryliao
- **日期**: 2023-08-30 11:47:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 59. [TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷
- **Commit ID**: `b3892d1623b3636b81e6130827e7d2d13eafc60f`
- **作者**: 林致帆
- **日期**: 2023-08-30 10:43:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 60. [流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常
- **Commit ID**: `9e1a8c44961c5907be02fe8fbdc1b861b7958e60`
- **作者**: waynechang
- **日期**: 2023-08-29 16:50:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 61. [ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗
- **Commit ID**: `b30e78a2b454939864ee2810855877513097fe36`
- **作者**: 林致帆
- **日期**: 2023-08-29 15:50:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 62. [web]Q00-20230829003 列印時附件資訊會超出邊界问题修复
- **Commit ID**: `f72c90f6b7c0d2f7c468a8d0dd2c43ba93369fab`
- **作者**: 刘旭
- **日期**: 2023-08-29 14:02:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 63. [流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據
- **Commit ID**: `feae55e055644d8903d73585988148566eb72137`
- **作者**: waynechang
- **日期**: 2023-08-29 10:32:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ParticipantActivityInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 64. [SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤
- **Commit ID**: `f7d42cd47ebe4f0fa47038313eee19d638b4ee3c`
- **作者**: 林致帆
- **日期**: 2023-08-28 16:57:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`

### 65. [DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題
- **Commit ID**: `c4e8abf77a2d5851011fb9f7df99123b4b5d5713`
- **作者**: pinchi_lin
- **日期**: 2023-08-28 11:15:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 66. [web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复
- **Commit ID**: `9e931eef3588ea08db7b75a46fbc9e1083303403`
- **作者**: 刘旭
- **日期**: 2023-08-25 17:38:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 67. [Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。
- **Commit ID**: `2ce60fb865619dd15ff23dc0db3378472fda363c`
- **作者**: 邱郁晏
- **日期**: 2023-08-24 13:43:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`

### 68. [Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常
- **Commit ID**: `39f901b07448b8ebf85b6a97a2a949267ac915bf`
- **作者**: waynechang
- **日期**: 2023-08-23 15:27:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`

