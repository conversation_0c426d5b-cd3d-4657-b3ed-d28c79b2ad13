{"比較資訊": {"專案ID": "BPM-ISO", "倉庫路徑": "D:\\IDEA_workspace\\BPM-ISO", "新分支": {"branch_name": "release_5.8.10.2", "date": "2024-06-25 15:50:24", "message": "[文件智能家]修正因流程主機位址不是127.0.0.1或localhost導致取得AccessToken失敗，間接導致不會觸發ChatFile接口", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.10.1", "date": "2024-03-26 16:59:30", "message": "[Secudocx] V00-20240326005 修正ISO攜出段，若為PDF加密時，會重複加密導致攜出失敗。", "author": "邱郁晏"}, "比較時間": "2025-07-18 11:45:29", "新增commit數量": 20, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "d79c650cc586f0bfb1bf366392bca60a5f91c69e", "commit_訊息": "[文件智能家]修正因流程主機位址不是127.0.0.1或localhost導致取得AccessToken失敗，間接導致不會觸發ChatFile接口", "提交日期": "2024-06-25 15:50:24", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3e928dc38f230b7ce72ea244f6ea622eb72d189c", "commit_訊息": "[文件智能家]更新表單卡控", "提交日期": "2024-06-20 19:56:54", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreate.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreateManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOMod.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/AccessRightEntityDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/AccessRightEntityController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "04292476d6331047b49b93bec39070443b699a91", "commit_訊息": "[ISO]V00-20240618001 修正文管首頁匯出EXCEL按鈕調整為多語系", "提交日期": "2024-06-18 15:59:39", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "45c1a381750fea8a49959b2f9f87e05760bdc557", "commit_訊息": "[文件智能家]配合表單更新調整js", "提交日期": "2024-06-14 17:58:31", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "021e10e0606ab1e4eefa3d1d0bc9aba9aa7ae6b5", "commit_訊息": "[文件智能家]調用 NaNaXWeb 關聯文件的單向接口增加傳入 docName(補))", "提交日期": "2024-06-14 10:27:09", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocCanceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "67dc47790c18213777656241fad5c82b65e00f31", "commit_訊息": "[文件智能家]調用 NaNaXWeb 關聯文件的單向接口增加傳入 docName", "提交日期": "2024-06-14 10:21:39", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOModifyDocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2ef7ffe5dab88be955aad459cad5e65a9c3fa308", "commit_訊息": "[ISO] V00-20240612006 修正文件類別管理查詢類別名稱異常問題", "提交日期": "2024-06-12 14:11:42", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/DocCategoryDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "030f91c67b8817cb362c1ad027295dfe1657ddd7", "commit_訊息": "[文件智能家]新增模組", "提交日期": "2024-05-24 15:39:38", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreate.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreateManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOMod.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOSearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/SearchCondiction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOAjaxController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocCanceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOModifyDocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "9798d2a9a8addb3d991d531292d69424d9e62108", "commit_訊息": "[ISO] C01-20240531005 修正ISO「文件階層」開窗顯示undefined問題(補)", "提交日期": "2024-06-07 17:15:36", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISODocLevel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISODocType.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "01aa968430e10ba56f7406f96442838c3ae39b00", "commit_訊息": "[ISO] C01-20240531005 修正ISO「文件階層」開窗顯示undefined問題", "提交日期": "2024-06-06 16:52:46", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISODocLevel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be4a32d8713d10c1873b2e022a696923f5f03fdf", "commit_訊息": "[ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件(補)", "提交日期": "2024-05-28 15:10:01", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "18948469b6b40c5bf4fb915bdc462fcfafe393ba", "commit_訊息": "[ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件(補)", "提交日期": "2024-05-28 11:52:40", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7fb03d605ed82f7ac43c2074a2f0d8fc496c5233", "commit_訊息": "[ISO] C01-20240520003 修正只有樹狀文管首頁時，ISO開啟文管首頁對應文件的URL會判斷沒有權限", "提交日期": "2024-05-27 17:28:07", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c90f8698eb2a1398ecac0a3aedb7ffe9483b3b47", "commit_訊息": "[ChatFile]新增取得指定 DocNo 的生效文件接口", "提交日期": "2024-05-22 09:02:50", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/commons-lang3-3.14.0.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "ISOModule/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/chatfile/ChatFileKnowledgeController.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/chatfile/ChatFileKnowledgeService.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "52da5fd0b46846e4e595c98d53b14d71f3efaf7e", "commit_訊息": "[ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件(補)", "提交日期": "2024-05-17 09:17:19", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISODocCmItemDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "37bd1c97c106bf9ee7b697de07d0b46cfd46b20f", "commit_訊息": "[ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件", "提交日期": "2024-05-16 15:27:33", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISODocCmItemDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISODocManagerController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocCanceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "5399c89586441cbb796555b9825436891cbcb66f", "commit_訊息": "[ISO] Q00-20240410001 修正取得未部署成功的文件清單時找不到資料，新增防呆(捕修正)", "提交日期": "2024-04-10 17:14:08", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISODocCmItemDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e1bb187c48d14ead222c220511d18023b4aedce2", "commit_訊息": "[ISO] Q00-20240410001 修正取得未部署成功的文件清單時找不到資料，新增防呆", "提交日期": "2024-04-10 16:44:31", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISODocCmItemDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4db47fff2bb415da99586bcdd020fb248844594f", "commit_訊息": "[ISO] Q00-20240319002 修正ISO文件類別管理，類別筆數超過1000後查詢異常問題(補)", "提交日期": "2024-04-08 10:23:09", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/DocCategoryDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a6809ec9fe2a687b73d1f2bbfefba472f8a41bd6", "commit_訊息": "[ISO]Q00-20240401003 修正ISO文件新增單儲存表單失敗", "提交日期": "2024-04-01 15:44:12", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}