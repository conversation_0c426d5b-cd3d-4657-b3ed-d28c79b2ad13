# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.8.4_20221221
- **舊版本**: release_5.8.8.4
- **生成時間**: 2025-07-18 11:04:14
- **新增 Commit 數量**: 79

## 變更摘要

### cherryliao (5 commits)

- **2022-12-20 16:47:16**: [DT]C01-20221201005 優化Web化資料使用權限管理頁面開啟緩慢問題
  - 變更檔案: 2 個
- **2022-11-18 14:25:40**: [Web]Q00-*********** 修正一般使用者簽核 T100 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊
  - 變更檔案: 5 個
- **2022-11-11 11:07:05**: [Web]Q00-20221111001 調整當使用者session過期時,撈取待辦、通知事項等總數出錯時不往前端拋訊息
  - 變更檔案: 1 個
- **2022-10-26 11:14:26**: [Web]Q00-20221019001 修正響應式表單Grid元件設定凍結欄位時縮放瀏覽器時會出現跑版的問題
  - 變更檔案: 4 個
- **2022-11-16 14:27:02**: [Web]Q00-20221116001 修正開啟流程草稿表單內容都被清空的問題
  - 變更檔案: 1 個

### yamiyeh10 (5 commits)

- **2022-12-14 16:40:17**: [DT]C01-20221214005 修正Web化系統管理工具的系統郵件在Oracle環境下不存在帳號時會頁面異常問題
  - 變更檔案: 1 個
- **2022-11-17 16:01:45**: [DT]C01-*********** 修正Web化系統管理工具流程主機設定在編輯儲存後導致其他使用者登入BPM後顯示空白畫面問題
  - 變更檔案: 1 個
- **2022-11-15 14:02:09**: [Web]Q00-20221111005 修正員工代號有大寫時，使用iReport的列印功能會發生異常問題
  - 變更檔案: 1 個
- **2022-11-03 11:24:45**: [WEB]Q00-20221103001 使用者撤銷流程，理由填空白字串時不允許撤銷流程
  - 變更檔案: 1 個
- **2022-11-01 17:40:10**: [WEB]Q00-20221101005 修正在表單上設定運算規則時有參考單身加總的元件時不會自動觸發更新的問題
  - 變更檔案: 1 個

### waynechang (15 commits)

- **2022-12-12 17:43:31**: [流程引擎]Q00-20221212003 修正併簽流程；若其中一個分支直接退回到分支以前的關卡且流程設定被退回時逐關通知，其他分支執行中關卡也一併被關閉的異常
  - 變更檔案: 1 個
- **2022-12-08 16:37:51**: [流程引擎]Q00-20221208002 修正流程最後一個關卡為服務任務，且系統參數「traceprocess.view.workitem.with.first.activity」設定為false時，系統管理員透過追蹤流程進入流程時，會提示查不到此流程的資料
  - 變更檔案: 1 個
- **2022-12-01 14:06:46**: [流程引擎]Q00-20221201001 修正核決關卡的處理者若符合自動簽核時，核決關卡偶發無法繼續派送下去
  - 變更檔案: 1 個
- **2022-11-17 12:04:11**: [在線閱覽]Q00-*********** 修正在線閱覽開啟檔案的URL，當文件主機設置的WebAddress最後一碼為斜線時需過濾，避免開啟閱讀檔案後，點擊其他BPM功能會被導入登入頁
  - 變更檔案: 1 個
- **2022-11-14 14:31:49**: [WEB]Q00-20221114003 修正5884版本絕對位置表單下載附件異常，無法下載檔案
  - 變更檔案: 1 個
- **2022-11-09 17:27:22**: [流程引擎]Q00-20221109001 調整流程圖點選核決權限關卡，核決關卡改以關卡建立時間排序
  - 變更檔案: 1 個
- **2022-11-08 16:22:44**: [流程引擎]Q00-20221108003 修正流程引擎的加簽函式功能「addCustomParallelAndSerialActivity」，加簽出來的關卡的表單未依照「參考關卡」呈現對應的「表單元件顯示」狀態
  - 變更檔案: 1 個
- **2022-11-04 11:40:39**: [內部]Q00-20221104002 調整觸發自動簽核時間點的log
  - 變更檔案: 1 個
- **2022-11-02 18:07:03**: [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
  - 變更檔案: 1 個
- **2022-10-31 17:41:06**: [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
  - 變更檔案: 1 個
- **2022-10-31 16:23:44**: [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常
  - 變更檔案: 1 個
- **2022-10-28 15:21:40**: [流程引擎]Q00-20221028002 修正Oracle資料庫，若流程有設計執行服務任務並將回傳值回寫至流程變數時，服務任務會報錯的異常
  - 變更檔案: 1 個
- **2022-10-27 15:27:51**: [內部]Q00-*********** 調整PDF8Convert轉檔機制由synchronized改為多執行序執行，並增加debuglog
  - 變更檔案: 2 個
- **2022-10-25 17:29:45**: [流程引擎]Q00-20221025003 調整當核決關卡解析時；若解析人員在同一個組織下有多個兼職部門，且兼職部門的職務核決層級的level都相同時，則以該人員的主部門作為解析部門
  - 變更檔案: 1 個
- **2022-11-11 15:00:49**: [在線閱覽] Q00-20221111002 修正追蹤流程重發新流程，當第一關關卡有設定上傳附件不使用在線閱覽時，上傳附件仍會出現在線閱覽的選項
  - 變更檔案: 1 個

### raven.917 (22 commits)

- **2022-10-25 15:06:58**: [Web]S00-20220711001Textbox元件設置整數及浮點數自動進位輸入值。
  - 變更檔案: 2 個
- **2022-11-23 09:01:05**: [WEB]Q00-20221123001 若tDialogType自定義開窗時，不應產生相應的Script語法。
  - 變更檔案: 1 個
- **2022-11-14 18:21:16**: [Web]Q00-20221114005絕對定位表單及RWD表單，統一可設定背景色設定。
  - 變更檔案: 4 個
- **2022-11-14 12:28:50**: [Web]Q00-20221114002修正表單設計師Barcode元件異常問題。
  - 變更檔案: 1 個
- **2022-11-08 15:25:39**: [Web]Q00-20221108001修正輸入元件設置必填後，沒勾選隱藏標籤原label標籤會出現undefined
  - 變更檔案: 1 個
- **2022-11-07 10:21:46**: [Web]S00-20220818003 修正預設天數上限，最多不可設置超過180日(修正多語系)
  - 變更檔案: 1 個
- **2022-11-07 10:15:56**: [Web]S00-20220818003 修正預設天數上限，最多不可設置超過180日
  - 變更檔案: 3 個
- **2022-11-04 17:32:12**: [Web]S00-20220818003 追蹤流程預設區間出貨為30天 ， 開放給使用者可以設定區間天數，最多不可超過120日。(補修正)
  - 變更檔案: 1 個
- **2022-11-04 17:06:47**: [Web]S00-20220818003 追蹤流程預設區間出貨為30天 ， 開放給使用者可以設定區間天數，最多不可超過120日。
  - 變更檔案: 4 個
- **2022-11-04 15:59:09**: [Web]Q00-20221104004 修正通知關卡指定離職人員時，離職交接人沒有作用。
  - 變更檔案: 1 個
- **2022-11-03 10:37:55**: [Tiptop]Q00-20221031002 修正log沒有辦法正常換日的問題，全部jar替換。
  - 變更檔案: 9 個
- **2022-11-01 16:41:19**: [WEB]Q00-20221028003 補修正Tiptop拋單單身含斷行符號會呈現<br/>(補修正)
  - 變更檔案: 1 個
- **2022-10-28 17:47:45**: [WEB]Q00-20221028003 修正Tiptop拋單單身含斷行符號會呈現<br/>(補修正)
  - 變更檔案: 2 個
- **2022-10-28 17:26:12**: [WEB]Q00-20221028003 修正Tiptop拋單單身含斷行符號會呈現<br/>
  - 變更檔案: 1 個
- **2022-10-28 15:59:37**: [WEB]A00-***********修正新增關卡內-經常選取對象無法第二次選取進清單。(補修正)
  - 變更檔案: 1 個
- **2022-10-28 15:20:12**: [WEB]A00-***********修正新增關卡內-經常選取對象無法第二次選取進清單。
  - 變更檔案: 1 個
- **2022-10-26 16:01:07**: [Web]S00-20220510001新增運算規則可以選取到hidden元件。
  - 變更檔案: 1 個
- **2022-10-25 15:45:35**: [Web]S00-20220720003 修正輸入元件設置必填，隱藏標籤後提示為元件ID。
  - 變更檔案: 2 個
- **2022-10-25 15:25:10**: [Web]Q00-20221006004 上傳附件功能，優化使用者提示，且上傳過程不可點擊關閉按鈕。
  - 變更檔案: 1 個
- **2022-10-25 15:18:30**: [Web]V00-20221019001修正流程管理/監控流程 選擇「已撤銷」流程，匯出Excel發現多了「執行中的關卡」跟「目前處理者」的欄位。
  - 變更檔案: 1 個
- **2022-10-26 14:13:48**: [Web]Q00-20221026002 新增判斷二階快取應確認來源位置是否為本地端(localhost / 127.0.0.1)若是則不須額外清除。
  - 變更檔案: 1 個
- **2022-11-01 12:00:20**: [WEB]Q00-20221101002 修正絕對定位表單SerialNumber元件CSS取到RWD設定
  - 變更檔案: 1 個

### 林致帆 (14 commits)

- **2022-12-09 17:09:53**: [流程引擎]Q00-20221209002 T100拋單若第一關與第二關的建立時間相同，自動簽核選擇與前一關相同簽核者就會無效
  - 變更檔案: 2 個
- **2022-12-05 14:41:15**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 1 個
- **2022-12-05 13:35:27**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 2 個
- **2022-12-01 18:19:35**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能
  - 變更檔案: 2 個
- **2022-11-21 18:02:16**: [流程引擎]Q00-20221121001 修正流程寄信內容是整張表單，且表單元件為浮點數且為空的狀況會派送失敗
  - 變更檔案: 1 個
- **2022-11-18 14:57:29**: [Web]Q00-20221118002 修正附件太多導致往下派送失敗
  - 變更檔案: 1 個
- **2022-11-17 09:23:07**: [流程引擎]Q00-20221117001 修正自動簽核在多人處理關卡上沒有效果
  - 變更檔案: 1 個
- **2022-11-16 14:15:06**: [Web]Q00-20221116002 修正個人資訊頁面載入，因為雙因素認證沒資料導致報錯
  - 變更檔案: 1 個
- **2022-11-14 10:42:08**: [WorkFlow]Q00-20221114001 修正附件URL帶有空格導致拋單敗
  - 變更檔案: 1 個
- **2022-11-03 17:41:40**: [流程引擎]A00-20221103001 修正流程繼續派送後或有通知關卡會重複寄信
  - 變更檔案: 3 個
- **2022-10-31 15:24:48**: [流程設計師]A00-20221026001 修正新增的預設關卡ID如果默認與已經存在的關卡ID一樣，儲存流程時不會異常導致開啟該流程直接報錯
  - 變更檔案: 1 個
- **2022-11-21 15:55:59**: [系統管理工具]A00-20221117001 修正儲存流程因為Application Server位址沒有填上PORT導致失敗
  - 變更檔案: 1 個
- **2022-11-21 15:48:40**: [Web]Q00-20221121002 修正關卡設定附近在線閱覽按鈕不顯示，在流程草稿上傳附件還是會顯示在線閱覽按鈕
  - 變更檔案: 1 個
- **2022-10-26 16:02:29**: [雙因素模組]Q00-20221026005 在未授權時，BPM首頁左側功能列會顯示雙因素模組功能
  - 變更檔案: 1 個

### 謝閔皓 (11 commits)

- **2022-11-19 19:39:49**: [Web]V00-20221020001 修正切換頁面時，若讀取時間較長，會先呈現原畫面再跳轉的問題
  - 變更檔案: 2 個
- **2022-11-17 17:41:12**: [Web]Q00-*********** 修正一般使用者簽核 TIPTOP 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊
  - 變更檔案: 4 個
- **2022-11-16 17:52:27**: [Web]Q00-20221116003 修正 Checkbox、RadioButton 元件，若文字過多造成換行時，勾選按鈕會有偏移的問題
  - 變更檔案: 1 個
- **2022-11-15 15:04:02**: [Web]Q00-20221115002 修正流程設計師/流程模型/進階的主旨範本若有換行，會導致流程資料/流程資料查詢的查詢畫面無法顯示的問題
  - 變更檔案: 1 個
- **2022-11-10 11:40:18**: [Web]Q00-*********** 修正逾期授權的人數也算進總授權數裡
  - 變更檔案: 1 個
- **2022-11-06 16:37:01**: [表單設計師]Q00-20221106001 修正表單設計師中設置輔助格線的貼齊刻度無法暫存修改後的參數
  - 變更檔案: 2 個
- **2022-11-03 18:09:04**: [流程設計師]Q00-20221103003 修正流程定義/事件處理/流程完成/網頁應用程式，第一次點擊編輯時畫面空白的問題
  - 變更檔案: 1 個
- **2022-11-02 14:53:14**: [Web]Q00-*********** 修正checkbox設計時，若有勾選「最後一個選項額外產生輸入框」，表單中checkbox呈現與列印不一致的問題
  - 變更檔案: 1 個
- **2022-11-01 15:24:25**: [Web]Q00-20221101003 修正使用者若有離職作業維護，點選離職人員會跳到登入畫面的問題
  - 變更檔案: 1 個
- **2022-10-27 18:59:08**: [系統管理工具]A00-*********** 若管理員將有組織設計師權限的人員離職，並且移除組織及部門，會導致使用權限設定沒有畫面的問題
  - 變更檔案: 6 個
- **2022-10-26 09:06:05**: [Web]Q00-20221020004 修正 TextBox 元件進階功能的運算規則，若將已綁定的欄位值輸入後又刪除，會顯示 NaN 的問題
  - 變更檔案: 1 個

### 郭哲榮 (6 commits)

- **2022-11-17 10:13:34**: [BPM APP]C01-20221109006 修正移動端Grid元件在不可新增但可編輯與刪除時能看到查看更多按鈕的問題
  - 變更檔案: 2 個
- **2022-11-16 14:40:46**: [BPM APP]C01-20221025006 修正企業微信未進入菜單前從推播進入表單畫面時空白問題
  - 變更檔案: 1 個
- **2022-11-15 18:20:46**: [BPM APP]C01-20220922002 修正移動端主旨與表單內容重疊跟取不到簽核歷程報錯問題
  - 變更檔案: 14 個
- **2022-11-11 17:34:54**: [BPM APP]C01-20221018001 修正移動端Grid元件因換行符號導致無法正常顯示Grid資料的問題
  - 變更檔案: 6 個
- **2022-11-03 18:57:05**: [BPM APP]C01-20220927008 修正移動端Grid顯示畫面上按鈕重疊問題
  - 變更檔案: 1 個
- **2022-10-31 11:47:13**: [BPM APP]C01-20220921001 修正移動端在簽核後兩條流程的表單內容會串單問題
  - 變更檔案: 8 個

### pinchi_lin (1 commits)

- **2022-11-17 10:03:57**: [DT]C01-20221114005 修正Web化系統管理工具TIPTOP整合設定中對映索引修改後沒儲存問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [DT]C01-20221201005 優化Web化資料使用權限管理頁面開啟緩慢問題
- **Commit ID**: `4e08db87d83fc8461069275bbbfe293d27844978`
- **作者**: cherryliao
- **日期**: 2022-12-20 16:47:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java`

### 2. [DT]C01-20221214005 修正Web化系統管理工具的系統郵件在Oracle環境下不存在帳號時會頁面異常問題
- **Commit ID**: `e69be8fc0d82581c5b98b40b8703f4fac1374d7c`
- **作者**: yamiyeh10
- **日期**: 2022-12-14 16:40:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SystemConfigMgr.java`

### 3. [流程引擎]Q00-20221212003 修正併簽流程；若其中一個分支直接退回到分支以前的關卡且流程設定被退回時逐關通知，其他分支執行中關卡也一併被關閉的異常
- **Commit ID**: `f0d728475a9f7cd8431895c6247e959c36151c58`
- **作者**: waynechang
- **日期**: 2022-12-12 17:43:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 4. [Web]S00-20220711001Textbox元件設置整數及浮點數自動進位輸入值。
- **Commit ID**: `36839bfa8628b3f0ae6fa0efdf26640a4194121a`
- **作者**: raven.917
- **日期**: 2022-10-25 15:06:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 5. [流程引擎]Q00-20221208002 修正流程最後一個關卡為服務任務，且系統參數「traceprocess.view.workitem.with.first.activity」設定為false時，系統管理員透過追蹤流程進入流程時，會提示查不到此流程的資料
- **Commit ID**: `8309c54d2bef01917e65a6db4978cd0b060b6010`
- **作者**: waynechang
- **日期**: 2022-12-08 16:37:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 6. [流程引擎]Q00-20221209002 T100拋單若第一關與第二關的建立時間相同，自動簽核選擇與前一關相同簽核者就會無效
- **Commit ID**: `cab91544cc72a2e8fe178c036f8a241d4de5cdf9`
- **作者**: 林致帆
- **日期**: 2022-12-09 17:09:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/WorkItemTimeComparator.java`

### 7. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `21accc56c2b3759f827db319a33e41fdff837fa6`
- **作者**: 林致帆
- **日期**: 2022-12-05 14:41:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 8. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `149a51b01a31f87c69b3c44dfeab84e1bfbdf106`
- **作者**: 林致帆
- **日期**: 2022-12-05 13:35:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 9. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能
- **Commit ID**: `8ca0fbd9695e3ccb487b3ec8b577434c7e9e6d1c`
- **作者**: 林致帆
- **日期**: 2022-12-01 18:19:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 10. [流程引擎]Q00-20221201001 修正核決關卡的處理者若符合自動簽核時，核決關卡偶發無法繼續派送下去
- **Commit ID**: `14fcb152c150a53595f926590bc8f1380f74bf6f`
- **作者**: waynechang
- **日期**: 2022-12-01 14:06:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java`

### 11. [WEB]Q00-20221123001 若tDialogType自定義開窗時，不應產生相應的Script語法。
- **Commit ID**: `b3f2a7f56075a4bbcfba365bf42b1963cefe5538`
- **作者**: raven.917
- **日期**: 2022-11-23 09:01:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`

### 12. [流程引擎]Q00-20221121001 修正流程寄信內容是整張表單，且表單元件為浮點數且為空的狀況會派送失敗
- **Commit ID**: `24ca6eb4cd593538f5111a31bb979d5713a49cbd`
- **作者**: 林致帆
- **日期**: 2022-11-21 18:02:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 13. [Web]V00-20221020001 修正切換頁面時，若讀取時間較長，會先呈現原畫面再跳轉的問題
- **Commit ID**: `2a46c5a8ba4b041d20c5d3101b5be261e2549bb4`
- **作者**: 謝閔皓
- **日期**: 2022-11-19 19:39:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 14. [Web]Q00-20221118002 修正附件太多導致往下派送失敗
- **Commit ID**: `a64e321202c7b075c623cb1859541ceee7478961`
- **作者**: 林致帆
- **日期**: 2022-11-18 14:57:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 15. [Web]Q00-*********** 修正一般使用者簽核 T100 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊
- **Commit ID**: `2c55aa1fd3bff9285475b300011b3c11d9b864f9`
- **作者**: cherryliao
- **日期**: 2022-11-18 14:25:40
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysNewTiptopToolDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopToolBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`

### 16. [Web]Q00-*********** 修正一般使用者簽核 TIPTOP 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊
- **Commit ID**: `a2a9ecf2fd9aa46548d535bf854365697186a2fa`
- **作者**: 謝閔皓
- **日期**: 2022-11-17 17:41:12
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/IPrsMappingKeyDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/OJBPrsMappingKeyDAO.java`

### 17. [DT]C01-*********** 修正Web化系統管理工具流程主機設定在編輯儲存後導致其他使用者登入BPM後顯示空白畫面問題
- **Commit ID**: `f3b770bb2d3f6d91dae4437210050e52f997ae7e`
- **作者**: yamiyeh10
- **日期**: 2022-11-17 16:01:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SystemConfigMgr.java`

### 18. [在線閱覽]Q00-*********** 修正在線閱覽開啟檔案的URL，當文件主機設置的WebAddress最後一碼為斜線時需過濾，避免開啟閱讀檔案後，點擊其他BPM功能會被導入登入頁
- **Commit ID**: `30a4f405b707ae4656df9ea6fcbc5f73ac99535f`
- **作者**: waynechang
- **日期**: 2022-11-17 12:04:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 19. [BPM APP]C01-20221109006 修正移動端Grid元件在不可新增但可編輯與刪除時能看到查看更多按鈕的問題
- **Commit ID**: `fefd5c2cb34e9a17f16a64db8569b14b36cd8200`
- **作者**: 郭哲榮
- **日期**: 2022-11-17 10:13:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 20. [DT]C01-20221114005 修正Web化系統管理工具TIPTOP整合設定中對映索引修改後沒儲存問題
- **Commit ID**: `5d8d71445a2f10293cbd79762f1fb91f51dac800`
- **作者**: pinchi_lin
- **日期**: 2022-11-17 10:03:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/TiptopSystemIntegrationMgr.java`

### 21. [流程引擎]Q00-20221117001 修正自動簽核在多人處理關卡上沒有效果
- **Commit ID**: `36bdee1880be4a0f4b3018fb8a959d490447b216`
- **作者**: 林致帆
- **日期**: 2022-11-17 09:23:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 22. [Web]Q00-20221116003 修正 Checkbox、RadioButton 元件，若文字過多造成換行時，勾選按鈕會有偏移的問題
- **Commit ID**: `b8f4b4453541a2a9b28979b7b816b7848e97cf17`
- **作者**: 謝閔皓
- **日期**: 2022-11-16 17:52:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 23. [BPM APP]C01-20221025006 修正企業微信未進入菜單前從推播進入表單畫面時空白問題
- **Commit ID**: `ea0964c68f4dac4caac828eb364237b566d7ed28`
- **作者**: 郭哲榮
- **日期**: 2022-11-16 14:40:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 24. [Web]Q00-20221116002 修正個人資訊頁面載入，因為雙因素認證沒資料導致報錯
- **Commit ID**: `b11c86b923037dc5c91666369313da06df79d16a`
- **作者**: 林致帆
- **日期**: 2022-11-16 14:15:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 25. [BPM APP]C01-20220922002 修正移動端主旨與表單內容重疊跟取不到簽核歷程報錯問題
- **Commit ID**: `f39465ca13f15565f960fcbf8ee20b0ebd236b34`
- **作者**: 郭哲榮
- **日期**: 2022-11-15 18:20:46
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 26. [Web]Q00-20221115002 修正流程設計師/流程模型/進階的主旨範本若有換行，會導致流程資料/流程資料查詢的查詢畫面無法顯示的問題
- **Commit ID**: `551a8b4af4b76208a5aa93500949abdbd665f238`
- **作者**: 謝閔皓
- **日期**: 2022-11-15 15:04:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/searchFormData/FormInstResultForSearching.java`

### 27. [Web]Q00-20221111005 修正員工代號有大寫時，使用iReport的列印功能會發生異常問題
- **Commit ID**: `57c3456ff5bb5d2d46018050434aa93bc9450345`
- **作者**: yamiyeh10
- **日期**: 2022-11-15 14:02:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`

### 28. [Web]Q00-20221114005絕對定位表單及RWD表單，統一可設定背景色設定。
- **Commit ID**: `a0b09ba6b5e4a52297ff25fd545f3b47b112ee7d`
- **作者**: raven.917
- **日期**: 2022-11-14 18:21:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/LinkElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 29. [WEB]Q00-20221114003 修正5884版本絕對位置表單下載附件異常，無法下載檔案
- **Commit ID**: `7bd8e1c6be8a4de2ab27479bd1137fce1fda873d`
- **作者**: waynechang
- **日期**: 2022-11-14 14:31:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 30. [Web]Q00-20221114002修正表單設計師Barcode元件異常問題。
- **Commit ID**: `cace94c2fe820f003d2fd9b5bedded1fce08eda4`
- **作者**: raven.917
- **日期**: 2022-11-14 12:28:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 31. [WorkFlow]Q00-20221114001 修正附件URL帶有空格導致拋單敗
- **Commit ID**: `c06a488ce947eef80574dd70859d14b452dac475`
- **作者**: 林致帆
- **日期**: 2022-11-14 10:42:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/util/TiptopUtil.java`

### 32. [BPM APP]C01-20221018001 修正移動端Grid元件因換行符號導致無法正常顯示Grid資料的問題
- **Commit ID**: `aa8dbbe7ef556ba95a7db183f5d4f03fa802a90d`
- **作者**: 郭哲榮
- **日期**: 2022-11-11 17:34:54
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 33. [Web]Q00-20221111001 調整當使用者session過期時,撈取待辦、通知事項等總數出錯時不往前端拋訊息
- **Commit ID**: `5846a40d1a707fc319ab804069734d00d195d80b`
- **作者**: cherryliao
- **日期**: 2022-11-11 11:07:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 34. [Web]Q00-*********** 修正逾期授權的人數也算進總授權數裡
- **Commit ID**: `acbc5a2795816ada5acaabcb14fd54828d38114a`
- **作者**: 謝閔皓
- **日期**: 2022-11-10 11:40:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java`

### 35. [流程引擎]Q00-20221109001 調整流程圖點選核決權限關卡，核決關卡改以關卡建立時間排序
- **Commit ID**: `7ada799a6b11a39b30de631e1c3386a6300accb4`
- **作者**: waynechang
- **日期**: 2022-11-09 17:27:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`

### 36. [流程引擎]Q00-20221108003 修正流程引擎的加簽函式功能「addCustomParallelAndSerialActivity」，加簽出來的關卡的表單未依照「參考關卡」呈現對應的「表單元件顯示」狀態
- **Commit ID**: `0ea3e8f32a1f88233795dfe16d4108978e43d4fc`
- **作者**: waynechang
- **日期**: 2022-11-08 16:22:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 37. [Web]Q00-20221108001修正輸入元件設置必填後，沒勾選隱藏標籤原label標籤會出現undefined
- **Commit ID**: `c63012432d0225424e4c3dbea899b7e44acb4ef2`
- **作者**: raven.917
- **日期**: 2022-11-08 15:25:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 38. [Web]S00-20220818003 修正預設天數上限，最多不可設置超過180日(修正多語系)
- **Commit ID**: `ea49cc2c6bd451e4d6a33c4813cde057b00855c7`
- **作者**: raven.917
- **日期**: 2022-11-07 10:21:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 39. [Web]S00-20220818003 修正預設天數上限，最多不可設置超過180日
- **Commit ID**: `0b638db52c710f0de3d7cdacae7035fbe2febc41`
- **作者**: raven.917
- **日期**: 2022-11-07 10:15:56
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_Oracle_1.sql`

### 40. [表單設計師]Q00-20221106001 修正表單設計師中設置輔助格線的貼齊刻度無法暫存修改後的參數
- **Commit ID**: `f08d120064769a64acd928a602cb0a34b258564f`
- **作者**: 謝閔皓
- **日期**: 2022-11-06 16:37:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`

### 41. [Web]S00-20220818003 追蹤流程預設區間出貨為30天 ， 開放給使用者可以設定區間天數，最多不可超過120日。(補修正)
- **Commit ID**: `f675ffb4677941cd5b8c95f1d495e3f30749d4f0`
- **作者**: raven.917
- **日期**: 2022-11-04 17:32:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`

### 42. [Web]S00-20220818003 追蹤流程預設區間出貨為30天 ， 開放給使用者可以設定區間天數，最多不可超過120日。
- **Commit ID**: `4f9afd5c868a813901192d7164c405a4c56bbe63`
- **作者**: raven.917
- **日期**: 2022-11-04 17:06:47
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_Oracle_1.sql`

### 43. [Web]Q00-20221104004 修正通知關卡指定離職人員時，離職交接人沒有作用。
- **Commit ID**: `ddc8286c4f162d300aca7a9361b7fccd27177010`
- **作者**: raven.917
- **日期**: 2022-11-04 15:59:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 44. [內部]Q00-20221104002 調整觸發自動簽核時間點的log
- **Commit ID**: `33ab9934d8e1849cf0c30642339c42c55e854354`
- **作者**: waynechang
- **日期**: 2022-11-04 11:40:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java`

### 45. [BPM APP]C01-20220927008 修正移動端Grid顯示畫面上按鈕重疊問題
- **Commit ID**: `a54cfacb2f7eaa1f2754a5e706331a2607d62cbb`
- **作者**: 郭哲榮
- **日期**: 2022-11-03 18:57:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css`

### 46. [流程設計師]Q00-20221103003 修正流程定義/事件處理/流程完成/網頁應用程式，第一次點擊編輯時畫面空白的問題
- **Commit ID**: `a3d600a41013c5a14d84b3bc31779a85a6036c03`
- **作者**: 謝閔皓
- **日期**: 2022-11-03 18:09:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/FormalParametersCellEditorRenderer.java`

### 47. [流程引擎]A00-20221103001 修正流程繼續派送後或有通知關卡會重複寄信
- **Commit ID**: `bf5499db5c93ab7115b4605b5812a50007571a6a`
- **作者**: 林致帆
- **日期**: 2022-11-03 17:41:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 48. [WEB]Q00-20221103001 使用者撤銷流程，理由填空白字串時不允許撤銷流程
- **Commit ID**: `0c707a452bcc259eb0ecba0e1354f23348bee124`
- **作者**: yamiyeh10
- **日期**: 2022-11-03 11:24:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`

### 49. [Tiptop]Q00-20221031002 修正log沒有辦法正常換日的問題，全部jar替換。
- **Commit ID**: `cc4ac54b5a45e3fa3122c13f456bec43a23716b4`
- **作者**: raven.917
- **日期**: 2022-11-03 10:37:55
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/lib/CruiseControl/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Log4J/log4j.jar`

### 50. [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
- **Commit ID**: `6a4bb6bffd198b538d19fd0bf94357d3692ca3c9`
- **作者**: waynechang
- **日期**: 2022-11-02 18:07:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 51. [Web]Q00-*********** 修正checkbox設計時，若有勾選「最後一個選項額外產生輸入框」，表單中checkbox呈現與列印不一致的問題
- **Commit ID**: `d4d03c1e1ba992fdd7df784a6deba949bd8541a2`
- **作者**: 謝閔皓
- **日期**: 2022-11-02 14:53:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 52. [WEB]Q00-20221101005 修正在表單上設定運算規則時有參考單身加總的元件時不會自動觸發更新的問題
- **Commit ID**: `d9388ed93e5018361c80c7a40ba7ed2ef35178a1`
- **作者**: yamiyeh10
- **日期**: 2022-11-01 17:40:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 53. [WEB]Q00-20221028003 補修正Tiptop拋單單身含斷行符號會呈現<br/>(補修正)
- **Commit ID**: `6b159215c2fde8f7ed7c73b510678fc63a04da0f`
- **作者**: raven.917
- **日期**: 2022-11-01 16:41:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 54. [Web]Q00-20221101003 修正使用者若有離職作業維護，點選離職人員會跳到登入畫面的問題
- **Commit ID**: `246d1ec0f14148c933baa369488376d6c2ab6588`
- **作者**: 謝閔皓
- **日期**: 2022-11-01 15:24:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`

### 55. [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
- **Commit ID**: `c90a418e46a8f7dd33c955aeb9179ab2802445a5`
- **作者**: waynechang
- **日期**: 2022-10-31 17:41:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 56. [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常
- **Commit ID**: `91fee61a0899a6dca7ad626d5dfe67c49cde6696`
- **作者**: waynechang
- **日期**: 2022-10-31 16:23:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 57. [流程設計師]A00-20221026001 修正新增的預設關卡ID如果默認與已經存在的關卡ID一樣，儲存流程時不會異常導致開啟該流程直接報錯
- **Commit ID**: `c81a4596e44c081a30e19479bb697f5a9eb68cea`
- **作者**: 林致帆
- **日期**: 2022-10-31 15:24:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNFactory.java`

### 58. [BPM APP]C01-20220921001 修正移動端在簽核後兩條流程的表單內容會串單問題
- **Commit ID**: `ac66e78c85cb8a29f55e41e77f0cdc8eb6611d70`
- **作者**: 郭哲榮
- **日期**: 2022-10-31 11:47:13
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 59. [WEB]Q00-20221028003 修正Tiptop拋單單身含斷行符號會呈現<br/>(補修正)
- **Commit ID**: `b44b02abc330cfaf9b1292a6b653a1b1167d109e`
- **作者**: raven.917
- **日期**: 2022-10-28 17:47:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 60. [WEB]Q00-20221028003 修正Tiptop拋單單身含斷行符號會呈現<br/>
- **Commit ID**: `d7e4743d51b68d0ded7d90780745b9c0ca47fe28`
- **作者**: raven.917
- **日期**: 2022-10-28 17:26:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 61. [WEB]A00-***********修正新增關卡內-經常選取對象無法第二次選取進清單。(補修正)
- **Commit ID**: `0eb2d3d8ae2224ea746588a8fa77ac499cc0c0ec`
- **作者**: raven.917
- **日期**: 2022-10-28 15:59:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 62. [流程引擎]Q00-20221028002 修正Oracle資料庫，若流程有設計執行服務任務並將回傳值回寫至流程變數時，服務任務會報錯的異常
- **Commit ID**: `c334dcb4b409cce073207bcb31d09ac273e44ed0`
- **作者**: waynechang
- **日期**: 2022-10-28 15:21:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 63. [WEB]A00-***********修正新增關卡內-經常選取對象無法第二次選取進清單。
- **Commit ID**: `ab0485d9d9576cb5b04392e7164d5167b07ba8cb`
- **作者**: raven.917
- **日期**: 2022-10-28 15:20:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 64. [系統管理工具]A00-*********** 若管理員將有組織設計師權限的人員離職，並且移除組織及部門，會導致使用權限設定沒有畫面的問題
- **Commit ID**: `dd219371af216d496a59f94bb90c04080f02dddd`
- **作者**: 謝閔皓
- **日期**: 2022-10-27 18:59:08
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WizardAuthorityManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/WizardAuthorityManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/OrgWizardAuthorityScopeController.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/toolauth/OrgAuthConfPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java`

### 65. [內部]Q00-*********** 調整PDF8Convert轉檔機制由synchronized改為多執行序執行，並增加debuglog
- **Commit ID**: `921f9374b88cad164defc3643fb434fc67bff34e`
- **作者**: waynechang
- **日期**: 2022-10-27 15:27:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java`

### 66. [Web]S00-20220510001新增運算規則可以選取到hidden元件。
- **Commit ID**: `58a242d861255d407187cd1e525b9d352f1cce7d`
- **作者**: raven.917
- **日期**: 2022-10-26 16:01:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 67. [Web]Q00-20221019001 修正響應式表單Grid元件設定凍結欄位時縮放瀏覽器時會出現跑版的問題
- **Commit ID**: `a835acea7ef36e5938d9c96ecd638af797d26ce2`
- **作者**: cherryliao
- **日期**: 2022-10-26 11:14:26
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-fixed-columns-1.18.3.js`

### 68. [Web]Q00-20221020004 修正 TextBox 元件進階功能的運算規則，若將已綁定的欄位值輸入後又刪除，會顯示 NaN 的問題
- **Commit ID**: `c2b231bb1f3e6671896ede09063354a8106983d0`
- **作者**: 謝閔皓
- **日期**: 2022-10-26 09:06:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 69. [流程引擎]Q00-20221025003 調整當核決關卡解析時；若解析人員在同一個組織下有多個兼職部門，且兼職部門的職務核決層級的level都相同時，則以該人員的主部門作為解析部門
- **Commit ID**: `75386735ef4e4aa14c63749eea9a40ba21a9a2f3`
- **作者**: waynechang
- **日期**: 2022-10-25 17:29:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 70. [Web]S00-20220720003 修正輸入元件設置必填，隱藏標籤後提示為元件ID。
- **Commit ID**: `34e1a626e5cdac6361bcaf053f5d9613b9aeea03`
- **作者**: raven.917
- **日期**: 2022-10-25 15:45:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 71. [Web]Q00-20221006004 上傳附件功能，優化使用者提示，且上傳過程不可點擊關閉按鈕。
- **Commit ID**: `fc3a5d88dae712787062c2018347e6d2d73e7034`
- **作者**: raven.917
- **日期**: 2022-10-25 15:25:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 72. [Web]V00-20221019001修正流程管理/監控流程 選擇「已撤銷」流程，匯出Excel發現多了「執行中的關卡」跟「目前處理者」的欄位。
- **Commit ID**: `927627b7b61f8622f56c59da50939a5a31b22e2f`
- **作者**: raven.917
- **日期**: 2022-10-25 15:18:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 73. [系統管理工具]A00-20221117001 修正儲存流程因為Application Server位址沒有填上PORT導致失敗
- **Commit ID**: `16e41849a9f1ffad6ccef7fe9cb2231eff7c9205`
- **作者**: 林致帆
- **日期**: 2022-11-21 15:55:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`

### 74. [Web]Q00-20221026002 新增判斷二階快取應確認來源位置是否為本地端(localhost / 127.0.0.1)若是則不須額外清除。
- **Commit ID**: `304cbdb38e6e3103a55fc1b86794a25b5542a7ab`
- **作者**: raven.917
- **日期**: 2022-10-26 14:13:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`

### 75. [Web]Q00-20221121002 修正關卡設定附近在線閱覽按鈕不顯示，在流程草稿上傳附件還是會顯示在線閱覽按鈕
- **Commit ID**: `d1c01f6a8a387e635780e90b0b984ba092c86cb2`
- **作者**: 林致帆
- **日期**: 2022-11-21 15:48:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`

### 76. [在線閱覽] Q00-20221111002 修正追蹤流程重發新流程，當第一關關卡有設定上傳附件不使用在線閱覽時，上傳附件仍會出現在線閱覽的選項
- **Commit ID**: `67df0bcb71552b497582364a54963bf575a51ed2`
- **作者**: waynechang
- **日期**: 2022-11-11 15:00:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`

### 77. [Web]Q00-20221116001 修正開啟流程草稿表單內容都被清空的問題
- **Commit ID**: `c3d835633c7eaa3dbb96f0764cb611bbb2eee4ff`
- **作者**: cherryliao
- **日期**: 2022-11-16 14:27:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 78. [WEB]Q00-20221101002 修正絕對定位表單SerialNumber元件CSS取到RWD設定
- **Commit ID**: `6b431c5cc12359ecca76a910f7c08f85231508e3`
- **作者**: raven.917
- **日期**: 2022-11-01 12:00:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 79. [雙因素模組]Q00-20221026005 在未授權時，BPM首頁左側功能列會顯示雙因素模組功能
- **Commit ID**: `4d35f4a1cd4ef39a9944bb61ef97566d601207b9`
- **作者**: 林致帆
- **日期**: 2022-10-26 16:02:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`

