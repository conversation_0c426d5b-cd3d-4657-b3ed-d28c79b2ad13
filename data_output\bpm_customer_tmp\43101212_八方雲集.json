{"company_id": "43101212", "company_name": "八方雲集", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\43101212_八方雲集\\01客戶基本資料", "files": [{"filename": "連線資訊.txt", "raw_content": "雲管家\r\n\r\nAP：\r\nEFGP正式機、ESS正式機*************\r\n網頁帳密：要跟客戶要\r\nEFGP測試機、ESS測試機*************\r\n(administrator/1234)\r\n\r\nDB：\r\nEFGP正式機、EFGP測試機同一台192.168.0.241\r\nsa/sql#DSC\r\n\r\n外網：\r\nhttp://efgp.8way.com.tw:8086/NaNaWeb\r\n\r\n聯繫人：李威廷\r\n \r\nTEL:02-8809-8898分機8829\r\n\r\n\r\n曹耀中\r\nTEL:02-8809-8898分機8832\r\n\r\n\r\n", "structured_data": {"網頁帳密": "要跟客戶要", "聯繫人": "李威廷", "http": "//efgp.8way.com.tw:8086/NaNaWeb", "tel": "02-8809-8898分機8832"}, "source_path": "C1.客戶維護相關\\43101212_八方雲集\\01客戶基本資料\\連線資訊.txt", "file_size": 332, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:25.583245"}, {"filename": "[八方雲集]連線資訊.txt", "raw_content": "web vpn：https://**************\r\n帳號：tiptop3\r\n密碼：tiptop#DSC\r\n \r\n機：*************\r\n機：*************\r\n\r\n劉經理287\r\n林經理892\r\n---------------------------------------------\r\nhttps://**************:10443/\r\n\r\nSSL VPN ip:**************\r\nCustomize port: 10443\r\n帳號:tiptop1.tiptop2.tiptop3\r\n密碼：tiptop#DSC\r\n\r\nIP *************\r\nadministrator / admin#DSC\r\n\r\nhttp://*************:8086/NaNaWeb/\r\n\r\n\r\n正式機*************\r\nhttp://*************:8086/NaNaWeb\r\nadministrator / admin#DSC\r\n\r\n測試機*************\r\nhttp://*************:8086/NaNaWeb\r\nadministrator / admin#DSC\r\n", "structured_data": {"web vpn": "https://**************", "username": "tiptop1.tiptop2.tiptop3", "password": "tiptop#DSC", "機": "*************", "web vpn：https": "//**************", "https": "//**************:10443/", "ssl vpn ip": "**************", "customize port": "10443", "http": "//*************:8086/NaNaWeb", "host": "**************", "port": "10443"}, "source_path": "C1.客戶維護相關\\43101212_八方雲集\\01客戶基本資料\\[八方雲集]連線資訊.txt", "file_size": 566, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:25.613271"}], "total_files": 2, "processed_at": "2025-08-26T10:46:25.613280"}