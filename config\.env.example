# BPM Easy Tools 環境變數配置檔案
# 複製此檔案為 .env 並修改相應的設定值

# ===== 部署索引生成器設定 =====
# --- 必填參數 ---
# 本次要建立索引的版本名稱（例如：5.8.10.4）
VERSION=8.1.1.2

# 掃描的目錄
# 範例：F:\Release\wildfly\standalone\deployments or G:\Release\base\components\@base
SCAN_PATHS=F:\Release\wildfly\standalone\deployments

# --- 選填參數 ---

# 僅包含指定的 WAR 檔，名稱以分號「;」分隔，例如：NaNaWeb.war;ISOModule.war
INCLUDE_WARS=NaNaWeb.war;ISOModule.war

# 僅包含指定的 EAR 檔，名稱以分號「;」分隔，例如：nana-app.ear
INCLUDE_EARS=nana-app.ear

# 僅索引指定 WAR 中的 jar 檔，名稱以分號「;」分隔，例如：nana-services-client.jar;nana-server-delegate.jar
INCLUDE_WAR_JARS=nana-services-client.jar;nana-server-delegate.jar

# 僅索引指定 EAR 中的 jar 檔，名稱以分號「;」分隔，例如：nana-services-server.jar
INCLUDE_EAR_JARS=nana-services-server.jar

# 若設定為 true，將強制重新建立索引，即使 index.json 已存在
FORCE_REBUILD=true

# 指定 WAR 檔中額外要索引的資料夾，用逗號分隔（會自動索引其中的 .jsp/.js 檔案）
# 範例：WMS,OpenWin,js
WAR_EXTRA_DIRS=WMS,OpenWin,js
