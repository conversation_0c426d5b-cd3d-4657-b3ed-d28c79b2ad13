# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.5.1
- **舊版本**: 5.6.4.2_1
- **生成時間**: 2025-07-28 18:15:04
- **新增 Commit 數量**: 158

## 變更摘要

### joseph (19 commits)

- **2017-10-24 18:31:12**: 修正 : JSON資料開窗 ,查詢功能輸入數字，會查不到資料的問題
  - 變更檔案: 1 個
- **2017-10-24 18:25:55**: 修正   關鍵事件開窗點選資料無法帶入欄位
  - 變更檔案: 1 個
- **2017-10-24 18:23:57**: C01-20170914001 2次修正 :WEB表单设计师控件跟着鼠标跑
  - 變更檔案: 1 個
- **2017-10-20 17:30:34**: 更換 排程log層級
  - 變更檔案: 1 個
- **2017-10-20 17:28:38**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-10-20 15:38:58**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-10-20 15:38:35**: C01-20170914001  修正 :WEB表单设计师控件跟着鼠标跑
  - 變更檔案: 1 個
- **2017-10-19 13:59:14**: 2次修改 關鍵事件 ORACLE CREATESQL
  - 變更檔案: 1 個
- **2017-10-19 13:57:40**: 修正 關注事件ORACLE createSQL
  - 變更檔案: 1 個
- **2017-10-18 11:27:12**: 修改 import js的路徑以防不同地方引用時出錯
  - 變更檔案: 1 個
- **2017-10-18 10:26:06**: 新增 JSON格式資料開窗分頁查詢功能
  - 變更檔案: 1 個
- **2017-10-18 10:21:44**: 因ESS整合問題 將Template固定為IE8
  - 變更檔案: 1 個
- **2017-10-16 14:07:08**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-10-16 14:06:50**: 移除 log
  - 變更檔案: 1 個
- **2017-10-16 14:02:35**: 修正  更新表單資料SQL語法
  - 變更檔案: 1 個
- **2017-10-16 12:04:45**: 新增 Restful SysintegerationServer資料Response物件
  - 變更檔案: 1 個
- **2017-10-13 16:15:07**: 新增 DotJ Restful接口及修改接口授權驗證機制
  - 變更檔案: 3 個
- **2017-10-13 15:55:45**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-10-13 15:55:24**: 修改系統排程log機制
  - 變更檔案: 1 個

### MiYu (32 commits)

- **2017-10-24 13:49:55**: 將log刪除
  - 變更檔案: 1 個
- **2017-10-24 13:46:24**: 調整資料格式(多表單) 調整userFormValue格式
  - 變更檔案: 7 個
- **2017-10-20 15:47:04**: 調整資料格式(多表單)
  - 變更檔案: 6 個
- **2017-10-20 11:48:06**: 調整RESTFul服務 取得待辦事項表單資料(controller與manage)
  - 變更檔案: 3 個
- **2017-10-19 11:56:51**: A00-20171017002 修正自带小數點保留功能時BPM APP會發生錯誤問題
  - 變更檔案: 4 個
- **2017-10-16 13:36:48**: C01-*********** 修正BPMAPP絕對位置畫面顯示不完全問題
  - 變更檔案: 1 個
- **2017-10-13 17:52:15**: 補上鼎慧的查詢聯絡人bySQL註冊器
  - 變更檔案: 1 個
- **2017-10-13 09:55:06**: 調整Oracle的RemoteData改為512
  - 變更檔案: 2 個
- **2017-10-12 18:21:41**: C01-20171010001 修正加簽的進階查詢第二次查詢才有動作問題
  - 變更檔案: 3 個
- **2017-10-12 17:46:59**: 新增查詢聯絡人SQL註冊器 新增SQL註冊器資料庫存取 多語系調整-流程圖示
  - 變更檔案: 10 個
- **2017-10-03 18:11:00**: 調整工作通知表單取表單的追蹤流程圖路徑
  - 變更檔案: 1 個
- **2017-10-03 17:53:18**: BPM APP表單畫面樣式還原 1.icon樣式還原 2.遮罩顏色加深 3.提示文字字型加大
  - 變更檔案: 25 個
- **2017-10-03 15:27:13**: 調整RESTful架構(Process bean)
  - 變更檔案: 2 個
- **2017-10-03 15:11:32**: 調整RESTful服務(controller 增加Mobile系列)
  - 變更檔案: 6 個
- **2017-10-03 14:52:21**: 調整RESTful服務(controller 增加MobileSystem)
  - 變更檔案: 2 個
- **2017-10-03 14:26:53**: 補上漏掉的Form bean 調整共用bean不放在mobile內
  - 變更檔案: 10 個
- **2017-10-03 13:51:38**: 調整RESTful架構(Form bean)
  - 變更檔案: 21 個
- **2017-10-03 11:23:51**: 調整RESTful架構(System bean)
  - 變更檔案: 8 個
- **2017-10-03 11:22:05**: 調整RESTful架構(controller)
  - 變更檔案: 10 個
- **2017-10-03 09:55:08**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-10-03 08:45:29**: 調整System的URI
  - 變更檔案: 1 個
- **2017-10-02 10:30:01**: 調整通知事項查詢服務
  - 變更檔案: 2 個
- **2017-09-30 18:30:14**: 新增通知事項查詢服務 調整產生表單邏輯
  - 變更檔案: 7 個
- **2017-09-30 11:05:34**: 調整流程圖示多語系 調整產生json文本增加參數
  - 變更檔案: 4 個
- **2017-09-29 08:43:54**: 新增鼎慧二期服務 1.獲取聯絡人資訊 補上漏掉UserListReader
  - 變更檔案: 6 個
- **2017-09-29 08:36:08**: 新增restful服務 1.取得聯絡人資訊 調整UserListReader多增加取得組織與職務名稱
  - 變更檔案: 5 個
- **2017-09-27 09:03:39**: 修正鼎慧二期列表異常 1.修正待辦已簽核關鍵提示資訊欄位為空 調整前端行事曆提醒功能
  - 變更檔案: 2 個
- **2017-09-27 08:53:18**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2017-09-27 08:52:50**: 調整鼎慧二期列表
  - 變更檔案: 1 個
- **2017-09-26 11:51:46**: 修正鼎慧二期列表異常 1.調整關注資訊欄位若無資料值預設給空值 2.調整部分註解與空格 3.調整待辦的流程發起時間與結束時間配合鼎慧
  - 變更檔案: 2 個
- **2017-09-25 16:42:20**: 修正鼎慧二期列表異常 1.修正流程圖示在追蹤流程無法找到對應的ProcessDefId 2.調整部分註解
  - 變更檔案: 1 個
- **2017-09-22 11:48:52**: 修正HtmlFormBuilderJSON的restful服務邏輯 調整WorkItemForListBean空格
  - 變更檔案: 3 個

### ChinRong (30 commits)

- **2017-10-24 09:07:34**: 新增取得系統多語系的RESTful服務
  - 變更檔案: 4 個
- **2017-10-23 11:46:57**: 修正微信使用者頁面與鼎捷移動使用者頁面的問題
  - 變更檔案: 2 個
- **2017-10-20 19:10:54**: 調整鼎捷移動部署網址產生工具
  - 變更檔案: 10 個
- **2017-10-19 11:37:41**: 調整鼎捷移動部份功能
  - 變更檔案: 4 個
- **2017-10-16 18:20:52**: 新增 getWeChatAccessToken, getWeChatAccountByCode的RESTful服務
  - 變更檔案: 5 個
- **2017-10-16 13:53:22**: 打開鼎捷移動中間層表單的部份
  - 變更檔案: 5 個
- **2017-10-12 16:50:36**: 新增getMobileOAuthWeChatByWeChatID的RESTful服務
  - 變更檔案: 2 個
- **2017-10-12 16:01:31**: 將鼎捷移動圓餅圖取資料的邏輯搬到BAMServerMgr中
  - 變更檔案: 3 個
- **2017-10-12 09:12:41**: 新增鼎捷移動圓餅圖-待處理工作百分比
  - 變更檔案: 2 個
- **2017-10-11 16:50:52**: 新增鼎捷移動圖表組件-圓餅圖-待辦未完成工作
  - 變更檔案: 2 個
- **2017-10-06 18:10:25**: 新增圓餅圖用到的java bean
  - 變更檔案: 4 個
- **2017-10-06 17:35:23**: 調整鼎捷移動部署工具
  - 變更檔案: 5 個
- **2017-10-02 18:16:19**: 新增取得微信使用者認證資料RESTful服務
  - 變更檔案: 5 個
- **2017-10-02 11:53:04**: 調整鼎慧簽核currentState參數
  - 變更檔案: 1 個
- **2017-10-02 11:16:07**: BPM App RESTful服務參數 currentState參數格式統一
  - 變更檔案: 4 個
- **2017-09-30 18:18:06**: 調整流程緊急度格式為數字25,50,75
  - 變更檔案: 4 個
- **2017-09-30 17:21:22**: 調整發起流程/草稿發起資料查詢的表單資料格式
  - 變更檔案: 2 個
- **2017-09-30 15:06:26**: 新增發起流程,草稿發起資料查詢服務
  - 變更檔案: 5 個
- **2017-09-29 17:20:44**: 新增發起流程資料查詢會用到的java bean
  - 變更檔案: 10 個
- **2017-09-29 09:00:55**: 修正鼎慧列表問題
  - 變更檔案: 2 個
- **2017-09-28 16:14:31**: 調整鼎慧二期進階查詢RESTful路徑
  - 變更檔案: 1 個
- **2017-09-27 18:49:37**: 鼎慧二期列表篩選增加"所有"選項
  - 變更檔案: 3 個
- **2017-09-26 17:55:23**: 調整鼎慧二期直連表單格式
  - 變更檔案: 1 個
- **2017-09-26 15:12:41**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-09-26 15:12:25**: 調整鼎慧二期列表
  - 變更檔案: 1 個
- **2017-09-26 10:07:09**: 調整鼎慧二期追蹤、通知列表進階查詢部份
  - 變更檔案: 2 個
- **2017-09-26 09:54:12**: 還原待辦事項關鍵資訊只有異常流程單據才取出來的判斷
  - 變更檔案: 1 個
- **2017-09-25 16:13:37**: 修正鼎慧二期列表異常
  - 變更檔案: 2 個
- **2017-09-22 16:31:32**: 新增進階查詢流程名稱過濾功能
  - 變更檔案: 3 個
- **2017-09-21 18:14:23**: 調整鼎慧二期列表進階查詢功能
  - 變更檔案: 1 個

### 張詠威 (19 commits)

- **2017-10-23 14:43:22**: 調整取得JdbcHelper的方法避免造成connection爆了
  - 變更檔案: 1 個
- **2017-10-20 17:03:36**: 將RTX程式移除
  - 變更檔案: 2 個
- **2017-10-20 16:43:22**: A00-*********** 修正驗證gird item邏輯
  - 變更檔案: 1 個
- **2017-10-20 16:29:28**: A00-20170823001 調整人員離職日期判斷
  - 變更檔案: 1 個
- **2017-10-20 16:14:24**: A00-20170810001
  - 變更檔案: 1 個
- **2017-10-18 17:55:56**: A00-20170817001 修正當「簽核歷程」頁面設定為「頁籤」顯示方式，無法正常呈現簽核意見
  - 變更檔案: 2 個
- **2017-10-18 17:40:30**: A00-20170828002 已修正
  - 變更檔案: 1 個
- **2017-10-18 16:58:17**: Q00-20171018001 流程圖無法顯示，流程可以正常執行
  - 變更檔案: 1 個
- **2017-10-18 16:43:43**: A00-20171017001 流程負責人限制發起的組織  在管理流程無作用
  - 變更檔案: 1 個
- **2017-10-16 14:06:15**: A00-*********** 先調整import的位置
  - 變更檔案: 1 個
- **2017-10-13 15:17:58**: 修正A00-*********** 點選流程完成通知信mail link，登入後會導到待辦頁面
  - 變更檔案: 2 個
- **2017-10-12 14:53:24**: A00-20170928001 修正設定核決關卡時發現所參考的關卡沒有設定取回重辦仍會出現取回重瓣
  - 變更檔案: 1 個
- **2017-10-02 14:59:31**: A00-20171002001 修正核決關卡「轉存表單」失效議題
  - 變更檔案: 1 個
- **2017-10-02 10:03:14**: 調整獨立模組新的驗證機制
  - 變更檔案: 3 個
- **2017-09-28 14:36:00**: C01-20170919001 修正T100簽核樣版有進版時，Mcloud維護設定會全變空的
  - 變更檔案: 1 個
- **2017-09-28 09:36:42**: 20170928 調整關注項目清單
  - 變更檔案: 1 個
- **2017-09-27 13:36:13**: 20170927 調整關注項目清單
  - 變更檔案: 3 個
- **2017-09-26 14:51:04**: S00-20170808003 關卡解析部門當部門失效時，需寄信通知系統管理員
  - 變更檔案: 1 個
- **2017-09-25 11:56:13**: 修正附件名稱有多個.導致流程無法繼續派送
  - 變更檔案: 1 個

### pinchi_lin (36 commits)

- **2017-10-20 15:23:46**: Q00-20171020005 修正取重要流程列表，若是Oracle資料庫會取不到資料問題
  - 變更檔案: 1 個
- **2017-10-20 11:33:25**: 新增鼎慧二期柱狀折線圖服務(統計個人發起量)
  - 變更檔案: 8 個
- **2017-10-16 13:45:43**: 調整RESTful架構
  - 變更檔案: 1 個
- **2017-10-13 09:59:39**: 調整RESTful服務
  - 變更檔案: 2 個
- **2017-10-13 09:46:55**: 調整RESTful服務(manage)
  - 變更檔案: 4 個
- **2017-10-13 09:42:04**: 調整RESTful架構(controller)
  - 變更檔案: 4 個
- **2017-10-12 18:26:48**: 調整RESTful架構(contorller部分)
  - 變更檔案: 4 個
- **2017-10-12 16:57:58**: 調整儀表圖取資料邏輯移至BAMServiceMgr裡
  - 變更檔案: 2 個
- **2017-10-12 15:03:07**: 調整鼎慧儀表圖RESTful服務
  - 變更檔案: 2 個
- **2017-10-12 15:01:17**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-10-12 15:00:45**: 調整BAM相關RESTful服務
  - 變更檔案: 2 個
- **2017-10-12 10:45:05**: 修正鼎慧二期中間層同意派送與明日提醒加入防呆
  - 變更檔案: 1 個
- **2017-10-11 19:12:13**: 新增鼎慧儀表圖取資料邏輯部分
  - 變更檔案: 3 個
- **2017-10-06 17:20:28**: 新增鼎慧二期儀表圖服務基本架構
  - 變更檔案: 2 個
- **2017-10-06 16:48:11**: 新增鼎慧二期圖表服務遺漏javaBean
  - 變更檔案: 1 個
- **2017-10-06 16:46:36**: 新增鼎慧二期圖表服務用的javaBean
  - 變更檔案: 6 個
- **2017-10-06 15:49:57**: 調整鼎慧二期中間層功能
  - 變更檔案: 1 個
- **2017-10-06 11:01:04**: 修正鼎慧二期明日提醒部分錯誤
  - 變更檔案: 1 個
- **2017-10-05 16:40:20**: 調整LOG
  - 變更檔案: 2 個
- **2017-10-05 16:33:17**: 調整鼎慧二期取追蹤列表速度較慢問題
  - 變更檔案: 3 個
- **2017-10-05 14:03:52**: 修正取行動通知清單會報錯問題
  - 變更檔案: 2 個
- **2017-10-05 11:49:25**: 調整當中間層表單沒主旨時給預設主旨
  - 變更檔案: 1 個
- **2017-10-05 11:12:19**: 修正鼎慧明日提醒的狀態為已完成問題
  - 變更檔案: 3 個
- **2017-10-03 17:37:08**: 調整RESTful架構(調整部分程式命名)
  - 變更檔案: 11 個
- **2017-10-03 16:31:37**: 調整RESTful架構(Process相關manage遺漏部分)
  - 變更檔案: 2 個
- **2017-10-03 16:09:38**: 調整RESTful服務(process與form相關manage)
  - 變更檔案: 38 個
- **2017-10-03 15:07:08**: 調整RESTful架構(System相關manage異動)
  - 變更檔案: 8 個
- **2017-10-03 15:00:37**: 調整RESTful架構(manage路徑與org相關manage)
  - 變更檔案: 20 個
- **2017-10-03 14:28:51**: 調整RESTful服務(System相關manage)
  - 變更檔案: 26 個
- **2017-10-03 11:01:08**: 調整RESTful架構(manage)
  - 變更檔案: 10 個
- **2017-10-02 14:26:30**: 新增取得流程追蹤表單資料RESTful服務
  - 變更檔案: 5 個
- **2017-09-30 18:36:31**: 調整撤銷流程RESTful寫錯部分
  - 變更檔案: 2 個
- **2017-09-29 16:22:45**: 調整中間層表單會依關卡中的表單欄位權限作抓取
  - 變更檔案: 2 個
- **2017-09-29 16:00:53**: 調整鼎慧二期服務路徑
  - 變更檔案: 1 個
- **2017-09-29 11:09:13**: Q00-20170929001 修正BPMAPP發起流程多組織無法選擇問題
  - 變更檔案: 1 個
- **2017-09-28 11:28:04**: 調整追蹤列表無法顯示中間層表單問題
  - 變更檔案: 2 個

### jd (18 commits)

- **2017-10-20 11:13:31**: 修正圖表儲存格式造成的錯誤
  - 變更檔案: 1 個
- **2017-10-12 14:59:46**: 新增查詢圖表服務資料庫存取
  - 變更檔案: 6 個
- **2017-10-11 18:12:25**: 修正RESTful認證架構
  - 變更檔案: 9 個
- **2017-10-11 11:18:16**: 新增BAM統計組件架構
  - 變更檔案: 4 個
- **2017-10-03 14:39:12**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2017-10-03 14:31:18**: 新增待辦事項開啟服務
  - 變更檔案: 5 個
- **2017-10-02 14:09:17**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 2 個
- **2017-10-02 14:00:59**: 修正鼎捷移動統計組件錯誤運提 調整BPM RESTful認證功能
  - 變更檔案: 9 個
- **2017-09-28 17:40:28**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-09-28 17:39:02**: 修改OA服務URI問題 修改BPM服務認證錯誤問題
  - 變更檔案: 7 個
- **2017-09-28 11:34:17**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-09-28 11:32:47**: 統一調整RESTful URI路徑,去除 /resful/目錄 訂製此版本給OA測試
  - 變更檔案: 10 個
- **2017-09-27 17:59:05**: 新增明天提醒服務 新增後天提醒服務 新增流程簽和服務 修正統計控件較能 新增AOP架構
  - 變更檔案: 15 個
- **2017-09-26 18:32:50**: 鼎捷移動,明日提醒,後天題醒
  - 變更檔案: 2 個
- **2017-09-26 14:02:21**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 6 個
- **2017-09-26 13:58:36**: 新增明日提醒、後天提醒服務
  - 變更檔案: 8 個
- **2017-09-22 16:37:56**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2017-09-22 16:37:22**: 修正鼎慧表單中間層問題
  - 變更檔案: 8 個

### loren (4 commits)

- **2017-10-13 15:27:56**: 修正授權驗證服務中verifyaccesstoken的uri錯誤
  - 變更檔案: 1 個
- **2017-10-11 14:28:37**: 依RESTful命名規範調整授權驗證(Identity)相關URI
  - 變更檔案: 1 個
- **2017-09-26 14:15:39**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-09-26 14:15:11**: BPM接口授權及驗證機制
  - 變更檔案: 4 個

## 詳細變更記錄

### 1. 修正 : JSON資料開窗 ,查詢功能輸入數字，會查不到資料的問題
- **Commit ID**: `47b6eed9dde4b1d21b7e3aa2a4103b5d96b21d3d`
- **作者**: joseph
- **日期**: 2017-10-24 18:31:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`

### 2. 修正   關鍵事件開窗點選資料無法帶入欄位
- **Commit ID**: `5e6d2d969497429882aac8b5a3bb13a7ee8ff735`
- **作者**: joseph
- **日期**: 2017-10-24 18:25:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`

### 3. C01-20170914001 2次修正 :WEB表单设计师控件跟着鼠标跑
- **Commit ID**: `295150225c15aa49f17013dbfe85414a715d1796`
- **作者**: joseph
- **日期**: 2017-10-24 18:23:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 4. 將log刪除
- **Commit ID**: `e857764e7e99cdcc561a186ad15bf905fd419e74`
- **作者**: MiYu
- **日期**: 2017-10-24 13:49:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 5. 調整資料格式(多表單) 調整userFormValue格式
- **Commit ID**: `23517d74114c2c502882b5e2a59df38fd1e2958c`
- **作者**: MiYu
- **日期**: 2017-10-24 13:46:24
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/UserFormValueBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeWorkItemReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/SaveFormBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CompleteWorkItemForListBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 6. 新增取得系統多語系的RESTful服務
- **Commit ID**: `343339eac4d725b52bdd48c74cced59c82c33e7f`
- **作者**: ChinRong
- **日期**: 2017-10-24 09:07:34
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/GetRsrcbundleBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GetRsrcbundleBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 7. 調整取得JdbcHelper的方法避免造成connection爆了
- **Commit ID**: `e163ef832dd96a5e718848b98c307d4f0a478f05`
- **作者**: 張詠威
- **日期**: 2017-10-23 14:43:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`

### 8. 修正微信使用者頁面與鼎捷移動使用者頁面的問題
- **Commit ID**: `ddb862d73bb86d0f998c7cb1ebd32dea1fa4ca31`
- **作者**: ChinRong
- **日期**: 2017-10-23 11:46:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 9. 調整鼎捷移動部署網址產生工具
- **Commit ID**: `2ff8cff337ebf28aebe4aa23e5d3b75c6b219f8c`
- **作者**: ChinRong
- **日期**: 2017-10-20 19:10:54
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5651.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 10. 更換 排程log層級
- **Commit ID**: `2fbdde8606a9d3e1543d791eeecc98d6bee71cf4`
- **作者**: joseph
- **日期**: 2017-10-20 17:30:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/TimerFacadeDelegate.java`

### 11. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `b901bb314c102ec1c320a3d8db2df2f1e52efcba`
- **作者**: joseph
- **日期**: 2017-10-20 17:28:38
- **變更檔案數量**: 0

### 12. 將RTX程式移除
- **Commit ID**: `244409702e4abbe9e333a4c1adbe9f39fd687e76`
- **作者**: 張詠威
- **日期**: 2017-10-20 17:03:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/rtxpush/RTXMessagePusher.java`

### 13. A00-*********** 修正驗證gird item邏輯
- **Commit ID**: `5df02ec191ee269a92ab71897037ab246714160e`
- **作者**: 張詠威
- **日期**: 2017-10-20 16:43:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`

### 14. A00-20170823001 調整人員離職日期判斷
- **Commit ID**: `5a383f431624c8a5eba60a7a96a68346e51d8761`
- **作者**: 張詠威
- **日期**: 2017-10-20 16:29:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 15. A00-20170810001
- **Commit ID**: `5991a87b2bf748ff2361a445c7a97d0b0f4ba909`
- **作者**: 張詠威
- **日期**: 2017-10-20 16:14:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 16. 調整資料格式(多表單)
- **Commit ID**: `af27d7dc19bc858b5e52205fa422ee3f1c8355f7`
- **作者**: MiYu
- **日期**: 2017-10-20 15:47:04
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/InvokeProcessBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/NoticeProcessBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/TraceProcessBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`

### 17. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `f0ec640ec405f0e04e4790c1fe00885afdb495ee`
- **作者**: joseph
- **日期**: 2017-10-20 15:38:58
- **變更檔案數量**: 0

### 18. C01-20170914001  修正 :WEB表单设计师控件跟着鼠标跑
- **Commit ID**: `516f623cb98f211953e061f68be62a807850c0d3`
- **作者**: joseph
- **日期**: 2017-10-20 15:38:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 19. Q00-20171020005 修正取重要流程列表，若是Oracle資料庫會取不到資料問題
- **Commit ID**: `31bcef1b477c4a05238afe2ac4d7bec6347fd779`
- **作者**: pinchi_lin
- **日期**: 2017-10-20 15:23:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 20. 調整RESTFul服務 取得待辦事項表單資料(controller與manage)
- **Commit ID**: `68bd17f73e48de6a23a796dd9a73299f761c6c93`
- **作者**: MiYu
- **日期**: 2017-10-20 11:48:06
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 21. 新增鼎慧二期柱狀折線圖服務(統計個人發起量)
- **Commit ID**: `5e50be82ccb88b225792351dfc09727753cca998`
- **作者**: pinchi_lin
- **日期**: 2017-10-20 11:33:25
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ChartXconfig.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ChartYconfig.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesConfigForLine.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BAMBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 22. 修正圖表儲存格式造成的錯誤
- **Commit ID**: `cbd904035bd2104b28346545c8c04f555846be39`
- **作者**: jd
- **日期**: 2017-10-20 11:13:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`

### 23. 2次修改 關鍵事件 ORACLE CREATESQL
- **Commit ID**: `2bce45f5801f02ea0933fe9af51760ddf16d0c40`
- **作者**: joseph
- **日期**: 2017-10-19 13:59:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`

### 24. 修正 關注事件ORACLE createSQL
- **Commit ID**: `8d2e2db44ce690ece33466ccf26f892ae7ff6b75`
- **作者**: joseph
- **日期**: 2017-10-19 13:57:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`

### 25. A00-20171017002 修正自带小數點保留功能時BPM APP會發生錯誤問題
- **Commit ID**: `18fc3e0f03ff3e03dcb87cc72f6606813313d1ba`
- **作者**: MiYu
- **日期**: 2017-10-19 11:56:51
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`

### 26. 調整鼎捷移動部份功能
- **Commit ID**: `00b6e7a200da75b9dac7f971a5094cb10c9675f6`
- **作者**: ChinRong
- **日期**: 2017-10-19 11:37:41
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5651.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 27. A00-20170817001 修正當「簽核歷程」頁面設定為「頁籤」顯示方式，無法正常呈現簽核意見
- **Commit ID**: `4cd2129fbdffb3fd7e1a5f43eb6a19ce381a3def`
- **作者**: 張詠威
- **日期**: 2017-10-18 17:55:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 28. A00-20170828002 已修正
- **Commit ID**: `e2ee262dbbbeca079ddc2abe142ec1717589e714`
- **作者**: 張詠威
- **日期**: 2017-10-18 17:40:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/TiptopMemo.jsp`

### 29. Q00-20171018001 流程圖無法顯示，流程可以正常執行
- **Commit ID**: `51edaa104d936ae3a5f5ac85b75e1ad630d8b90b`
- **作者**: 張詠威
- **日期**: 2017-10-18 16:58:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 30. A00-20171017001 流程負責人限制發起的組織  在管理流程無作用
- **Commit ID**: `5ac769132c2280f195b7dd8031d4d9357618f5aa`
- **作者**: 張詠威
- **日期**: 2017-10-18 16:43:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 31. 修改 import js的路徑以防不同地方引用時出錯
- **Commit ID**: `2bbdce5ad9791ebfc0e74bf320fc69076f455a5f`
- **作者**: joseph
- **日期**: 2017-10-18 11:27:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`

### 32. 新增 JSON格式資料開窗分頁查詢功能
- **Commit ID**: `cea87350af07afc6ec163db4daecaf4dc38ecc75`
- **作者**: joseph
- **日期**: 2017-10-18 10:26:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`

### 33. 因ESS整合問題 將Template固定為IE8
- **Commit ID**: `3bde7ac85bdefa8419fe2e537c13b0df52871eb1`
- **作者**: joseph
- **日期**: 2017-10-18 10:21:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 34. 新增 getWeChatAccessToken, getWeChatAccountByCode的RESTful服務
- **Commit ID**: `f19a2c743589e67132dbb2fb28268ab6580b8482`
- **作者**: ChinRong
- **日期**: 2017-10-16 18:20:52
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/WeChatAuthBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/WeChatAccessTokenBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/WeChatAccountBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java`

### 35. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `ad2967c8f5e46c11f86e307006c9056cc578dfa3`
- **作者**: joseph
- **日期**: 2017-10-16 14:07:08
- **變更檔案數量**: 0

### 36. 移除 log
- **Commit ID**: `aed936058440cd2dd38ee8235905085e7cd7eae4`
- **作者**: joseph
- **日期**: 2017-10-16 14:06:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 37. A00-*********** 先調整import的位置
- **Commit ID**: `9c6bf911c24bb935efdf167f2d78cf665fe7e7a3`
- **作者**: 張詠威
- **日期**: 2017-10-16 14:06:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`

### 38. 修正  更新表單資料SQL語法
- **Commit ID**: `695193b27344f98394b9625a164053db331f1b69`
- **作者**: joseph
- **日期**: 2017-10-16 14:02:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 39. 打開鼎捷移動中間層表單的部份
- **Commit ID**: `696c236d61a9938379c0b05bea39429df757c9f7`
- **作者**: ChinRong
- **日期**: 2017-10-16 13:53:22
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementStyle.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`

### 40. 調整RESTful架構
- **Commit ID**: `e0237493fdcbf8c9d57512e1de3ea971a3c20c8a`
- **作者**: pinchi_lin
- **日期**: 2017-10-16 13:45:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`

### 41. C01-*********** 修正BPMAPP絕對位置畫面顯示不完全問題
- **Commit ID**: `8ab8ee962efd70b28b3db64b178ef9a50a1ea88e`
- **作者**: MiYu
- **日期**: 2017-10-16 13:36:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css`

### 42. 新增 Restful SysintegerationServer資料Response物件
- **Commit ID**: `aa4ae0d81ae7696c81042c5f287d095a51b061cb`
- **作者**: joseph
- **日期**: 2017-10-16 12:04:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysintegrationServerBeanRes.java`

### 43. 補上鼎慧的查詢聯絡人bySQL註冊器
- **Commit ID**: `8b8cb2fb2e4380243ec1374fbbeccf6975703275`
- **作者**: MiYu
- **日期**: 2017-10-13 17:52:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`

### 44. 新增 DotJ Restful接口及修改接口授權驗證機制
- **Commit ID**: `1329cc71fb49d488518464b3b6721c9066ad9dc6`
- **作者**: joseph
- **日期**: 2017-10-13 16:15:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 45. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `0f31390e8347774c29a7c536f26938af3cb6b0c0`
- **作者**: joseph
- **日期**: 2017-10-13 15:55:45
- **變更檔案數量**: 0

### 46. 修改系統排程log機制
- **Commit ID**: `809952bf104dd8282f39417a74a0e144669e4d9e`
- **作者**: joseph
- **日期**: 2017-10-13 15:55:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/TimerFacadeDelegate.java`

### 47. 修正授權驗證服務中verifyaccesstoken的uri錯誤
- **Commit ID**: `c6186d449da7ffac10489d7adb08c960e8d97e0f`
- **作者**: loren
- **日期**: 2017-10-13 15:27:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`

### 48. 修正A00-*********** 點選流程完成通知信mail link，登入後會導到待辦頁面
- **Commit ID**: `3d70e785bd40c6c57b8d08cd727c4d08fe8a108e`
- **作者**: 張詠威
- **日期**: 2017-10-13 15:17:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 49. 調整RESTful服務
- **Commit ID**: `afe1da494453d03f6b2d189df98b157f27ba099c`
- **作者**: pinchi_lin
- **日期**: 2017-10-13 09:59:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`

### 50. 調整Oracle的RemoteData改為512
- **Commit ID**: `4f1e3cefde60072a1f096d2d37fb0078119c7d39`
- **作者**: MiYu
- **日期**: 2017-10-13 09:55:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.1_updateSQL_Oracle.sql`

### 51. 調整RESTful服務(manage)
- **Commit ID**: `572d58d7e154928b987a31b471aafff27367c00e`
- **作者**: pinchi_lin
- **日期**: 2017-10-13 09:46:55
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileMPlatformServiceTool.java`

### 52. 調整RESTful架構(controller)
- **Commit ID**: `d62b1ab78927a3e3b03c4ac21ae0626fcaf2a13c`
- **作者**: pinchi_lin
- **日期**: 2017-10-13 09:42:04
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/mobile/MobileForm.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/mobile/MobileOrg.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/mobile/MobileProcess.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/mobile/MobileSystem.java`

### 53. 調整RESTful架構(contorller部分)
- **Commit ID**: `1b9b96cc3328c827fdf65afd2c69d73908ccf206`
- **作者**: pinchi_lin
- **日期**: 2017-10-12 18:26:48
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileForm.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileOrg.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`

### 54. C01-20171010001 修正加簽的進階查詢第二次查詢才有動作問題
- **Commit ID**: `38ea3a7ce3540e7c55bc5087f30efc6b557bcb0a`
- **作者**: MiYu
- **日期**: 2017-10-12 18:21:41
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 55. 新增查詢聯絡人SQL註冊器 新增SQL註冊器資料庫存取 多語系調整-流程圖示
- **Commit ID**: `a01d9a9b48a91b8ed86e80ae4b0e1a0c0440482e`
- **作者**: MiYu
- **日期**: 2017-10-12 17:46:59
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/ConnectionFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5651.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ContactBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.1_updateSQL_SQLServer.sql`

### 56. 調整儀表圖取資料邏輯移至BAMServiceMgr裡
- **Commit ID**: `2df57cf5edfe06496d62b20e65ff4acff06bd600`
- **作者**: pinchi_lin
- **日期**: 2017-10-12 16:57:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 57. 新增getMobileOAuthWeChatByWeChatID的RESTful服務
- **Commit ID**: `10b280dce9a8d6860f4734e88895b5230d55f359`
- **作者**: ChinRong
- **日期**: 2017-10-12 16:50:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java`

### 58. 將鼎捷移動圓餅圖取資料的邏輯搬到BAMServerMgr中
- **Commit ID**: `a0863d83a79f9c57d700d016522e1870f9306a32`
- **作者**: ChinRong
- **日期**: 2017-10-12 16:01:31
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/BAMBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 59. 調整鼎慧儀表圖RESTful服務
- **Commit ID**: `7b5f5eb859185ee51272ad1fc6da3c587ffce9df`
- **作者**: pinchi_lin
- **日期**: 2017-10-12 15:03:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 60. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `8b19d44410544840529342e788a833491e044836`
- **作者**: pinchi_lin
- **日期**: 2017-10-12 15:01:17
- **變更檔案數量**: 0

### 61. 調整BAM相關RESTful服務
- **Commit ID**: `f2345a26f1200bd6c4bf031def709e4bba4a6f4f`
- **作者**: pinchi_lin
- **日期**: 2017-10-12 15:00:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BAMBeanChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BAMBeanRes.java`

### 62. 新增查詢圖表服務資料庫存取
- **Commit ID**: `ada1a8fe7ee6019637ee1ec3060b9226d333592c`
- **作者**: jd
- **日期**: 2017-10-12 14:59:46
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileGraphTemplates.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.1_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.1_updateSQL_SQLServer.sql`

### 63. A00-20170928001 修正設定核決關卡時發現所參考的關卡沒有設定取回重辦仍會出現取回重瓣
- **Commit ID**: `1fe53a910e7b84c4e3b88218ba2963dd4d6ec98d`
- **作者**: 張詠威
- **日期**: 2017-10-12 14:53:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 64. 修正鼎慧二期中間層同意派送與明日提醒加入防呆
- **Commit ID**: `51f0ae629ad1265741053ea2e17827fe09d0565f`
- **作者**: pinchi_lin
- **日期**: 2017-10-12 10:45:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 65. 新增鼎捷移動圓餅圖-待處理工作百分比
- **Commit ID**: `0623c7f0134a65377a7e872dde90db35bafbc2c2`
- **作者**: ChinRong
- **日期**: 2017-10-12 09:12:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 66. 新增鼎慧儀表圖取資料邏輯部分
- **Commit ID**: `818f3cd5ac1b91e5fc5388ea1a1e32458b8551ef`
- **作者**: pinchi_lin
- **日期**: 2017-10-11 19:12:13
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesData.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 67. 修正RESTful認證架構
- **Commit ID**: `e1b85b6dda88e05ee203e4ea5e37a8a0aa7398bf`
- **作者**: jd
- **日期**: 2017-10-11 18:12:25
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/BpmServiceAuthenticate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileOrg.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`

### 68. 新增鼎捷移動圖表組件-圓餅圖-待辦未完成工作
- **Commit ID**: `e840723925acef02adb031736d8859a380c9fab0`
- **作者**: ChinRong
- **日期**: 2017-10-11 16:50:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 69. 依RESTful命名規範調整授權驗證(Identity)相關URI
- **Commit ID**: `8cf934ad383b6372c32b25acf393998c54579a87`
- **作者**: loren
- **日期**: 2017-10-11 14:28:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`

### 70. 新增BAM統計組件架構
- **Commit ID**: `f656d1335ddba018e8d8458aa467a1a5500ebfe2`
- **作者**: jd
- **日期**: 2017-10-11 11:18:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/BAMBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BAMBeanChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AbstractMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java`

### 71. 新增圓餅圖用到的java bean
- **Commit ID**: `a7bcc66731aec98b8ae294fc1beac77233ef80a1`
- **作者**: ChinRong
- **日期**: 2017-10-06 18:10:25
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ChartLegendConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterChartRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 72. 調整鼎捷移動部署工具
- **Commit ID**: `640e7847fda3d5917e73ae7aa901082666d2042b`
- **作者**: ChinRong
- **日期**: 2017-10-06 17:35:23
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5651.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 73. 新增鼎慧二期儀表圖服務基本架構
- **Commit ID**: `06e9082e9eba9c97c515690588bd5f9520565a82`
- **作者**: pinchi_lin
- **日期**: 2017-10-06 17:20:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 74. 新增鼎慧二期圖表服務遺漏javaBean
- **Commit ID**: `0846d9d3c70648321d1d5b3d5c0d6436ac3b3e90`
- **作者**: pinchi_lin
- **日期**: 2017-10-06 16:48:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterChartRes.java`

### 75. 新增鼎慧二期圖表服務用的javaBean
- **Commit ID**: `ccbc2743fbcae97bc114fe3c1ba8b5f23711da4f`
- **作者**: pinchi_lin
- **日期**: 2017-10-06 16:46:36
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesConfig.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesDetail.java`

### 76. 調整鼎慧二期中間層功能
- **Commit ID**: `7275d1b205dacd59232dce4f68e6b0a3c850a332`
- **作者**: pinchi_lin
- **日期**: 2017-10-06 15:49:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 77. 修正鼎慧二期明日提醒部分錯誤
- **Commit ID**: `d2e0cbbc4f5239301fa06d952dd845e513cc123e`
- **作者**: pinchi_lin
- **日期**: 2017-10-06 11:01:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 78. 調整LOG
- **Commit ID**: `186793c41fe0463bb1cd8da63eb2fa2ddee8f21d`
- **作者**: pinchi_lin
- **日期**: 2017-10-05 16:40:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 79. 調整鼎慧二期取追蹤列表速度較慢問題
- **Commit ID**: `a12952a8911d47fc9ff40a5054bbfa0cdeacc712`
- **作者**: pinchi_lin
- **日期**: 2017-10-05 16:33:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmTraceProcessTool.java`

### 80. 修正取行動通知清單會報錯問題
- **Commit ID**: `ef4a5bb4bebc5c0929700cae7360d23bc2db8c03`
- **作者**: pinchi_lin
- **日期**: 2017-10-05 14:03:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 81. 調整當中間層表單沒主旨時給預設主旨
- **Commit ID**: `3b66b6282fe437b08377399d7778e1d91438b288`
- **作者**: pinchi_lin
- **日期**: 2017-10-05 11:49:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 82. 修正鼎慧明日提醒的狀態為已完成問題
- **Commit ID**: `2d0af9d38df972bdfd69f29c53bfd0223ca559a2`
- **作者**: pinchi_lin
- **日期**: 2017-10-05 11:12:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`

### 83. 調整工作通知表單取表單的追蹤流程圖路徑
- **Commit ID**: `3bd370ad3feba30b34bab1c996cae5d996835698`
- **作者**: MiYu
- **日期**: 2017-10-03 18:11:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`

### 84. BPM APP表單畫面樣式還原 1.icon樣式還原 2.遮罩顏色加深 3.提示文字字型加大
- **Commit ID**: `4835a5a72c50459bfd92b1c6302a80ff1b4fbd2f`
- **作者**: MiYu
- **日期**: 2017-10-03 17:53:18
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTraceLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/StartUp.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/add_sign.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/agree.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/attachment.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/confirm_retrieve.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/exclamation.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/go_back.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/minus.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/more.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/plus.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/remind_calendar.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/retrieve.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/return.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/revoke.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/save_form.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/send.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/stop_process.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 85. 調整RESTful架構(調整部分程式命名)
- **Commit ID**: `bc54e7671e559e8c73409d15655960694e6c19da`
- **作者**: pinchi_lin
- **日期**: 2017-10-03 17:37:08
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/FetchInvokeProcessBeanReq.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/FetchNoticeWorkItemBeanReq.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/TraceDetailBeanReq.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FetchInvokeProcessBeanRes.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FetchNoticeWorkItemBeanRes.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/TraceDetailBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`

### 86. 調整RESTful架構(Process相關manage遺漏部分)
- **Commit ID**: `beeab27e1a5211bf2f534537bfd5b78d72e82357`
- **作者**: pinchi_lin
- **日期**: 2017-10-03 16:31:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 87. 調整RESTful服務(process與form相關manage)
- **Commit ID**: `df365168700f4214fd78277a8b923a6eb0f77769`
- **作者**: pinchi_lin
- **日期**: 2017-10-03 16:09:38
- **變更檔案數量**: 38
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchInvokeProcessBeanReq.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchNoticeWorkItemBeanReq.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeWorkItemReq.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/SaveFormBeanReq.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BpmFormBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BpmPhaseBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BpmProcessLevelBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BpmWorkItemDataBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchInvokeProcessBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchNoticeWorkItemBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/OrgUnitBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkStepViewerBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/TraceDetailBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AbortProcess.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileCompleteWorkStep.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchProcessComments.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeList.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeManage.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileNoticeList.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformCritical.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformDispatch.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformList.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformManage.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileReexecuteActivity.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileSaveForm.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileTerminateProcess.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileTraceList.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`

### 88. 調整RESTful架構(Process bean)
- **Commit ID**: `3f00764cd5de24aff31d2bdb304cd1cbc2218487`
- **作者**: MiYu
- **日期**: 2017-10-03 15:27:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/SaveFormBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CriticalBeanRes.java`

### 89. 調整RESTful服務(controller 增加Mobile系列)
- **Commit ID**: `32d2c218e5651375d7c8d40a7cce7108a9508222`
- **作者**: MiYu
- **日期**: 2017-10-03 15:11:32
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileForm.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileOrg.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`

### 90. 調整RESTful架構(System相關manage異動)
- **Commit ID**: `bafd2de2cfa1aec14cd47e07f6140d4d3e45f368`
- **作者**: pinchi_lin
- **日期**: 2017-10-03 15:07:08
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileNoticeList.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/BuildFormSystemMgr.java`

### 91. 調整RESTful架構(manage路徑與org相關manage)
- **Commit ID**: `38e519ad208fc0e381f4abf2a9db6dec5fff9a61`
- **作者**: pinchi_lin
- **日期**: 2017-10-03 15:00:37
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileContactUser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileNoticeList.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/BuildFormSystemMgr.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/DinWhaleSystemMgr.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/ESSFileManager.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/ESSFormHandler.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/ESSPerformWorkItem.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/FormMgr.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/InvokeProcessMgr.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/NoticeProcessMgr.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/OrgMgr.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/PerformProcessMgr.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/TraceProcessMgr.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/WeChatSystemMgr.java`

### 92. 調整RESTful服務(controller 增加MobileSystem)
- **Commit ID**: `46796a43055837d5bd7ddb7cfe9652a9481ff286`
- **作者**: MiYu
- **日期**: 2017-10-03 14:52:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`

### 93. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `b4936216f7e3eecdc184c23b873d2a0e426544c2`
- **作者**: jd
- **日期**: 2017-10-03 14:39:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java`

### 94. 新增待辦事項開啟服務
- **Commit ID**: `d119685b1250318b0ee9d72397bab8d88d5533b8`
- **作者**: jd
- **日期**: 2017-10-03 14:31:18
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchWorkStepsBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForPerformBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java`

### 95. 調整RESTful服務(System相關manage)
- **Commit ID**: `06b4ce955293b2686d4c6cec1bc67de8ee911797`
- **作者**: pinchi_lin
- **日期**: 2017-10-03 14:28:51
- **變更檔案數量**: 26
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/BuildFormBeanReq.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/MobileScheduleListBeanReq.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/MobileWeChatMsgBeanReq.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/OAuthConfigBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchInvokeProcessBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormDefinitionBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/MobileScheduleBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/MobileScheduleListBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/OAuthConfigBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFormDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileNoticeList.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileRsrcBundle.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileScheduleList.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileWeChatMsg.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileWeChatOAuth.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/DinWhaleSystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/OrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/PerformProcessMgr.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/WeChatOAuth.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/WeChatSystemMgr.java`

### 96. 補上漏掉的Form bean 調整共用bean不放在mobile內
- **Commit ID**: `8bf25dba7f15cab725ac1cec29ad7fa5ba29637e`
- **作者**: MiYu
- **日期**: 2017-10-03 14:26:53
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/ElementBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeWorkItemReq.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/ApplicationDefinitionBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/ExternalReferenceBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FetchNoticeWorkItemBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormFieldAccessDefinitionBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormInstanceBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormInstanceForPerformBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/SimpleUserBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/WorkStepViewerBeanRes.java`

### 97. 調整RESTful架構(Form bean)
- **Commit ID**: `83e1abc6379739c4298996fe25a99acce0552b7c`
- **作者**: MiYu
- **日期**: 2017-10-03 13:51:38
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/ElementBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/FetchInvokeProcessBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/FetchNoticeWorkItemBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeWorkItemReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/ApplicationDefinitionBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmFormBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmPhaseBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmProcessLevelBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmWorkItemDataBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/ExternalReferenceBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FetchInvokeProcessBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormFieldAccessDefinitionBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormInstanceBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormInstanceForPerformBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/MailingFrequencyTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OrgUnitBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OrganizationUnitForInvokingListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/SimpleUserBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/UserInputSubjectTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/VerifyPasswordTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/WorkStepViewerBeanRes.java`

### 98. 調整RESTful架構(System bean)
- **Commit ID**: `d6636271879d73d7adc64315c941747ad857a08c`
- **作者**: MiYu
- **日期**: 2017-10-03 11:23:51
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/BuildFormBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/OAuthConfigBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/ScheduleListBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/WeChatMsgBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormDefinitionBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OAuthConfigBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/ScheduleBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/ScheduleListBeanRes.java`

### 99. 調整RESTful架構(controller)
- **Commit ID**: `703f7d1c8302a83418bd43265f3a2aeca25f1a3d`
- **作者**: MiYu
- **日期**: 2017-10-03 11:22:05
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Invoke.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Notice.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Organization.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java`

### 100. 調整RESTful架構(manage)
- **Commit ID**: `12c9c9361e927f260af438cd8dc694ba0917ef49`
- **作者**: pinchi_lin
- **日期**: 2017-10-03 11:01:08
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/DinWhaleSystemMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/InvokeProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/NoticeProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/OrgMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/PerformProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/TraceProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/WeChatSystemMgr.java`

### 101. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `54f6007fbd2eec640983b20cd94e5fe2a7c23f15`
- **作者**: MiYu
- **日期**: 2017-10-03 09:55:08
- **變更檔案數量**: 0

### 102. 調整System的URI
- **Commit ID**: `5ae045ef9170b6ea8c5b3e56c7d4767932d93421`
- **作者**: MiYu
- **日期**: 2017-10-03 08:45:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`

### 103. 新增取得微信使用者認證資料RESTful服務
- **Commit ID**: `a98fec552222b745668e038b9b5aa92f4b9147aa`
- **作者**: ChinRong
- **日期**: 2017-10-02 18:16:19
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/WeChatAuthBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OAuthClientUserBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OAuthConfigBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/WeChatOAuth.java`

### 104. A00-20171002001 修正核決關卡「轉存表單」失效議題
- **Commit ID**: `21be0bca0835ee6576ddefdec428ff476427f75b`
- **作者**: 張詠威
- **日期**: 2017-10-02 14:59:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 105. 新增取得流程追蹤表單資料RESTful服務
- **Commit ID**: `e6c943240d1d1e159ea49f1080eaf45748b4c61c`
- **作者**: pinchi_lin
- **日期**: 2017-10-02 14:26:30
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/TraceDetailBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BpmPhaseBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/TraceDetailBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/TraceProcessMgr.java`

### 106. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `d2f00f2e8dd3c8fb65d051debfec7b3db62afdf3`
- **作者**: jd
- **日期**: 2017-10-02 14:09:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **刪除**: `3.Implementation/subproject/webapp/lib/AspectJ/aspectj-1.8.10.jar`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`

### 107. 修正鼎捷移動統計組件錯誤運提 調整BPM RESTful認證功能
- **Commit ID**: `ce604f047c2f6ac2970b9037855512c0cbc85e2a`
- **作者**: jd
- **日期**: 2017-10-02 14:00:59
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/BpmServiceAuthenticate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`

### 108. 調整鼎慧簽核currentState參數
- **Commit ID**: `efaeab2dbc6f067973849a5e10fee1c2048b5610`
- **作者**: ChinRong
- **日期**: 2017-10-02 11:53:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 109. BPM App RESTful服務參數 currentState參數格式統一
- **Commit ID**: `b43b1387127caf9b7dd0dd228b0e7143583dc235`
- **作者**: ChinRong
- **日期**: 2017-10-02 11:16:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CompleteWorkItemForListBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessInstanceForListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessInstanceStateTypeBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformDispatch.java`

### 110. 調整通知事項查詢服務
- **Commit ID**: `7705ad44e5faca017abbd7e1fce6924f88c8f649`
- **作者**: MiYu
- **日期**: 2017-10-02 10:30:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Notice.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileNoticeList.java`

### 111. 調整獨立模組新的驗證機制
- **Commit ID**: `5b25aa5a7f8de525b6e12c7a09779b2e8dcace0e`
- **作者**: 張詠威
- **日期**: 2017-10-02 10:03:14
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 112. 調整撤銷流程RESTful寫錯部分
- **Commit ID**: `4b510a928a79dd5ca78a7a994d136f6b080b2e2d`
- **作者**: pinchi_lin
- **日期**: 2017-09-30 18:36:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AbortProcessBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AbortProcess.java`

### 113. 新增通知事項查詢服務 調整產生表單邏輯
- **Commit ID**: `909d59f3e3ca04e6726d6143ae5bb327df9c91f9`
- **作者**: MiYu
- **日期**: 2017-09-30 18:30:14
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchNoticeWorkItemBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BpmWorkItemDataBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchNoticeWorkItemBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormDefinitionBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Notice.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFormDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileNoticeList.java`

### 114. 調整流程緊急度格式為數字25,50,75
- **Commit ID**: `798b2e6d57a498fd35fda99e302bac3d0ccafb26`
- **作者**: ChinRong
- **日期**: 2017-09-30 18:18:06
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeWorkItemReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchInvokeProcessBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeManage.java`

### 115. 調整發起流程/草稿發起資料查詢的表單資料格式
- **Commit ID**: `372fef92d7a6eec709f092c1e90d12ce55ae38bd`
- **作者**: ChinRong
- **日期**: 2017-09-30 17:21:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchInvokeProcessBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java`

### 116. 新增發起流程,草稿發起資料查詢服務
- **Commit ID**: `dd0d8e32704825ff048f9c25391848fe08af84fc`
- **作者**: ChinRong
- **日期**: 2017-09-30 15:06:26
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchInvokeProcessBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchInvokeProcessBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkStepViewerBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Invoke.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java`

### 117. 調整流程圖示多語系 調整產生json文本增加參數
- **Commit ID**: `46200a50055bf3421e06f6159e64d4aafa9b26d3`
- **作者**: MiYu
- **日期**: 2017-09-30 11:05:34
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/BuildFormBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormDefinitionBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFormDefinition.java`

### 118. 新增發起流程資料查詢會用到的java bean
- **Commit ID**: `1e2847e0f0ec5fdf4d1b3c81d05609ff1eff79c1`
- **作者**: ChinRong
- **日期**: 2017-09-29 17:20:44
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchInvokeProcessBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BpmFormBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BpmPhaseBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BpmProcessLevelBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BpmWorkItemDataBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchInvokeProcessBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/OrgUnitBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/OrganizationUnitForInvokingListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCommentTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkStepViewerBeanRes.java`

### 119. 調整中間層表單會依關卡中的表單欄位權限作抓取
- **Commit ID**: `f813205a1215f7a0795a98bb43f3ef0f0dec5f23`
- **作者**: pinchi_lin
- **日期**: 2017-09-29 16:22:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 120. 調整鼎慧二期服務路徑
- **Commit ID**: `9ffaef5a182f85eaa7cbcc83c674969f35829a8c`
- **作者**: pinchi_lin
- **日期**: 2017-09-29 16:00:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`

### 121. Q00-20170929001 修正BPMAPP發起流程多組織無法選擇問題
- **Commit ID**: `a698c1224700151cd597741d6f0d63cb918e290a`
- **作者**: pinchi_lin
- **日期**: 2017-09-29 11:09:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`

### 122. 修正鼎慧列表問題
- **Commit ID**: `a78a0d0d95fd8ce9d638987ef5b3db384fd86a49`
- **作者**: ChinRong
- **日期**: 2017-09-29 09:00:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 123. 新增鼎慧二期服務 1.獲取聯絡人資訊 補上漏掉UserListReader
- **Commit ID**: `cf9928c8037ce4f45132996629d00b48b3ea58a4`
- **作者**: MiYu
- **日期**: 2017-09-29 08:43:54
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PhonebookData.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 124. 新增restful服務 1.取得聯絡人資訊 調整UserListReader多增加取得組織與職務名稱
- **Commit ID**: `19cb1b922b77fc7c0f6aa43ad8ab8aa5f31c7122`
- **作者**: MiYu
- **日期**: 2017-09-29 08:36:08
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserForListDTO.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ContactUserBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserForListBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileContactUser.java`

### 125. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `364ff8c808362192d20df8b0dceeaa765bae5974`
- **作者**: jd
- **日期**: 2017-09-28 17:40:28
- **變更檔案數量**: 0

### 126. 修改OA服務URI問題 修改BPM服務認證錯誤問題
- **Commit ID**: `57cdb568a99d5bd80300ff2b7548aa721f1a43ae`
- **作者**: jd
- **日期**: 2017-09-28 17:39:02
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulClientDinWhale.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulServiceDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Invoke.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`

### 127. 調整鼎慧二期進階查詢RESTful路徑
- **Commit ID**: `6ead0bfca1a7575aa61327800cfac76b3ae0f69c`
- **作者**: ChinRong
- **日期**: 2017-09-28 16:14:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`

### 128. C01-20170919001 修正T100簽核樣版有進版時，Mcloud維護設定會全變空的
- **Commit ID**: `bb118c9d41e80900d749164c4018217548acedf0`
- **作者**: 張詠威
- **日期**: 2017-09-28 14:36:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 129. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a31eadad294b5957e4a5cd3cc09d0828f3253340`
- **作者**: jd
- **日期**: 2017-09-28 11:34:17
- **變更檔案數量**: 0

### 130. 統一調整RESTful URI路徑,去除 /resful/目錄 訂製此版本給OA測試
- **Commit ID**: `8f4c66154f163aec479c8ec9b07f798307f5255f`
- **作者**: jd
- **日期**: 2017-09-28 11:32:47
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Invoke.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Notice.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Organization.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 131. 調整追蹤列表無法顯示中間層表單問題
- **Commit ID**: `bd5fbd755eb97350c07eeb08f3140bd8c6fa3708`
- **作者**: pinchi_lin
- **日期**: 2017-09-28 11:28:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AbortProcessBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 132. 20170928 調整關注項目清單
- **Commit ID**: `7f0664bc17ee059524354cdfe2378177a7fbe46f`
- **作者**: 張詠威
- **日期**: 2017-09-28 09:36:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`

### 133. 鼎慧二期列表篩選增加"所有"選項
- **Commit ID**: `a3ac6b55e71ed396c17b333704a1824796322bc1`
- **作者**: ChinRong
- **日期**: 2017-09-27 18:49:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`

### 134. 新增明天提醒服務 新增後天提醒服務 新增流程簽和服務 修正統計控件較能 新增AOP架構
- **Commit ID**: `7780d0cc0166413dd6ecae28ccc954d023fdc277`
- **作者**: jd
- **日期**: 2017-09-27 17:59:05
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/AspectJ/aspectjrt-1.8.9.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/AspectJ/aspectjweaver-1.8.9.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogAspect.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationButtonRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`

### 135. 20170927 調整關注項目清單
- **Commit ID**: `0f65a0ce47f97dd680c229f1953ee9aff4ffef3b`
- **作者**: 張詠威
- **日期**: 2017-09-27 13:36:13
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 136. 修正鼎慧二期列表異常 1.修正待辦已簽核關鍵提示資訊欄位為空 調整前端行事曆提醒功能
- **Commit ID**: `65da337c94419b534a96a092be34e345dc1eb303`
- **作者**: MiYu
- **日期**: 2017-09-27 09:03:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`

### 137. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `f3d6981ccaccc41e939db7fb29baf58733dc5d6e`
- **作者**: MiYu
- **日期**: 2017-09-27 08:53:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 138. 調整鼎慧二期列表
- **Commit ID**: `e1d10f69e6b78c87a12bb93b3fab62e728636297`
- **作者**: MiYu
- **日期**: 2017-09-27 08:52:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 139. 鼎捷移動,明日提醒,後天題醒
- **Commit ID**: `c617ecd1cb9ec9ae9db9cf064bfbc08a5f8904e1`
- **作者**: jd
- **日期**: 2017-09-26 18:32:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 140. 調整鼎慧二期直連表單格式
- **Commit ID**: `b81f7502976742c9a9156ce7fd25d6dc6527ee90`
- **作者**: ChinRong
- **日期**: 2017-09-26 17:55:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 141. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `eab49af2a0e5dd402f569d2f7f36b1ed50829e54`
- **作者**: ChinRong
- **日期**: 2017-09-26 15:12:41
- **變更檔案數量**: 0

### 142. 調整鼎慧二期列表
- **Commit ID**: `82e029d5ab057f62b1f500b217485898d19f0812`
- **作者**: ChinRong
- **日期**: 2017-09-26 15:12:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`

### 143. S00-20170808003 關卡解析部門當部門失效時，需寄信通知系統管理員
- **Commit ID**: `b25787a6c12260b07f5c3ba14b92ff44a6e7e004`
- **作者**: 張詠威
- **日期**: 2017-09-26 14:51:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 144. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `852ec624264bd7838e6cc00a54aed77643314a07`
- **作者**: loren
- **日期**: 2017-09-26 14:15:39
- **變更檔案數量**: 0

### 145. BPM接口授權及驗證機制
- **Commit ID**: `e3ff6180fe7e519958be9bdd2996bf197f301201`
- **作者**: loren
- **日期**: 2017-09-26 14:15:11
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JavaJWT/java-jwt-2.1.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java`

### 146. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `900ff48201f416c52c5643ed4ef41ab753e98438`
- **作者**: jd
- **日期**: 2017-09-26 14:02:21
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5602.xls`
  - 📄 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5631.xls`
  - 📄 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls`
  - 📄 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5642.xls`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 147. 新增明日提醒、後天提醒服務
- **Commit ID**: `3be4dbb21d25289d41ed56a31d7953adb8ad11d9`
- **作者**: jd
- **日期**: 2017-09-26 13:58:36
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogAspect.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleOperationRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationButtonRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataOperationRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 148. 修正鼎慧二期列表異常 1.調整關注資訊欄位若無資料值預設給空值 2.調整部分註解與空格 3.調整待辦的流程發起時間與結束時間配合鼎慧
- **Commit ID**: `0fd224f752df053b42dbbfc9978f5b8b20825c8f`
- **作者**: MiYu
- **日期**: 2017-09-26 11:51:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 149. 調整鼎慧二期追蹤、通知列表進階查詢部份
- **Commit ID**: `3e7c49c3417495517cf32a952efcb9fbb87fd970`
- **作者**: ChinRong
- **日期**: 2017-09-26 10:07:09
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 150. 還原待辦事項關鍵資訊只有異常流程單據才取出來的判斷
- **Commit ID**: `77134f182dc3eff19ae3d1367012a43125c0084f`
- **作者**: ChinRong
- **日期**: 2017-09-26 09:54:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 151. 修正鼎慧二期列表異常 1.修正流程圖示在追蹤流程無法找到對應的ProcessDefId 2.調整部分註解
- **Commit ID**: `38b35cc6b472ad2ac80cbf40e8e11c5eefa9daf2`
- **作者**: MiYu
- **日期**: 2017-09-25 16:42:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 152. 修正鼎慧二期列表異常
- **Commit ID**: `094047ed1821b59ee67eb2666b9ac096039de451`
- **作者**: ChinRong
- **日期**: 2017-09-25 16:13:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 153. 修正附件名稱有多個.導致流程無法繼續派送
- **Commit ID**: `a4ed566958f0fff298b266e8d67a7a98d153fb04`
- **作者**: 張詠威
- **日期**: 2017-09-25 11:56:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 154. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `544e97f9750588874e979686e37c02af695891e6`
- **作者**: jd
- **日期**: 2017-09-22 16:37:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 155. 修正鼎慧表單中間層問題
- **Commit ID**: `13a45876f8008b2465ba8222ee2f3e37f81b99ed`
- **作者**: jd
- **日期**: 2017-09-22 16:37:22
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformDispatch.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 156. 新增進階查詢流程名稱過濾功能
- **Commit ID**: `35a8ab82a8e59129b4f23ecf8d3fe23896c698d4`
- **作者**: ChinRong
- **日期**: 2017-09-22 16:31:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDataset.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

### 157. 修正HtmlFormBuilderJSON的restful服務邏輯 調整WorkItemForListBean空格
- **Commit ID**: `d54d02aa4e537fa0f1b616a508f800992f06df10`
- **作者**: MiYu
- **日期**: 2017-09-22 11:48:52
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/BuildFormBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForListBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFormDefinition.java`

### 158. 調整鼎慧二期列表進階查詢功能
- **Commit ID**: `c45d093e83366ac18e5cda2b22b684e389593aa6`
- **作者**: ChinRong
- **日期**: 2017-09-21 18:14:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java`

