# Release Notes - BPM-ISO

## 版本資訊
- **新版本**: release_5.8.9.4
- **舊版本**: release_5.8.9.3
- **生成時間**: 2025-07-18 11:46:00
- **新增 Commit 數量**: 35

## 變更摘要

### 邱郁晏 (18 commits)

- **2023-12-05 15:26:01**: [附件擴充] 修正參數未開啟時判斷邏輯異常問題
  - 變更檔案: 4 個
- **2023-11-23 14:08:18**: [附件擴充] 上傳下載附件擴充機制: PDF閱讀機制
  - 變更檔案: 1 個
- **2023-11-23 11:31:39**: [附件擴充] 上傳下載附件擴充機制: PDF轉檔下載事件
  - 變更檔案: 1 個
- **2023-11-23 10:34:45**: [附件擴充] 上傳下載附件擴充機制:下載檔案機制
  - 變更檔案: 2 個
- **2023-11-22 17:08:49**: [附件擴充] 上傳下載附件擴充機制
  - 變更檔案: 5 個
- **2023-11-22 12:05:07**: [ISO] Q00-20231122001 新增評審規則刪除前檢查是否有文件引用
  - 變更檔案: 4 個
- **2023-11-16 17:15:41**: [ISO] Q00-20231116004 調整文件歸還申請單，隱藏欄位空值導致通知關卡未觸發問題
  - 變更檔案: 1 個
- **2023-11-16 15:09:57**: [ISO] S00-20230703004 文件屬性管理新增全文檢索功能
  - 變更檔案: 2 個
- **2023-11-13 14:35:17**: [ISO] TFG專案
  - 變更檔案: 5 個
- **2023-11-10 16:38:57**: [ISO] TFG專案
  - 變更檔案: 1 個
- **2023-11-09 14:58:58**: [ISO] TFG專案
  - 變更檔案: 5 個
- **2023-10-31 14:18:25**: Revert "[ISO] S00-20230818001 新增ISO可自定義文件浮水印字體(補)"
  - 變更檔案: 1 個
- **2023-10-31 14:15:29**: [ISO] S00-20230818001 新增ISO可自定義文件浮水印字體(補)
  - 變更檔案: 1 個
- **2023-10-30 17:33:22**: [ISO] S00-20230818001 新增ISO可自定義文件浮水印字體(補)
  - 變更檔案: 1 個
- **2023-10-27 14:03:42**: [ISO] S00-20230818001 新增ISO可自定義文件浮水印字體
  - 變更檔案: 3 個
- **2023-10-25 14:10:36**: [ISO] Q00-20231025003 修正行業別變更單據開窗選擇文件編號異常問題
  - 變更檔案: 1 個
- **2023-10-24 18:08:20**: [ISO] Q00-20231024001 修正行業別文管開窗選取到正在變更中的文件，新增查詢條件(補)
  - 變更檔案: 4 個
- **2023-10-24 14:44:20**: [ISO] Q00-20231024001 修正行業別文管開窗選取到正在變更中的文件，新增查詢條件
  - 變更檔案: 1 個

### waynechang (15 commits)

- **2023-11-21 17:21:59**: [ISO]Q00-20231121007 修正ISO文件屬性管理-索引卡的相關單位無法同時新增「部門、專案、群組」這三種類型的單位
  - 變更檔案: 1 個
- **2023-11-08 16:38:03**: [ISO]Q00-20231108003 調整ISO閱讀PDF檔案頁面，增加封鎖ctrl+s 下載檔案功能，只允許透過頁面上的下載按鈕
  - 變更檔案: 2 個
- **2023-10-31 14:42:36**: [ISO]Q00-20231031002 修正ISO文件的制定單位或保管單位為群組時，組通知信件時會發生錯誤，導致生失效通知信無法寄送的異常
  - 變更檔案: 2 個
- **2023-10-23 11:24:08**: [ISO]Q00-20230925001 調整ISO新增單、文管新增單，當編碼規則設定為自定義時，只有在Requester、ISODocManager關卡時，才允許重新編輯「文件編號」欄位[補]
  - 變更檔案: 1 個
- **2023-10-17 15:35:35**: [ISO] 專案-ISO全文檢索版本升版[補]
  - 變更檔案: 2 個
- **2023-10-03 13:41:37**: [ISO]Q00-20231003001 修正ISO調閱單，當單身有多筆資料按下刪除時，F12會有script錯誤的異常
  - 變更檔案: 1 個
- **2023-09-25 13:42:38**: [ISO]Q00-20230925001 調整ISO新增單、文管新增單，當編碼規則設定為自定義時，只有在Requester、ISODocManager關卡時，才允許重新編輯「文件編號」欄位
  - 變更檔案: 2 個
- **2023-09-23 16:35:40**: [ISO] 專案-ISO全文檢索版本升版
  - 變更檔案: 8 個
- **2023-09-21 11:51:36**: [ISO]V00-20230913001 修正ISO文件攜出申請流程，若攜出的郵件發送失敗時，想透過攜出申請表單的下載檔案功能時，會提示未結案無法下載的異常
  - 變更檔案: 1 個
- **2023-09-19 10:13:35**: [ISO]Q00-20230831003 修正ISO文件類別管理，使用者若只有子類別的權限，而沒有父類別的權限時，使用者仍可以看到父類別的文件的異常[補]
  - 變更檔案: 1 個
- **2023-09-15 11:11:46**: [ISO]Q00-20230913002 修正ISO文件變更單浮水印設定的內容變成圖片浮水印的最後一組文字內容
  - 變更檔案: 1 個
- **2023-09-12 15:21:37**: [ISO]Q00-20230912002 調整ISO新增單的檢核文件編號是否重複邏輯由非同步改為同步，避免檢核文件編號是否重複還沒完成流程就已經往下派送
  - 變更檔案: 2 個
- **2023-08-31 13:57:40**: [ISO]Q00-20230831003 修正ISO文件類別管理，使用者若只有子類別的權限，而沒有父類別的權限時，使用者仍可以看到父類別的文件的異常
  - 變更檔案: 1 個
- **2023-08-24 14:37:59**: [ISO]Q00-20230824002 修正ISO文件類別管理，當權限的套用範圍設定為部門但不包含子部門時，子部門的人員仍可以看到這份文件的異常
  - 變更檔案: 1 個
- **2023-08-24 14:11:40**: [ISO]S00-20230807002 ISO生失效郵件範本管理增加「變更原因」及「作廢原因」兩個信件樣版變數
  - 變更檔案: 1 個

### 刘旭 (1 commits)

- **2023-10-19 15:36:30**: [資安] V00-20230906001 SonarQube安全性議題 : 修复'PWD','Password','PASSWORD'安全检测问题
  - 變更檔案: 1 個

### 周权 (1 commits)

- **2023-10-19 15:28:59**: [資安]V00-20230906002 SonarQube安全性議題修正：'TOKEN' detected in this expression, review this potentially hard-coded secret
  - 變更檔案: 2 個

## 詳細變更記錄

### 1. [附件擴充] 修正參數未開啟時判斷邏輯異常問題
- **Commit ID**: `449e8e4de676c2ee31023d833efdaf3da70dc77f`
- **作者**: 邱郁晏
- **日期**: 2023-12-05 15:26:01
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 2. [附件擴充] 上傳下載附件擴充機制: PDF閱讀機制
- **Commit ID**: `63224f76d78377b169c4214aefef3f418d3ed477`
- **作者**: 邱郁晏
- **日期**: 2023-11-23 14:08:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java`

### 3. [附件擴充] 上傳下載附件擴充機制: PDF轉檔下載事件
- **Commit ID**: `37cf416d8d97eeb4c234a02af777d860389d33ab`
- **作者**: 邱郁晏
- **日期**: 2023-11-23 11:31:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 4. [附件擴充] 上傳下載附件擴充機制:下載檔案機制
- **Commit ID**: `1a7b8191d368d41646b6fbd986548159bf4d2f22`
- **作者**: 邱郁晏
- **日期**: 2023-11-23 10:34:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 5. [附件擴充] 上傳下載附件擴充機制
- **Commit ID**: `4ab8a203781a0355e785411a85b5c422437a8b67`
- **作者**: 邱郁晏
- **日期**: 2023-11-22 17:08:49
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 6. [ISO] Q00-20231122001 新增評審規則刪除前檢查是否有文件引用
- **Commit ID**: `40ef2014b68475b6d16be54d4c695e03d24381e5`
- **作者**: 邱郁晏
- **日期**: 2023-11-22 12:05:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOVettingRule.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISODocCmItem.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOVettingRuleController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOVettingRuleMgr.java`

### 7. [ISO]Q00-20231121007 修正ISO文件屬性管理-索引卡的相關單位無法同時新增「部門、專案、群組」這三種類型的單位
- **Commit ID**: `1e45e5196483ba868e51d2fd26323430135c5c4e`
- **作者**: waynechang
- **日期**: 2023-11-21 17:21:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/UpdateDocumentInfo.jsp`

### 8. [ISO] Q00-20231116004 調整文件歸還申請單，隱藏欄位空值導致通知關卡未觸發問題
- **Commit ID**: `8193914e295e1b54964cb3103b902bd963d43b81`
- **作者**: 邱郁晏
- **日期**: 2023-11-16 17:15:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOPaperWriteOff.js`

### 9. [ISO] S00-20230703004 文件屬性管理新增全文檢索功能
- **Commit ID**: `d936328c0149331783bce49dbb668418e61f7bc2`
- **作者**: 邱郁晏
- **日期**: 2023-11-16 15:09:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISODocUpdate.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISODocCmItemDaoImpl.java`

### 10. [ISO] TFG專案
- **Commit ID**: `0fc1459adbdfcaf80a2d979cf85cf0937d6dd69d`
- **作者**: 邱郁晏
- **日期**: 2023-11-13 14:35:17
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 11. [ISO] TFG專案
- **Commit ID**: `22afdb6224510c3ab1deeb8a58647f8528209740`
- **作者**: 邱郁晏
- **日期**: 2023-11-10 16:38:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 12. [ISO] TFG專案
- **Commit ID**: `56df686862973a81ef4e41c645092f0057944178`
- **作者**: 邱郁晏
- **日期**: 2023-11-09 14:58:58
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 13. [ISO]Q00-20231108003 調整ISO閱讀PDF檔案頁面，增加封鎖ctrl+s 下載檔案功能，只允許透過頁面上的下載按鈕
- **Commit ID**: `11c0cf2e553db8e6abb5513e80e80e8ff40f7645`
- **作者**: waynechang
- **日期**: 2023-11-08 16:38:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.html`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.js`

### 14. [ISO]Q00-20231031002 修正ISO文件的制定單位或保管單位為群組時，組通知信件時會發生錯誤，導致生失效通知信無法寄送的異常
- **Commit ID**: `58f7313acc286292a43ceb13d91d790b712f1ad0`
- **作者**: waynechang
- **日期**: 2023-10-31 14:42:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocMailMgr.java`

### 15. Revert "[ISO] S00-20230818001 新增ISO可自定義文件浮水印字體(補)"
- **Commit ID**: `36eeb51edaa0368da531d50ceb7a189d3fbe8404`
- **作者**: 邱郁晏
- **日期**: 2023-10-31 14:18:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`

### 16. [ISO] S00-20230818001 新增ISO可自定義文件浮水印字體(補)
- **Commit ID**: `197cd972757d5a2252ba155e21e3d1db74a1a801`
- **作者**: 邱郁晏
- **日期**: 2023-10-31 14:15:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`

### 17. [ISO] S00-20230818001 新增ISO可自定義文件浮水印字體(補)
- **Commit ID**: `40aeca1a855c4de9513a358dee870c978761d113`
- **作者**: 邱郁晏
- **日期**: 2023-10-30 17:33:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`

### 18. [ISO] S00-20230818001 新增ISO可自定義文件浮水印字體
- **Commit ID**: `6518b50b6c4bea55112c41484451e7d380b6c9a4`
- **作者**: 邱郁晏
- **日期**: 2023-10-27 14:03:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java`

### 19. [ISO] Q00-20231025003 修正行業別變更單據開窗選擇文件編號異常問題
- **Commit ID**: `f945afba2cd8c0801f254a996518be02b18e27f4`
- **作者**: 邱郁晏
- **日期**: 2023-10-25 14:10:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/CustomISODocListReader.java`

### 20. [ISO] Q00-20231024001 修正行業別文管開窗選取到正在變更中的文件，新增查詢條件(補)
- **Commit ID**: `0ea331b50b520bf70e09e0175e78bdf228525fc9`
- **作者**: 邱郁晏
- **日期**: 2023-10-24 18:08:20
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/CustomISOHomePage.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/DocumentChooser.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/CustomISODocListReader.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ManageDocumentMgr.java`

### 21. [ISO] Q00-20231024001 修正行業別文管開窗選取到正在變更中的文件，新增查詢條件
- **Commit ID**: `6ecfeeccfdac1e38b6b6fff9f626e9c40c8afae8`
- **作者**: 邱郁晏
- **日期**: 2023-10-24 14:44:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/CustomISODocListReader.java`

### 22. [ISO]Q00-20230925001 調整ISO新增單、文管新增單，當編碼規則設定為自定義時，只有在Requester、ISODocManager關卡時，才允許重新編輯「文件編號」欄位[補]
- **Commit ID**: `2290762a55df45aa8cd973207bffcb80435e788a`
- **作者**: waynechang
- **日期**: 2023-10-23 11:24:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreateManager.js`

### 23. [資安] V00-20230906001 SonarQube安全性議題 : 修复'PWD','Password','PASSWORD'安全检测问题
- **Commit ID**: `634c5a4a91b08054212e14bfb71ff491b6009c32`
- **作者**: 刘旭
- **日期**: 2023-10-19 15:36:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/isoPortability/ISOPortabilityMailDesign.java`

### 24. [資安]V00-20230906002 SonarQube安全性議題修正：'TOKEN' detected in this expression, review this potentially hard-coded secret
- **Commit ID**: `3db3a4d8df05cc6be116457c3c123dedf8c9fd44`
- **作者**: 周权
- **日期**: 2023-10-19 15:28:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ApplicationToken.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/util/cache/LicenseRegCache.java`

### 25. [ISO] 專案-ISO全文檢索版本升版[補]
- **Commit ID**: `03b60a9600c1dffd7530d79cc62d3c0c642a9c14`
- **作者**: waynechang
- **日期**: 2023-10-17 15:35:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/IndexingHandler.java`

### 26. [ISO]Q00-20231003001 修正ISO調閱單，當單身有多筆資料按下刪除時，F12會有script錯誤的異常
- **Commit ID**: `64f3e2363b787ad80f58ad15a042839b70591c6e`
- **作者**: waynechang
- **日期**: 2023-10-03 13:41:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOAccess.js`

### 27. [ISO]Q00-20230925001 調整ISO新增單、文管新增單，當編碼規則設定為自定義時，只有在Requester、ISODocManager關卡時，才允許重新編輯「文件編號」欄位
- **Commit ID**: `2847fbf2ede88ecde8e97cdb7c988d5d58a95414`
- **作者**: waynechang
- **日期**: 2023-09-25 13:42:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreate.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreateManager.js`

### 28. [ISO] 專案-ISO全文檢索版本升版
- **Commit ID**: `0786eb05d5f0f4876d659c67d1d8b3372aa830fd`
- **作者**: waynechang
- **日期**: 2023-09-23 16:35:40
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOFileDocumentsDaoImpl.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOFile.hbm.xml`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOFile.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileDocumentsController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileCache.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/IndexingHandler.java`

### 29. [ISO]V00-20230913001 修正ISO文件攜出申請流程，若攜出的郵件發送失敗時，想透過攜出申請表單的下載檔案功能時，會提示未結案無法下載的異常
- **Commit ID**: `2b4f2455552827b23431efa81491560150d2efa1`
- **作者**: waynechang
- **日期**: 2023-09-21 11:51:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java`

### 30. [ISO]Q00-20230831003 修正ISO文件類別管理，使用者若只有子類別的權限，而沒有父類別的權限時，使用者仍可以看到父類別的文件的異常[補]
- **Commit ID**: `73217112818152edb88492807a7745f2f88e185d`
- **作者**: waynechang
- **日期**: 2023-09-19 10:13:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java`

### 31. [ISO]Q00-20230913002 修正ISO文件變更單浮水印設定的內容變成圖片浮水印的最後一組文字內容
- **Commit ID**: `09dab005dfd6868db8e8620b6818bf20858fffad`
- **作者**: waynechang
- **日期**: 2023-09-15 11:11:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOModifyDocManagerMgr.java`

### 32. [ISO]Q00-20230912002 調整ISO新增單的檢核文件編號是否重複邏輯由非同步改為同步，避免檢核文件編號是否重複還沒完成流程就已經往下派送
- **Commit ID**: `01b9a5d2700781738415f1532fe6027cfbd27ce1`
- **作者**: waynechang
- **日期**: 2023-09-12 15:21:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreate.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreateManager.js`

### 33. [ISO]Q00-20230831003 修正ISO文件類別管理，使用者若只有子類別的權限，而沒有父類別的權限時，使用者仍可以看到父類別的文件的異常
- **Commit ID**: `3ebb4c51e2330b6fb031c507a30c9e230474e757`
- **作者**: waynechang
- **日期**: 2023-08-31 13:57:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java`

### 34. [ISO]Q00-20230824002 修正ISO文件類別管理，當權限的套用範圍設定為部門但不包含子部門時，子部門的人員仍可以看到這份文件的異常
- **Commit ID**: `bf1569bf0fc98e50a4d0dd0a9d9cf21a7f392d0b`
- **作者**: waynechang
- **日期**: 2023-08-24 14:37:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOAuthorityDaoImpl.java`

### 35. [ISO]S00-20230807002 ISO生失效郵件範本管理增加「變更原因」及「作廢原因」兩個信件樣版變數
- **Commit ID**: `a6f8d61395104dbb6089fa4b248c59d02929ff22`
- **作者**: waynechang
- **日期**: 2023-08-24 14:11:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/NotificationContent.jsp`

