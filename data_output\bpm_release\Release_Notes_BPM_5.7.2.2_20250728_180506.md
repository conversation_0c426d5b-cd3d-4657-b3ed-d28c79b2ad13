# Release Notes - BPM

## 版本資訊
- **新版本**: 5.7.2.2
- **舊版本**: 5.7.2.1
- **生成時間**: 2025-07-28 18:05:06
- **新增 Commit 數量**: 254

## 變更摘要

### joseph (30 commits)

- **2018-07-18 16:14:38**: 修正 : AccessToken驗證完後，儲存userInfo時傳入的Key有誤
  - 變更檔案: 1 個
- **2018-07-17 11:47:11**: 修正 : 整合表單同步Merge 儲存後 ,原先新增元件標註的顏色不會復原
  - 變更檔案: 1 個
- **2018-07-09 15:02:19**: 移除不必要的Log
  - 變更檔案: 1 個
- **2018-07-09 10:23:58**: 新增 : 簽核歷程restful接口
  - 變更檔案: 8 個
- **2018-07-09 10:19:37**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-07-09 10:19:20**: 調整 : Restful 切面授權驗證機制
  - 變更檔案: 2 個
- **2018-07-06 11:25:13**: A00-20180416001 修正 : 當Grid欄位設定與日期元件代號相同,而且剛好先產生Grid資料後產生Date資料時,會導致 FormData XML 中 Grid的資料型態錯誤 ,導致轉存表單無法存入
  - 變更檔案: 4 個
- **2018-07-06 11:22:08**: 調整 :E10CreateSQL 命名
  - 變更檔案: 2 個
- **2018-07-06 11:21:32**: 調整 : 產品序號欄位的欄位長度為100
  - 變更檔案: 12 個
- **2018-06-29 16:13:45**: 新增 : E10整合SQL
  - 變更檔案: 4 個
- **2018-06-28 16:00:57**: 新增 : 整合產品表單同步Merge功能
  - 變更檔案: 3 個
- **2018-06-28 14:58:18**: 新增 :RESTful發起流程接口，紀錄流程發起資訊在ProcessMappingKey資料表
  - 變更檔案: 13 個
- **2018-06-28 14:45:50**: 修正 : RESTful 撤銷流程 ,因報錯內容過大導致Response異常
  - 變更檔案: 1 個
- **2018-06-28 14:42:27**: 修正 : RestFul發單使用URLDecoder套件在編碼,因Form資料有&符號導致解析異常 ,調整為使用java源生字串編碼
  - 變更檔案: 1 個
- **2018-06-28 14:20:48**: 新增 : E10功能 -子單身開窗
  - 變更檔案: 8 個
- **2018-06-22 16:05:39**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-06-22 16:05:19**: 移除 : 表單同步測試程式
  - 變更檔案: 1 個
- **2018-06-22 15:39:06**: 修正 : 整合產品表單同步時因static物件導致吃到舊的變數資料
  - 變更檔案: 1 個
- **2018-06-22 15:38:04**: 新增 : 表單同步支援Date元件,以及加入E10產品時加入FormScript
  - 變更檔案: 2 個
- **2018-06-22 15:29:05**: 修正 : Cross 同步產品資訊時不會去改設定檔設定
  - 變更檔案: 1 個
- **2018-06-22 15:27:46**: 調整 : 回寫整合產品簽核歷程機制
  - 變更檔案: 10 個
- **2018-06-22 14:18:29**: 調整 : 取得簽核歷程加入表單id
  - 變更檔案: 1 個
- **2018-06-22 14:16:15**: A00-20180530027 修正: ISO文管首頁 IE無法開啟檔案,以及檔名異常
  - 變更檔案: 1 個
- **2018-06-22 14:08:40**: Q00-20180619001 修正 : WebService fetchWorkFlowDiagram  接口 取URL不支援 Https
  - 變更檔案: 3 個
- **2018-06-08 15:41:08**: 修正 : E10同步表單後無法儲存問題
  - 變更檔案: 1 個
- **2018-06-08 15:40:00**: 修正 :Restful 取簽核歷程 ,因沒有時間而報錯問題
  - 變更檔案: 1 個
- **2018-06-08 15:38:39**: 修正 : Restful發單 Grid資料因浮點樹型態無法轉型
  - 變更檔案: 1 個
- **2018-06-04 10:30:31**: A00-20180514001 修正 :  資料選取器開窗後，GRID欄位沒有按照設定所顯示
  - 變更檔案: 1 個
- **2018-06-01 14:13:51**: A00-20180514003 修正 Button元件,設定為SQL註冊器開窗 ,在選取資料後,呼叫的方法無作用
  - 變更檔案: 1 個
- **2018-05-30 15:47:35**: C01-***********  調整 發信及發通知 不會因為Qeueu報錯而Rollback
  - 變更檔案: 1 個

### jd (14 commits)

- **2018-07-18 15:36:43**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-07-18 15:27:43**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-07-18 15:26:07**: 新增移動表單客制開窗功能,提供Window.Open效果函式
  - 變更檔案: 1 個
- **2018-07-17 17:37:56**: 修正5.7.2.1的Oracle DDL欄位長度過長,導致流程設計師無法使用問題。
  - 變更檔案: 2 個
- **2018-07-12 14:47:32**: 修正鼎捷移動服務，列表篩選功能的重要性沒有多語系
  - 變更檔案: 2 個
- **2018-07-12 14:46:42**: 修正互聯溝通格式增加屬性時，取使用者對照表會發生解析錯誤問題
  - 變更檔案: 1 個
- **2018-07-12 14:45:30**: 修正老客升級時，沒有設定推播中間層會發生錯誤問題。
  - 變更檔案: 1 個
- **2018-06-20 18:01:12**: 修正連續簽核功能,修正互聯V2參數問題
  - 變更檔案: 3 個
- **2018-06-05 10:51:28**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-06-05 10:50:49**: 修正LOG記錄功能
  - 變更檔案: 13 個
- **2018-06-04 16:08:02**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-06-04 11:09:16**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-31 10:28:12**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 2 個
- **2018-05-31 10:25:24**: 調整LOG顯示機制
  - 變更檔案: 11 個

### waynechang (12 commits)

- **2018-07-18 15:30:46**: 新增經銷管理模組-透過互連中心取User資訊
  - 變更檔案: 2 個
- **2018-06-29 11:06:43**: Q00-20180629001 版更PDFViewer的jar 及替換寫法
  - 變更檔案: 8 個
- **2018-06-25 14:19:06**: A00-20180615002 修正文件總管，搜尋文件打開閱讀後(沒閱讀不會異常)，在搜尋時會發生找不到任何文件
  - 變更檔案: 1 個
- **2018-06-25 13:42:52**: A00-20180615001 修正ISO文件權限屬性管理作業異常
  - 變更檔案: 1 個
- **2018-06-25 11:33:50**: A00-20180613001 修正核決關卡取回重辦功能判斷異常
  - 變更檔案: 1 個
- **2018-06-15 16:52:38**: 增加ISOFile透過DocServer轉呼叫流程主機進行轉檔
  - 變更檔案: 4 個
- **2018-06-15 11:07:39**: C01-20180611002 增加轉檔PDF需要有書籤、交互參照(超連結)的功能
  - 變更檔案: 2 個
- **2018-06-14 13:43:05**: A00-20180529002 修正ISO使用紀錄查詢當文件ID有單引號時會報錯
  - 變更檔案: 1 個
- **2018-06-06 17:50:52**: A00-20180402002 修正 新增單文件類別無法開啟下階類別
  - 變更檔案: 1 個
- **2018-06-06 17:01:56**: 增加BCL 轉檔需要的jar
  - 變更檔案: 1 個
- **2018-06-04 17:32:28**: A00-20180530032 增加ISO模組url通知信連結
  - 變更檔案: 2 個
- **2018-06-01 11:17:51**: A00-20180530019 調整多語系
  - 變更檔案: 1 個

### ChinRong (40 commits)

- **2018-07-18 14:14:41**: 補上行動版加簽功能新樣式漏merge的部分
  - 變更檔案: 5 個
- **2018-07-18 11:57:29**: 補上行動版快搜功能共用方法中漏掉的程式碼
  - 變更檔案: 1 個
- **2018-07-17 10:13:46**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-07-17 10:13:16**: 修正議題
  - 變更檔案: 3 個
- **2018-07-13 15:05:19**: 修正DoubleTextBox在表單中間層顯示異常
  - 變更檔案: 2 個
- **2018-07-13 13:50:34**: 修正入口平台微信編輯沒有卡控根組織ID最多兩個字元的問題
  - 變更檔案: 1 個
- **2018-07-13 10:47:10**: 調整行動版取回重辦、開窗新樣式
  - 變更檔案: 23 個
- **2018-07-12 15:09:27**: 修正找不到FormUtil方法造成表單無法開啟及客製開窗無法使用的問題
  - 變更檔案: 1 個
- **2018-07-12 15:07:22**: 修正議題
  - 變更檔案: 6 個
- **2018-07-11 12:22:33**: 調整行動版樣式及功能
  - 變更檔案: 24 個
- **2018-07-10 15:58:57**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-07-10 15:58:19**: C01-20180704002 修正鼎捷移動處理的流程透過流程分類篩選後取不到資料的議題
  - 變更檔案: 1 個
- **2018-07-06 17:58:27**: 修正鼎捷移動發起流程列表概要字段為流程分類名稱時Label會顯示undefined的問題
  - 變更檔案: 1 個
- **2018-07-04 17:48:45**: 調整App簽核歷程頁面
  - 變更檔案: 21 個
- **2018-06-29 16:52:53**: 二次修正欄位名稱過常造成Oracle無法正常寫讀取/寫入資料的問題
  - 變更檔案: 1 個
- **2018-06-29 16:50:04**: 修正欄位名稱過常造成Oracle無法正常寫讀取/寫入資料的問題
  - 變更檔案: 6 個
- **2018-06-28 18:56:30**: 修正App函式庫中RadioButton取值方法
  - 變更檔案: 1 個
- **2018-06-28 16:08:58**: 調整App函式庫
  - 變更檔案: 2 個
- **2018-06-28 11:42:38**: 修正安卓手機打開表單會出現undefined is not a function 的問題
  - 變更檔案: 2 個
- **2018-06-28 09:38:15**: 修正merge錯誤
  - 變更檔案: 1 個
- **2018-06-27 18:25:36**: 調整App函式庫
  - 變更檔案: 1 個
- **2018-06-26 18:59:18**: 補上工作通知表單資料表單來源欄位
  - 變更檔案: 1 個
- **2018-06-25 10:36:26**: 修正鼎捷移動預設中間層不會取到Grid與附件的資料
  - 變更檔案: 2 個
- **2018-06-21 15:09:42**: 移除多餘的console.log
  - 變更檔案: 1 個
- **2018-06-21 09:04:12**: 修正行動版RWD設計器標記中間層無效的問題
  - 變更檔案: 2 個
- **2018-06-20 19:08:26**: 調整RESTful服務
  - 變更檔案: 5 個
- **2018-06-20 13:42:06**: 修正行動版RWD設計器在修改子頁籤ID時會產生多的區塊
  - 變更檔案: 3 個
- **2018-06-15 17:50:49**: 新增RESTful接口
  - 變更檔案: 9 個
- **2018-06-15 17:47:12**: 修正議題
  - 變更檔案: 4 個
- **2018-06-15 17:44:15**: 舊發起流程RESTful接口新增回傳流程序號
  - 變更檔案: 3 個
- **2018-06-13 18:43:46**: C01-20180514004 修正BPM重啟後使用企業微信登入App會取不到產品序號的問題
  - 變更檔案: 1 個
- **2018-06-07 15:17:28**: 修正表單相對位置設計器未開啟鼎捷移動開關時拖拉仍會標記中間層
  - 變更檔案: 2 個
- **2018-06-06 09:16:50**: 調整行動表單自動轉換機制，如果是用切分欄位的話在行動表單會用多欄位顯示
  - 變更檔案: 3 個
- **2018-06-04 13:59:59**: 修正行動表單預覽pdf檔案當附檔名為大寫時無法預覽的問題
  - 變更檔案: 10 個
- **2018-06-01 10:05:45**: 新增鼎捷移動待辦清單轉派資訊
  - 變更檔案: 1 個
- **2018-05-30 17:54:35**: 調整鼎捷移動行事曆功能,將WorkInfo塞到outer_schedule_id欄位中
  - 變更檔案: 2 個
- **2018-05-30 11:40:14**: 修正待簽核流程以及表單詳情資料相反的問題
  - 變更檔案: 1 個
- **2018-05-30 11:20:56**: 更新詳情維護多語系
  - 變更檔案: 2 個
- **2018-05-30 11:05:39**: 調整入口平台詳情ID維護
  - 變更檔案: 1 個
- **2018-05-30 10:39:35**: 新增入口平台鼎捷移動詳情應用代號維護功能
  - 變更檔案: 6 個

### 治傑 (20 commits)

- **2018-07-18 13:55:08**: 修正鼎捷移動中間層簽核後沒有返回到列表
  - 變更檔案: 2 個
- **2018-07-17 19:17:04**: 修正議題 1.修正鼎捷移動列表用時間過濾異常 2.修正鼎捷移動中間層退回重辦後關卡無簽核意見
  - 變更檔案: 2 個
- **2018-07-17 12:06:21**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-07-17 12:05:24**: 修正議題 1.修正鼎捷移動列表使用時間過濾異常 2.將整合設定中詳情關連應用設定功能鎖上 3.將鼎捷移動連續簽核功能鎖上
  - 變更檔案: 3 個
- **2018-07-13 13:39:30**: 修正BPMApp未開啟時，提示畫面沒有多語系
  - 變更檔案: 1 個
- **2018-07-03 17:52:14**: 修正使用Android點選客製開窗時，畫面會滑動
  - 變更檔案: 1 個
- **2018-07-03 14:01:57**: 修正merge錯誤
  - 變更檔案: 1 個
- **2018-07-03 09:30:43**: 調整App函式庫複合元件CSS功能
  - 變更檔案: 1 個
- **2018-06-27 18:02:31**: 開發APP元件函式庫labelcss功能
  - 變更檔案: 1 個
- **2018-06-25 17:26:59**: 修正企業微信使用者管理頁面的啟用狀態顯示為2
  - 變更檔案: 1 個
- **2018-06-22 17:18:51**: 修正行動版Gird沒綁定新增鈕時會出現undefined按鈕
  - 變更檔案: 1 個
- **2018-06-20 14:15:52**: 加入中台使用名稱
  - 變更檔案: 1 個
- **2018-06-20 10:15:16**: 修正行動版Gird沒綁定新增鈕時會出現undefined按鈕
  - 變更檔案: 12 個
- **2018-06-19 10:29:34**: 修正企業微信多語系僅支援繁中
  - 變更檔案: 1 個
- **2018-06-13 17:44:53**: 新增簽核RESTful服務接口
  - 變更檔案: 1 個
- **2018-06-13 14:45:19**: 新增簽核RESTful服務java bean
  - 變更檔案: 10 個
- **2018-06-06 15:49:31**: 補上轉派資訊多語系
  - 變更檔案: 2 個
- **2018-06-06 11:02:10**: 新增行動版處理的流程增加轉派資訊
  - 變更檔案: 3 個
- **2018-05-30 17:55:39**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-30 17:55:07**: 新增鼎捷移動"轉派資訊"概要字段
  - 變更檔案: 3 個

### jerry1218 (22 commits)

- **2018-07-18 11:23:02**: 序號邏輯微調
  - 變更檔案: 1 個
- **2018-07-18 10:14:00**: 借貨邏輯調整
  - 變更檔案: 2 個
- **2018-07-17 17:27:53**: Q00-20180717002 修正安裝密碼註冊-無法刪除不屬於此Mac的License(取消註冊失敗)
  - 變更檔案: 1 個
- **2018-07-17 17:26:54**: 移除多餘System.out
  - 變更檔案: 1 個
- **2018-07-16 16:05:42**: Q00-20180716002 修正 1.我的關注中，處理中的流程與授權的流程數量異常 2.個人化首頁串我的關注轉導致錯誤頁面(跑到追蹤流程) 3.我的關注中,未判斷關注流程&重要流程
  - 變更檔案: 3 個
- **2018-07-16 12:04:03**: Q00-20180716001 修正當使用者再兩個部門 , 其中非主部門為主管時 , 個人化首頁會產生圖表異常
  - 變更檔案: 1 個
- **2018-07-13 14:16:24**: Q00-20180713001 修正APP借貨到期依然可以登入的問題
  - 變更檔案: 3 個
- **2018-07-10 15:57:12**: Q00-*********** 修正借貨功能導致的異常 1.借貨邏輯異常 2.VIP所計算的總授權數異常(未考慮到期借貨) 3.借貨到期的情境administrator未強制導到註冊頁面
  - 變更檔案: 5 個
- **2018-07-09 09:58:06**: 修正5722 oracle update SQL錯誤(to_date)
  - 變更檔案: 1 個
- **2018-07-06 09:17:57**: 修改5.7.1.2_DML_Oracle_1.sql - 因內含的日期update語法有誤
  - 變更檔案: 1 個
- **2018-07-02 11:44:10**: A00-20180702001 修正退回重辦時,未點選欲退回關卡即按下確定後會導致錯誤的問題
  - 變更檔案: 1 個
- **2018-06-15 16:14:03**: A00-20180427005 修正監控流程時,如果選擇未結案流程，才會出現撤銷流程選項
  - 變更檔案: 1 個
- **2018-06-13 14:59:17**: C01-20180613002 修正bpmTable.js-319行length拼錯
  - 變更檔案: 1 個
- **2018-06-13 14:58:45**: C01-20180613001 修正button元件資料選取器中step3如使用複製查詢標籤功能會與step2的index互相影響的異常
  - 變更檔案: 1 個
- **2018-06-11 16:49:19**: C01-20180608002 修正PerformWorkItemMain.jsp與easyflow整合時,沒有任何一筆會導致loadding不完的問題
  - 變更檔案: 1 個
- **2018-06-11 15:54:54**: A00-20180611001 取消流程序號圖片的cancelBubble
  - 變更檔案: 2 個
- **2018-06-11 14:51:01**: C01-20180611001 修改個人化首頁-追蹤流程&我的追蹤2功能轉頁時呈現的條件與個人化首頁一致
  - 變更檔案: 2 個
- **2018-06-05 14:56:56**: Q00-20180530002 修改IE瀏覽器placeholder顏色預防使者誤解
  - 變更檔案: 2 個
- **2018-06-04 15:07:57**: Q00-20180604001 修正主管頁面兩個錯誤 1.圖表的分類有本週,本月,本季,但邏輯為較舊版本的最近七天,最近一月,已修改邏輯與分類一致 2.直方圖計算基礎錯誤,正確的邏輯應為所選部門下面的逾時單據統計,依User分群(原為所選部門下面的人員的所有逾時單據統計)
  - 變更檔案: 3 個
- **2018-05-30 15:29:53**: 修改loadding圖案
  - 變更檔案: 2 個
- **2018-05-30 10:46:56**: Q00-20180330004 修正英文語系第二層manu太長時會有字串被截斷問題
  - 變更檔案: 1 個
- **2018-05-30 09:53:44**: 修正RemoteObjectProvider.createWorkflowServerManager method中存在session中的WorkflowServerManagerDelegate session key錯誤問題
  - 變更檔案: 1 個

### Gaspard (49 commits)

- **2018-07-18 09:35:18**: 提升引用資源版本號的可讀性(Timestamp + 實際日期與時間)
  - 變更檔案: 1 個
- **2018-07-16 14:09:25**: 上傳IReport定義檔時，多增加驗證定義檔代號不可為空
  - 變更檔案: 3 個
- **2018-07-16 13:51:51**: QRCode元件增加getData方法，可將產生的圖片以base64匯出
  - 變更檔案: 1 個
- **2018-07-16 11:44:19**: 優化簽名元件匯出base64字串時，移除「data:image/png;base64,」字眼
  - 變更檔案: 1 個
- **2018-07-16 09:55:48**: 移除工具列上按鈕描述的多餘符號
  - 變更檔案: 1 個
- **2018-07-16 09:11:16**: 修改工具列上之待辦數量呈現的樣式
  - 變更檔案: 1 個
- **2018-07-08 19:38:24**: 修正當菜單載入速度大於template時，工具列按鈕無法順利載入的異常
  - 變更檔案: 1 個
- **2018-07-08 18:17:26**: 修正當菜單的iframe載入比主畫面iframe慢時導致無法setTitle的異常
  - 變更檔案: 1 個
- **2018-07-06 22:33:12**: 在地化議題修正
  - 變更檔案: 1 個
- **2018-07-06 17:42:11**: 修正鼎新版本工具列ICON顯示的特殊邏輯
  - 變更檔案: 1 個
- **2018-07-06 15:10:50**: 在地化功能調整與異常修正
  - 變更檔案: 30 個
- **2018-07-05 16:25:17**: 隱藏未完成的資料選取註冊器功能
  - 變更檔案: 3 個
- **2018-07-05 15:44:37**: 隱藏5722尚未完成的功能與SQL
  - 變更檔案: 4 個
- **2018-07-05 15:33:21**: 增加表單設計藍圖功能
  - 變更檔案: 9 個
- **2018-07-04 15:45:17**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-07-04 15:44:39**: 儲存表單時，手寫元件的資料使用ntext或CLOB裝載
  - 變更檔案: 1 個
- **2018-07-02 15:22:17**: 多增加絕對位置表單轉換成RWD表單功能，同時表單可不進版
  - 變更檔案: 10 個
- **2018-07-02 09:57:37**: 修正Web表單設計器，檢視表單修改歷程後，欲取出某一版表單時，只能使用絕對位置的編輯器開啟
  - 變更檔案: 1 個
- **2018-06-29 17:12:39**: 加入HandWriting元件
  - 變更檔案: 15 個
- **2018-06-29 16:24:01**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-06-29 16:23:42**: 修改FormUtil的提示訊息為英文提示
  - 變更檔案: 1 個
- **2018-06-29 12:00:38**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-06-29 12:00:19**: 修正開啟腳本樣版視窗的情況下在點擊右上關閉，全域變數清單不會顯示的異常
  - 變更檔案: 1 個
- **2018-06-29 10:29:48**: 資料選取註冊器，修正原始SQL有order by時，動態組成SQL的方式
  - 變更檔案: 1 個
- **2018-06-29 10:17:23**: 移除全形的SQL語法
  - 變更檔案: 1 個
- **2018-06-26 16:16:28**: 移除多餘LOG顯示
  - 變更檔案: 1 個
- **2018-06-26 16:07:23**: 增加表單的頁籤元件可透過流程設計器設定於各關卡是否顯示
  - 變更檔案: 2 個
- **2018-06-26 14:07:16**: 表單設計器增加QRCode元件
  - 變更檔案: 13 個
- **2018-06-26 11:25:25**: 增加Grid元件列印表單時漏呼叫的載入後方法、修正名片式呈現時若無資料會顯示兩次無資料的異常
  - 變更檔案: 1 個
- **2018-06-26 11:23:47**: 修正ESS整合中Q串F無法開啟的問題處理
  - 變更檔案: 1 個
- **2018-06-22 09:00:37**: 增加FormUtil的共用method
  - 變更檔案: 4 個
- **2018-06-21 16:32:33**: 將「工作通知清單」與「監控流程」頁面的快搜按鈕，從查詢收合窗移到外層靠右排列
  - 變更檔案: 2 個
- **2018-06-21 15:49:25**: 增加資料選取註冊器功能
  - 變更檔案: 36 個
- **2018-06-20 10:21:41**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-06-20 10:21:26**: 修改讓程式樣板的內容可以透過ctrl+c來複製需要的程式
  - 變更檔案: 1 個
- **2018-06-13 10:01:07**: 修改表單設計器清單頁的查詢功能，查出來的結果可顯示為「響應式」或是「絕對位置」表單
  - 變更檔案: 1 個
- **2018-06-12 16:28:17**: 將QueryDesigner.css也加上資源版本控制
  - 變更檔案: 12 個
- **2018-06-12 10:46:02**: 修改Web表單設計器，恢復拖曳元件時可自訂貼齊格線大小的功能
  - 變更檔案: 3 個
- **2018-06-08 17:10:06**: 修改表單Title元件僅在大螢幕呈現，且文字直接擺放於100%寬度的容器內
  - 變更檔案: 5 個
- **2018-06-08 09:50:03**: 加入表單腳本樣版功能
  - 變更檔案: 19 個
- **2018-06-07 11:52:56**: 調整ESS表單預設呈現高度為1500
  - 變更檔案: 2 個
- **2018-06-07 11:06:37**: 優化Web表單設計器，提升表單開啟效能，將表單輔助格線變更為3種模式，分別為5x5 10x10 20x20。
  - 變更檔案: 6 個
- **2018-06-06 17:24:35**: 產生ESS表單的橫向Scrollbar
  - 變更檔案: 2 個
- **2018-06-05 17:27:20**: 修改引用資源版號變成預設值0000000000，未來手動更新時直接變成當下的timestamp
  - 變更檔案: 4 個
- **2018-06-04 11:36:32**: (1)表單的全域變數中，提供資源動態載入時可使用的變數 (2)優化表單全域變數產生方法
  - 變更檔案: 23 個
- **2018-06-02 23:18:13**: 增加引用JS、CSS資源的版本設定
  - 變更檔案: 238 個
- **2018-06-01 23:21:22**: 增加資源載入版號的系統變數設計(加入SQL)
  - 變更檔案: 2 個
- **2018-06-01 23:18:23**: 增加資源載入版號的系統變數設計
  - 變更檔案: 9 個
- **2018-05-30 14:46:54**: RWD表單開發優化
  - 變更檔案: 12 個

### yamiyeh10 (15 commits)

- **2018-07-17 10:10:53**: 修正Grid在連續修改時表單內容沒有帶值問題
  - 變更檔案: 1 個
- **2018-07-11 18:02:42**: 調整鼎捷移動附件新樣式
  - 變更檔案: 10 個
- **2018-07-06 13:52:25**: 切割BPMAPP新樣式檔案路徑與還原舊有檔案
  - 變更檔案: 880 個
- **2018-07-04 11:45:56**: A00-20180615004 修正草稿流程議題後導致發單畫面無法開啟問題
  - 變更檔案: 1 個
- **2018-07-03 16:45:24**: 調整發起、待辦、追蹤、通知的流程主旨、簽核意見與選擇部門樣式 調整FormUtil取得主旨ID 調整退回重辦只有一個選項時樣式 新增流程主旨、簽核意見與選擇部門樣式多語系
  - 變更檔案: 26 個
- **2018-06-28 19:29:54**: 補上漏掉的退回重辦jsp
  - 變更檔案: 1 個
- **2018-06-28 19:26:06**: 調整App退回重辦樣式
  - 變更檔案: 10 個
- **2018-06-28 11:47:19**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-06-28 11:46:41**: C01-20180626002 修正入口平台整合設定中微信使用者管理搜尋功能異常
  - 變更檔案: 2 個
- **2018-06-27 18:18:42**: 調整水平線ID名稱 調整Title元件logo拿掉與調整ID名稱 補上Attachment元件漏掉icon 調整分頁、流程名稱、終止、撤銷樣式 調整按鈕、選項、開窗元件、DoubleTextBox元件
  - 變更檔案: 23 個
- **2018-06-22 10:19:33**: 修正App頁籤功能異常問題 1.多頁籤時顯示異常 2.頁籤與子頁籤更改ID時顯示異常 3.選擇子頁籤時影響其餘頁籤顯示 4.子頁籤Value過長時畫面跑版
  - 變更檔案: 6 個
- **2018-06-20 18:37:04**: 新增RESTful接口 1.終止流程 2.退回重辦 3.加簽關卡
  - 變更檔案: 7 個
- **2018-06-19 18:33:27**: 修正BPMApp若行動版表單設計師設置空欄位時畫面顯示${column}問題
  - 變更檔案: 1 個
- **2018-05-30 18:19:07**: 調整App取得已轉派列表tool位置
  - 變更檔案: 2 個
- **2018-05-30 17:40:06**: 新增App用的已轉派流程tool與listreader
  - 變更檔案: 5 個

### 施廷緯 (15 commits)

- **2018-07-16 18:07:02**: Q00-20180620001修改組織設計師按下多語系的開窗後，再次開啟沒有多語系值暫存問題。
  - 變更檔案: 2 個
- **2018-07-04 16:11:43**: Q00-20180704002 調整Forminstance.fieldValues的XML排版1.不會隨派送過程上下tag間距增加2.產生的XML的排版至左。
  - 變更檔案: 1 個
- **2018-07-04 15:05:33**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-07-04 15:01:35**: 同C01-20180702002，同Formdispatch為false會一直loading問題，針對"派送"做修正。
  - 變更檔案: 1 個
- **2018-07-04 10:34:47**: C01-20180702002  修正FormDispatch為False時，發起流程畫面會停在Loading.
  - 變更檔案: 1 個
- **2018-06-26 18:49:30**: A00-20180626001 修正SQL指令存在全形空白的字元導致異常。
  - 變更檔案: 1 個
- **2018-06-26 11:46:26**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-06-26 11:40:15**: C01-20180621003 修正57測試機整合WF ERP單據撤銷、終止、回寫失敗。在WorkflowManager、WorkflowManagerLocal補上@Remoter及@Local。
  - 變更檔案: 2 個
- **2018-06-19 15:26:26**: 同A00-20180606005 修正刪除子部門後，會發生找不到帳號的錯誤訊息。
  - 變更檔案: 3 個
- **2018-06-14 16:27:53**: A00-20180606005	新增備註及修改縮排問題
  - 變更檔案: 1 個
- **2018-06-13 20:06:15**: A00-20180606005 修正刪除子部門會發生找不到此帳號的錯誤訊息
  - 變更檔案: 2 個
- **2018-06-13 15:59:26**: 修正User在ProcessID少於兩碼無法發起流程問題。
  - 變更檔案: 1 個
- **2018-06-13 10:43:45**: 修正刪除子部門會發生找不到此帳號的錯誤訊息。
  - 變更檔案: 1 個
- **2018-06-13 10:37:12**: 修正HR小助手 檢查部門失效且離職人員有主部門條件(微調)
  - 變更檔案: 1 個
- **2018-06-04 16:38:30**: 修改組織HRM小助手同步當部門失效時，忽略離職人員設定此單位為主部門的卡控。
  - 變更檔案: 1 個

### 顏伸儒 (10 commits)

- **2018-07-16 12:08:13**: Q00-20180627001 修正BPM57版本,離職人員ID含有單引號開窗會發生異常。
  - 變更檔案: 1 個
- **2018-07-09 18:30:03**: S00-*********** 修正BPM57版本,公用片語管理的重設按鈕,易造成使用者混淆故刪除。
  - 變更檔案: 1 個
- **2018-07-02 12:54:56**: A00-20180510003 修正BPM57版本,退回重辦時,將取得參予者的資料加上防呆。
  - 變更檔案: 1 個
- **2018-06-28 19:33:53**: A00-20180628001 修正BPM57版本,修正T100操作刪除簽核流程時會執行的LOG。
  - 變更檔案: 1 個
- **2018-06-27 16:02:06**: A00-20180620001 修正BPM57版本,設定個人資訊裡的預設代理人設定,和流程代理人設定裡面的起始與結束時間選取異常。
  - 變更檔案: 2 個
- **2018-06-27 14:46:44**: A00-20180625001 修正BPM57版本,從T100端欲重建BPM流程時會執行到的判斷。
  - 變更檔案: 1 個
- **2018-06-25 13:50:36**: A00-20180612001 修正BPM57版本,查詢開窗輸入搜尋條件後,查詢的資料排序消失的問題。
  - 變更檔案: 1 個
- **2018-06-22 15:56:47**: [C01-20180530003]修正BPM57版本,核決關卡沒有人簽核過,不顯示在退回重瓣的清單裡。
  - 變更檔案: 1 個
- **2018-06-13 17:54:25**: 修正BPM57版本,向前(後)加簽關卡時無法正常發單的問題。
  - 變更檔案: 3 個
- **2018-06-06 16:57:48**: 加入ISO文件檔案管理模組製作索引文件時會用到的jar檔。
  - 變更檔案: 1 個

### pinchi_lin (22 commits)

- **2018-07-12 18:13:27**: 修改grid新增或編輯隱藏邏輯調整錯誤地方
  - 變更檔案: 1 個
- **2018-07-11 19:13:57**: 修正grid資料編輯至最後一筆後不跳回單身與grid資料編輯畫面按鈕跑版問題
  - 變更檔案: 13 個
- **2018-07-11 13:09:47**: 新增新樣式的清除鈕與調整password元件跑版
  - 變更檔案: 5 個
- **2018-07-10 20:00:09**: 修正元件清除條跑版問題(在多欄與不同螢幕大小時)
  - 變更檔案: 1 個
- **2018-07-10 19:09:48**: 修正PASSWORD元件跑板問題(在多欄與不同螢幕大小時)
  - 變更檔案: 2 個
- **2018-07-10 17:57:07**: 修正元件清除條無法顯示與使用問題
  - 變更檔案: 6 個
- **2018-07-05 13:47:55**: A00-20180705001 修正鼎捷移動推播網址錯誤導致無法開啟表單畫面問題
  - 變更檔案: 1 個
- **2018-07-04 14:58:45**: 調整APP函式庫錯誤訊息內容
  - 變更檔案: 1 個
- **2018-07-03 17:21:30**: 修正APP元件函式庫顯示與隱藏功能造成跑板問題
  - 變更檔案: 1 個
- **2018-07-03 14:43:45**: 開啟取元件類別中的log
  - 變更檔案: 1 個
- **2018-06-29 14:18:12**: 開發APP元件函式庫的元件顯示功能與調整隱藏功能邏輯
  - 變更檔案: 1 個
- **2018-06-28 16:58:13**: 開發APP元件函式庫的元件禁用功能與修正附件按鈕樣板id錯誤問題
  - 變更檔案: 2 個
- **2018-06-27 15:50:39**: 開發APP元件函式庫的時間元件取值與設值功能
  - 變更檔案: 1 個
- **2018-06-26 16:48:25**: 調整APP表單函示庫的輸入元件設值與取值以及設定主旨功能
  - 變更檔案: 2 個
- **2018-06-26 11:29:25**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-06-26 11:26:04**: 新增APP用元件函式庫
  - 變更檔案: 11 個
- **2018-06-26 10:14:24**: C01-20180624008 修正發起時能發單但有ReferenceError:goMenu is not defined的錯誤
  - 變更檔案: 4 個
- **2018-06-20 18:15:24**: 簽核流設計師中加入行動版表單存取控管可以批次關卡設定
  - 變更檔案: 6 個
- **2018-06-11 18:34:56**: 調整APP站台restful服務支持RWD表單與增加表單來源
  - 變更檔案: 7 個
- **2018-06-11 18:08:59**: 調整APP中行事曆跳轉詳情應用功能
  - 變更檔案: 1 個
- **2018-05-31 14:18:38**: 調整APP中鼎捷移動行事曆新增的JSON格式
  - 變更檔案: 3 個
- **2018-05-30 18:34:25**: 調整APP的鼎捷移動推播消息跳轉至詳情應用
  - 變更檔案: 3 個

### lorenchang (4 commits)

- **2018-06-28 17:20:41**: 變更scan-interval為30秒，減少因為IO來不及讀完NaNaWeb.war導致無法順利部署的問題
  - 變更檔案: 2 個
- **2018-06-26 15:54:21**: 配合公司調整產品預設Port
  - 變更檔案: 2 個
- **2018-06-06 17:06:26**: 修正5.7.1.1_DML_MSSQL_1.sql內含MSSQL2008無法使用的語法
  - 變更檔案: 1 個
- **2018-05-30 15:17:02**: 移除copyfile及db內的@iso，改由獨立模組自行維護
  - 變更檔案: 43 個

### arielshih (1 commits)

- **2018-06-19 13:47:47**: No number
  - 變更檔案: 5 個

## 詳細變更記錄

### 1. 修正 : AccessToken驗證完後，儲存userInfo時傳入的Key有誤
- **Commit ID**: `e9a36d8ba04e9f01461b489a8ae3a33618d651b3`
- **作者**: joseph
- **日期**: 2018-07-18 16:14:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`

### 2. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `430cf93442d6313c2017156e31e482b01ed27fc2`
- **作者**: jd
- **日期**: 2018-07-18 15:36:43
- **變更檔案數量**: 0

### 3. 新增經銷管理模組-透過互連中心取User資訊
- **Commit ID**: `4a10b11f1d6897ce33232b5bb6a762babbeffed6`
- **作者**: waynechang
- **日期**: 2018-07-18 15:30:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java`

### 4. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `0b5fb5d0eec7631f4bcd71cd54f6e36f736d2047`
- **作者**: jd
- **日期**: 2018-07-18 15:27:43
- **變更檔案數量**: 0

### 5. 新增移動表單客制開窗功能,提供Window.Open效果函式
- **Commit ID**: `b862b2699b6a1279ae436f476fe3f3def1e36bd5`
- **作者**: jd
- **日期**: 2018-07-18 15:26:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 6. 補上行動版加簽功能新樣式漏merge的部分
- **Commit ID**: `d6ba9d490857e4ea9e3a2e9de85fc3cac1363842`
- **作者**: ChinRong
- **日期**: 2018-07-18 14:14:41
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/addPostActivity.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/addPreActivity.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/historyDierect_select.png`

### 7. 修正鼎捷移動中間層簽核後沒有返回到列表
- **Commit ID**: `bee20d6529ac7b2a902a3b7afdb3586883f1f535`
- **作者**: 治傑
- **日期**: 2018-07-18 13:55:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationButtonRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 8. 補上行動版快搜功能共用方法中漏掉的程式碼
- **Commit ID**: `3d3ee96788f14c954876263dd5617c5b7dae4427`
- **作者**: ChinRong
- **日期**: 2018-07-18 11:57:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 9. 序號邏輯微調
- **Commit ID**: `019ffc025291dc5a3334a9afc711c6c4e181eae0`
- **作者**: jerry1218
- **日期**: 2018-07-18 11:23:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 10. 借貨邏輯調整
- **Commit ID**: `e0c62fe67063896399d43801af989e46af312af7`
- **作者**: jerry1218
- **日期**: 2018-07-18 10:14:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 11. 提升引用資源版本號的可讀性(Timestamp + 實際日期與時間)
- **Commit ID**: `588945e49b68e5e0f6c4edbbfa6c346dd49d0603`
- **作者**: Gaspard
- **日期**: 2018-07-18 09:35:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`

### 12. 修正議題 1.修正鼎捷移動列表用時間過濾異常 2.修正鼎捷移動中間層退回重辦後關卡無簽核意見
- **Commit ID**: `44f4df12f5d00a62ac3169ae9b28e5a46941da13`
- **作者**: 治傑
- **日期**: 2018-07-17 19:17:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 13. 修正5.7.2.1的Oracle DDL欄位長度過長,導致流程設計師無法使用問題。
- **Commit ID**: `5a9b5df223172c62860e5f29215167f2a60dd743`
- **作者**: jd
- **日期**: 2018-07-17 17:37:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql`

### 14. Q00-20180717002 修正安裝密碼註冊-無法刪除不屬於此Mac的License(取消註冊失敗)
- **Commit ID**: `a728da0c9aa5720e7d4c071774cb0b107710d933`
- **作者**: jerry1218
- **日期**: 2018-07-17 17:27:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java`

### 15. 移除多餘System.out
- **Commit ID**: `54c42c4701b025a431f15d2386d606ce8200b325`
- **作者**: jerry1218
- **日期**: 2018-07-17 17:26:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 16. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `b20710f341ee86331c255bb167d83eccfa3ecf57`
- **作者**: 治傑
- **日期**: 2018-07-17 12:06:21
- **變更檔案數量**: 0

### 17. 修正議題 1.修正鼎捷移動列表使用時間過濾異常 2.將整合設定中詳情關連應用設定功能鎖上 3.將鼎捷移動連續簽核功能鎖上
- **Commit ID**: `5e2845fbd3d93805c185a8571ebe432b4ea76c34`
- **作者**: 治傑
- **日期**: 2018-07-17 12:05:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`

### 18. 修正 : 整合表單同步Merge 儲存後 ,原先新增元件標註的顏色不會復原
- **Commit ID**: `f6589aa768ef8e9eb1082cf5e8e2211199e7121a`
- **作者**: joseph
- **日期**: 2018-07-17 11:47:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java`

### 19. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `31e83d425b13e05d1bb20f8553a4ae20fb0c03c3`
- **作者**: ChinRong
- **日期**: 2018-07-17 10:13:46
- **變更檔案數量**: 0

### 20. 修正議題
- **Commit ID**: `ecfd69788a3a118f3a2e1e3efcdc178c4804be9a`
- **作者**: ChinRong
- **日期**: 2018-07-17 10:13:16
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`

### 21. 修正Grid在連續修改時表單內容沒有帶值問題
- **Commit ID**: `cdc94bb00364acf1d494a57501ce88bb9529798c`
- **作者**: yamiyeh10
- **日期**: 2018-07-17 10:10:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`

### 22. Q00-20180620001修改組織設計師按下多語系的開窗後，再次開啟沒有多語系值暫存問題。
- **Commit ID**: `50401890b1d2496375861ca25f240de47f867660`
- **作者**: 施廷緯
- **日期**: 2018-07-16 18:07:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/extend_swing/AbstractDesignerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`

### 23. Q00-20180716002 修正 1.我的關注中，處理中的流程與授權的流程數量異常 2.個人化首頁串我的關注轉導致錯誤頁面(跑到追蹤流程) 3.我的關注中,未判斷關注流程&重要流程
- **Commit ID**: `f8894a7516f3b6223e6575cf516e651eabc28524`
- **作者**: jerry1218
- **日期**: 2018-07-16 16:05:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 24. 上傳IReport定義檔時，多增加驗證定義檔代號不可為空
- **Commit ID**: `6ceb71688b7f3192293d35cb6fc57979f1835ac9`
- **作者**: Gaspard
- **日期**: 2018-07-16 14:09:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 25. QRCode元件增加getData方法，可將產生的圖片以base64匯出
- **Commit ID**: `da2cb4a847f2fdc13f38874b8efa0881aef07b49`
- **作者**: Gaspard
- **日期**: 2018-07-16 13:51:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-qrcode.js`

### 26. Q00-20180627001 修正BPM57版本,離職人員ID含有單引號開窗會發生異常。
- **Commit ID**: `e2f669d57d8b81684ae5642b978e8bde60ea65aa`
- **作者**: 顏伸儒
- **日期**: 2018-07-16 12:08:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`

### 27. Q00-20180716001 修正當使用者再兩個部門 , 其中非主部門為主管時 , 個人化首頁會產生圖表異常
- **Commit ID**: `4c808599a3b24412a1c08aa032dd6c8063515e42`
- **作者**: jerry1218
- **日期**: 2018-07-16 12:04:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 28. 優化簽名元件匯出base64字串時，移除「data:image/png;base64,」字眼
- **Commit ID**: `c2844cd7709ea00cb5754da3eddf195bba3481fe`
- **作者**: Gaspard
- **日期**: 2018-07-16 11:44:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`

### 29. 移除工具列上按鈕描述的多餘符號
- **Commit ID**: `dad5a0e5aa5401f517e21acaa3c5111bf83c9afd`
- **作者**: Gaspard
- **日期**: 2018-07-16 09:55:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 30. 修改工具列上之待辦數量呈現的樣式
- **Commit ID**: `d253e5963e643e44c397cca44fb584b3dd9e73f3`
- **作者**: Gaspard
- **日期**: 2018-07-16 09:11:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 31. 修正DoubleTextBox在表單中間層顯示異常
- **Commit ID**: `d14e3deabda19e54184da6df491e92ef7743af3f`
- **作者**: ChinRong
- **日期**: 2018-07-13 15:05:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 32. Q00-20180713001 修正APP借貨到期依然可以登入的問題
- **Commit ID**: `8cedb8a3950d11f012597eae4dd94b45d2228192`
- **作者**: jerry1218
- **日期**: 2018-07-13 14:16:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`

### 33. 修正入口平台微信編輯沒有卡控根組織ID最多兩個字元的問題
- **Commit ID**: `abe77f130343b854620ebe3867d4531464ff195a`
- **作者**: ChinRong
- **日期**: 2018-07-13 13:50:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`

### 34. 修正BPMApp未開啟時，提示畫面沒有多語系
- **Commit ID**: `273df00e7a32354011be3ad6283aea4a3b07c69c`
- **作者**: 治傑
- **日期**: 2018-07-13 13:39:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 35. 調整行動版取回重辦、開窗新樣式
- **Commit ID**: `112600fc12452e402e36c32cf2d2a5ac9070e626`
- **作者**: ChinRong
- **日期**: 2018-07-13 10:47:10
- **變更檔案數量**: 23
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/mobileSelect.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobileSelect.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 36. 修改grid新增或編輯隱藏邏輯調整錯誤地方
- **Commit ID**: `d0cf24a559c8ce589b092ca4f01b4b1631a9e674`
- **作者**: pinchi_lin
- **日期**: 2018-07-12 18:13:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 37. 修正找不到FormUtil方法造成表單無法開啟及客製開窗無法使用的問題
- **Commit ID**: `2d664dd2e2948fd41a8eb41108e806119d7e138c`
- **作者**: ChinRong
- **日期**: 2018-07-12 15:09:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 38. 修正議題
- **Commit ID**: `a1292dfdbeaeabe152fe1e8132cb6c8a0d384cdb`
- **作者**: ChinRong
- **日期**: 2018-07-12 15:07:22
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 39. 修正鼎捷移動服務，列表篩選功能的重要性沒有多語系
- **Commit ID**: `8f054f7cd71a22dc38a25fa7a37c215ca07cc314`
- **作者**: jd
- **日期**: 2018-07-12 14:47:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BPMPerformRequestTool.java`

### 40. 修正互聯溝通格式增加屬性時，取使用者對照表會發生解析錯誤問題
- **Commit ID**: `04775fb8bbbfd3a0e399640012d96a629ebcd01d`
- **作者**: jd
- **日期**: 2018-07-12 14:46:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`

### 41. 修正老客升級時，沒有設定推播中間層會發生錯誤問題。
- **Commit ID**: `d55a08dfa3d4d1be0ea41e96b0d6a19859f3377e`
- **作者**: jd
- **日期**: 2018-07-12 14:45:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 42. 修正grid資料編輯至最後一筆後不跳回單身與grid資料編輯畫面按鈕跑版問題
- **Commit ID**: `96d86b42d1dba2fb683882c528a3e82a0c000666`
- **作者**: pinchi_lin
- **日期**: 2018-07-11 19:13:57
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 43. 調整鼎捷移動附件新樣式
- **Commit ID**: `cf60af1b319cf4bdeb2fcdb309ce4111c4ec46dd`
- **作者**: yamiyeh10
- **日期**: 2018-07-11 18:02:42
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`

### 44. 新增新樣式的清除鈕與調整password元件跑版
- **Commit ID**: `b6648bb133aad7fbe122bf2b6581c90515f8060f`
- **作者**: pinchi_lin
- **日期**: 2018-07-11 13:09:47
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css`

### 45. 調整行動版樣式及功能
- **Commit ID**: `8813f4a829cf007ec8a2adf1a5a816ece506c5f3`
- **作者**: ChinRong
- **日期**: 2018-07-11 12:22:33
- **變更檔案數量**: 24
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/SimpleDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css`

### 46. 修正元件清除條跑版問題(在多欄與不同螢幕大小時)
- **Commit ID**: `f82f872d0222d25b93cc4116795af497420ee0ec`
- **作者**: pinchi_lin
- **日期**: 2018-07-10 20:00:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 47. 修正PASSWORD元件跑板問題(在多欄與不同螢幕大小時)
- **Commit ID**: `78bbba3a31bf52bdaff2638a693b725cbaf05132`
- **作者**: pinchi_lin
- **日期**: 2018-07-10 19:09:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerInput.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 48. 修正元件清除條無法顯示與使用問題
- **Commit ID**: `14e8373144e45bbe3ad3ad60fea0e03642d06ced`
- **作者**: pinchi_lin
- **日期**: 2018-07-10 17:57:07
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerInput.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileProductOpenWin.js`

### 49. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `ce8c0e68a0cc5f96bfddbf76e5726d65df9c7e39`
- **作者**: ChinRong
- **日期**: 2018-07-10 15:58:57
- **變更檔案數量**: 0

### 50. C01-20180704002 修正鼎捷移動處理的流程透過流程分類篩選後取不到資料的議題
- **Commit ID**: `f164c13319e5a0ff4f2c7845de3d54025beab9aa`
- **作者**: ChinRong
- **日期**: 2018-07-10 15:58:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 51. Q00-*********** 修正借貨功能導致的異常 1.借貨邏輯異常 2.VIP所計算的總授權數異常(未考慮到期借貨) 3.借貨到期的情境administrator未強制導到註冊頁面
- **Commit ID**: `384cc90249b0eed976f80d566b0b9b41ddfcb24a`
- **作者**: jerry1218
- **日期**: 2018-07-10 15:57:12
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 52. S00-*********** 修正BPM57版本,公用片語管理的重設按鈕,易造成使用者混淆故刪除。
- **Commit ID**: `6dd009c501aeefc7a4d7a91d0c8cafb4d0672095`
- **作者**: 顏伸儒
- **日期**: 2018-07-09 18:30:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`

### 53. 移除不必要的Log
- **Commit ID**: `6cdee95812c04cff388f9c8efb716e8e270db7cb`
- **作者**: joseph
- **日期**: 2018-07-09 15:02:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`

### 54. 新增 : 簽核歷程restful接口
- **Commit ID**: `8ab251fc0345183a96182c49fa135aaf93093504`
- **作者**: joseph
- **日期**: 2018-07-09 10:23:58
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessInstanceDTOFactoryDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessSignInfoParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessSignInfoReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessSignInfoStdDataReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 55. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `2555e08aaa9d26e415917128650c22c3f9172457`
- **作者**: joseph
- **日期**: 2018-07-09 10:19:37
- **變更檔案數量**: 0

### 56. 調整 : Restful 切面授權驗證機制
- **Commit ID**: `5c6e888a8e0abc077d6a8986d8b060b6c8f2e77c`
- **作者**: joseph
- **日期**: 2018-07-09 10:19:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java`

### 57. 修正5722 oracle update SQL錯誤(to_date)
- **Commit ID**: `70671c3a142ccf83e4e2c89098fb37962cb42297`
- **作者**: jerry1218
- **日期**: 2018-07-09 09:58:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_Oracle_1.sql`

### 58. 修正當菜單載入速度大於template時，工具列按鈕無法順利載入的異常
- **Commit ID**: `bb274d9f6510843bc4fcfb270006b500cb6f581d`
- **作者**: Gaspard
- **日期**: 2018-07-08 19:38:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 59. 修正當菜單的iframe載入比主畫面iframe慢時導致無法setTitle的異常
- **Commit ID**: `678e2eb1a18ab1251f4ebf8e95ba331930b53593`
- **作者**: Gaspard
- **日期**: 2018-07-08 18:17:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 60. 在地化議題修正
- **Commit ID**: `a9f46e71dda3ea3c6254b1b01d21683c4f40755f`
- **作者**: Gaspard
- **日期**: 2018-07-06 22:33:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 61. 修正鼎捷移動發起流程列表概要字段為流程分類名稱時Label會顯示undefined的問題
- **Commit ID**: `7764c94ace11914f66de535760d5625e19e2b9a1`
- **作者**: ChinRong
- **日期**: 2018-07-06 17:58:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 62. 修正鼎新版本工具列ICON顯示的特殊邏輯
- **Commit ID**: `98141a1de14ca582aa9c732309a1bd86749d8273`
- **作者**: Gaspard
- **日期**: 2018-07-06 17:42:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 63. 在地化功能調整與異常修正
- **Commit ID**: `2cf833bf7c933b46f7f1c4df678052316a611b14`
- **作者**: Gaspard
- **日期**: 2018-07-06 15:10:50
- **變更檔案數量**: 30
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IPerformWorkItemHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/RmiRegistry.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/PerformWorkItemHandlerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MainMenuManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-server2-critical-Process-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-server2-critical-Process.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-server2-focus-Process-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-server2-focus-Process.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-server2-notice-list-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-server2-notice-list.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-server2-todo-list-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-server2-todo-list.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-server2-trace-process-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-server2-trace-process.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-trace-process-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-trace-process.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/perform-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/perform.png`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 64. 切割BPMAPP新樣式檔案路徑與還原舊有檔案
- **Commit ID**: `629ae79feb558865042e427a12ef20e1fb294ec6`
- **作者**: yamiyeh10
- **日期**: 2018-07-06 13:52:25
- **變更檔案數量**: 880
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BPMProcessTracing.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListContact.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListNotice.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListToDo.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTraceInvoked.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTracePerformed.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppMenu.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmTaskManage.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmWorkItem.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmWorkItemShell.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmWorkPublic.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleForm.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleFormTodo.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileNotice.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTraceInvoked.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTracePerform.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/lang/cn.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/lang/de.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/lang/es.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/lang/fr.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/lang/it.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/lang/nl.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/lang/pt.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/lang/ru.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/lib/aw.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/_button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/_checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/_combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/_grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/_icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/_radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/_tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/_tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/bg1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/bg2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/aqua/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/classic/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/classic/checkbox1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/classic/checkbox2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/classic/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/classic/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/classic/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/classic/radio1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/classic/radio2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/classic/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/mono/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/mono/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/mono/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/mono/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/mono/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/mono/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/mono/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_aqua-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_aqua-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_aqua-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_aqua-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_aqua-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_aqua-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_aqua-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_aqua-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_vista-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_vista-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_vista-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_vista-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_vista-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_xp-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_xp-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_xp-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_xp-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_xp-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/_xp-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-bg1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-bg2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aqua-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/classic-checkbox1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/classic-checkbox2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/classic-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/classic-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/classic-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/classic-radio1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/classic-radio2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/classic-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-g4.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-tabs1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-tabs2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/vista-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/xp-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/xp-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/xp-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/xp-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/xp-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/xp-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/xp-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/system/xp-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/_button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/_checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/_icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/_radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/_tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/g4.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/tabs1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/tabs2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/vista/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/_button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/_checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/_icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/_radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/_tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/_tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ActiveWidgets264/styles/xp/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/AppModalDialog.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/Dialog.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ModalDialog.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/OpenWin.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/aw.min.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ds-grid-aw.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/ds.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/dsMobile.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/form/popup.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/Map.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/MobileAppGrid.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/MobileProductOpenWin.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/index.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/ajax-loader.gif`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/action-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/action-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/alert-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/alert-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-l-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-l-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-r-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-r-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-l-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-l-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-r-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-r-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-l-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-l-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-r-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-r-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/audio-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/audio-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/back-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/back-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bars-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bars-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bullets-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bullets-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/calendar-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/calendar-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/camera-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/camera-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-d-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-d-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-l-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-l-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-r-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-r-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-u-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-u-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/check-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/check-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/clock-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/clock-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/cloud-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/cloud-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/comment-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/comment-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/delete-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/delete-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/edit-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/edit-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/eye-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/eye-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forbidden-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forbidden-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forward-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forward-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/gear-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/gear-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/grid-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/grid-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/heart-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/heart-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/home-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/home-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/info-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/info-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/location-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/location-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/lock-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/lock-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/mail-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/mail-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/minus-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/minus-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/navigation-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/navigation-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/phone-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/phone-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/plus-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/plus-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/power-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/power-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/recycle-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/recycle-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/refresh-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/refresh-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/search-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/search-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/shop-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/shop-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/star-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/star-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/tag-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/tag-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/user-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/user-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/video-black.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/video-white.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/action-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/action-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/alert-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/alert-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-l-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-l-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-r-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-r-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-l-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-l-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-r-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-r-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-l-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-l-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-r-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-r-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/audio-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/audio-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/back-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/back-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bars-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bars-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bullets-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bullets-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/calendar-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/calendar-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/camera-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/camera-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-d-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-d-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-l-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-l-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-r-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-r-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-u-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-u-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/check-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/check-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/clock-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/clock-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/cloud-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/cloud-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/comment-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/comment-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/delete-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/delete-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/edit-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/edit-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/eye-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/eye-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forbidden-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forbidden-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forward-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forward-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/gear-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/gear-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/grid-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/grid-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/heart-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/heart-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/home-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/home-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/info-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/info-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/location-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/location-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/lock-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/lock-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/mail-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/mail-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/minus-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/minus-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/navigation-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/navigation-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/phone-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/phone-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/plus-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/plus-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/power-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/power-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/recycle-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/recycle-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/refresh-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/refresh-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/search-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/search-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/shop-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/shop-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/star-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/star-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/tag-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/tag-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/user-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/user-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/video-black.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/video-white.svg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.map`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.min.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.structure.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.structure.min.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.theme.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.theme.min.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.external-png-1.4.5.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.external-png-1.4.5.min.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.icons-1.4.5.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.icons-1.4.5.min.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-png-1.4.5.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-png-1.4.5.min.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-svg-1.4.5.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-svg-1.4.5.min.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.structure-1.4.5.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.structure-1.4.5.min.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.theme-1.4.5.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.theme-1.4.5.min.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jquery-1.8.3.min.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/snap.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/system/BpmMobileLibrary.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/system/BpmMobilePublic.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileCustomOpenWin.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileGrid.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileLibrary.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileProductOpenWin.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileTool.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/system/knockout-3.2.0.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/system/knockout.mapping.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/system/utab.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BPMProcessTracing.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListContact.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppMenu.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmTaskManage.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmWorkItem.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmWorkItemShell.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmWorkPublic.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileCustomOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTool.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/aw.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/lang/cn.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/lang/de.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/lang/es.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/lang/fr.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/lang/it.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/lang/nl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/lang/pt.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/lang/ru.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/lib/aw.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/_combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/_grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/_tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/bg1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/bg2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/aqua/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/classic/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/classic/checkbox1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/classic/checkbox2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/classic/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/classic/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/classic/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/classic/radio1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/classic/radio2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/classic/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/mono/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/mono/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/mono/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/mono/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/mono/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/mono/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/mono/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_aqua-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_aqua-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_aqua-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_aqua-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_aqua-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_aqua-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_aqua-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_aqua-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_vista-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_vista-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_vista-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_vista-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_vista-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_xp-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_xp-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_xp-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_xp-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_xp-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/_xp-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-bg1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-bg2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aqua-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/classic-checkbox1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/classic-checkbox2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/classic-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/classic-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/classic-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/classic-radio1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/classic-radio2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/classic-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-g4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-tabs1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-tabs2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/vista-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/xp-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/xp-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/xp-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/xp-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/xp-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/xp-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/xp-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/system/xp-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/g4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/tabs1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/tabs2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/vista/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/_tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ActiveWidgets264/styles/xp/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/AppModalDialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/Dialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ModalDialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/OpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/aw.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ds-grid-aw.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/ds.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/dsMobile.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/popup.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/Map.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/MobileAppGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/index.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/ajax-loader.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/action-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/action-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/alert-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/alert-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-d-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-d-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-d-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-d-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-d-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-d-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-u-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-u-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-u-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-u-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-u-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/arrow-u-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/audio-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/audio-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/back-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/back-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/bars-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/bars-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/bullets-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/bullets-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/calendar-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/calendar-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/camera-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/camera-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/carat-d-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/carat-d-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/carat-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/carat-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/carat-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/carat-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/carat-u-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/carat-u-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/check-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/check-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/clock-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/clock-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/cloud-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/cloud-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/comment-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/comment-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/delete-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/delete-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/edit-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/edit-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/eye-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/eye-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/forbidden-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/forbidden-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/forward-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/forward-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/gear-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/gear-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/grid-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/grid-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/heart-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/heart-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/home-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/home-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/info-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/info-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/location-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/location-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/lock-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/lock-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/mail-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/mail-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/minus-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/minus-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/navigation-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/navigation-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/phone-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/phone-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/plus-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/plus-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/power-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/power-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/recycle-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/recycle-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/refresh-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/refresh-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/search-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/search-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/shop-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/shop-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/star-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/star-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/tag-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/tag-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/user-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/user-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/video-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-png/video-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/action-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/action-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/alert-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/alert-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-d-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-d-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-d-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-d-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-d-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-d-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-u-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-u-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-u-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-u-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-u-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/arrow-u-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/audio-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/audio-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/back-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/back-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/bars-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/bars-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/bullets-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/bullets-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/calendar-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/calendar-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/camera-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/camera-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/carat-d-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/carat-d-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/carat-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/carat-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/carat-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/carat-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/carat-u-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/carat-u-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/check-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/check-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/clock-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/clock-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/cloud-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/cloud-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/comment-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/comment-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/delete-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/delete-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/edit-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/edit-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/eye-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/eye-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/forbidden-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/forbidden-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/forward-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/forward-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/gear-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/gear-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/grid-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/grid-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/heart-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/heart-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/home-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/home-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/info-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/info-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/location-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/location-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/lock-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/lock-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/mail-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/mail-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/minus-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/minus-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/navigation-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/navigation-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/phone-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/phone-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/plus-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/plus-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/power-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/power-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/recycle-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/recycle-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/refresh-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/refresh-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/search-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/search-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/shop-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/shop-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/star-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/star-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/tag-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/tag-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/user-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/user-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/video-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/images/icons-svg/video-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile-1.4.5.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile-1.4.5.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile-1.4.5.min.map`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.custom.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.custom.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.custom.structure.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.custom.structure.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.custom.theme.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.custom.theme.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.external-png-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.external-png-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.icons-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.icons-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.inline-png-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.inline-png-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.inline-svg-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.inline-svg-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.structure-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.structure-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.theme-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jQM/jquery.mobile.theme-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jquery-1.8.3.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jweixin-1.0.0.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/snap.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmMobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmMobilePublic.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileSubTab.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileTool.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/knockout-3.2.0.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/knockout.mapping.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/utab.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/bpmApp/BpmFormatMaterialize.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/file_thumbnail.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 65. A00-20180416001 修正 : 當Grid欄位設定與日期元件代號相同,而且剛好先產生Grid資料後產生Date資料時,會導致 FormData XML 中 Grid的資料型態錯誤 ,導致轉存表單無法存入
- **Commit ID**: `cffe3373bf09a8e1539f8814b1a25118722eafd4`
- **作者**: joseph
- **日期**: 2018-07-06 11:25:13
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 66. 調整 :E10CreateSQL 命名
- **Commit ID**: `0627916e78a97073462827fa6c9fc2d4f7e00d33`
- **作者**: joseph
- **日期**: 2018-07-06 11:22:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@E10/create/5.7.2.1_E10_DML_MSSQL_1.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@E10/create/5.7.2.1_E10_DML_Oracle_1.sql`

### 67. 調整 : 產品序號欄位的欄位長度為100
- **Commit ID**: `757fcce7d459073c3ebdf060f7aa081f50eb20c3`
- **作者**: joseph
- **日期**: 2018-07-06 11:21:32
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.7.2.2_AppForm_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.7.2.2_AppForm_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@plm/CreateTable_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@plm/CreateTable_SQLServer2005.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@plm/update/5.7.2.2_PLM_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@plm/update/5.7.2.2_PLM_DDL_Oracle_1.sql`

### 68. 修改5.7.1.2_DML_Oracle_1.sql - 因內含的日期update語法有誤
- **Commit ID**: `3f1e3fd269aca13a9b7551f6ab596de7e35d8a7b`
- **作者**: jerry1218
- **日期**: 2018-07-06 09:17:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.2_DML_Oracle_1.sql`

### 69. 隱藏未完成的資料選取註冊器功能
- **Commit ID**: `c4895055bb71cf8455888948ac6edc4f9d9fb2dc`
- **作者**: Gaspard
- **日期**: 2018-07-05 16:25:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_Oracle_1.sql`

### 70. 隱藏5722尚未完成的功能與SQL
- **Commit ID**: `165cdefe86017f2d318781ce17d5e59080e50fc9`
- **作者**: Gaspard
- **日期**: 2018-07-05 15:44:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_Oracle_1.sql`

### 71. 增加表單設計藍圖功能
- **Commit ID**: `e84c548f7bed7c1f3fda71f87b9a83ff7f8cd82b`
- **作者**: Gaspard
- **日期**: 2018-07-05 15:33:21
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDesigner-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/AbsoluteFormBluePrint.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 72. A00-20180705001 修正鼎捷移動推播網址錯誤導致無法開啟表單畫面問題
- **Commit ID**: `114989821d45555c57a8b32f6194672f2a98c548`
- **作者**: pinchi_lin
- **日期**: 2018-07-05 13:47:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 73. 調整App簽核歷程頁面
- **Commit ID**: `d6fbdbb54f5be31802282c8df03f09ad9f4f7664`
- **作者**: ChinRong
- **日期**: 2018-07-04 17:48:45
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 74. Q00-20180704002 調整Forminstance.fieldValues的XML排版1.不會隨派送過程上下tag間距增加2.產生的XML的排版至左。
- **Commit ID**: `d3ea15264004b3f6c31e3e9ec8ea74ac62a17477`
- **作者**: 施廷緯
- **日期**: 2018-07-04 16:11:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/Dom4jUtil.java`

### 75. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `acecc6c6ba63af1f2350b374d03bf773c8426a9f`
- **作者**: Gaspard
- **日期**: 2018-07-04 15:45:17
- **變更檔案數量**: 0

### 76. 儲存表單時，手寫元件的資料使用ntext或CLOB裝載
- **Commit ID**: `28a12db3543e949a3965c66da73386e556622990`
- **作者**: Gaspard
- **日期**: 2018-07-04 15:44:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 77. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `47019a941f503dfa4048fb346ebceec7e06d99f9`
- **作者**: 施廷緯
- **日期**: 2018-07-04 15:05:33
- **變更檔案數量**: 0

### 78. 同C01-20180702002，同Formdispatch為false會一直loading問題，針對"派送"做修正。
- **Commit ID**: `16d7a6268b5acfb6e619d0826d364ebf0a059d38`
- **作者**: 施廷緯
- **日期**: 2018-07-04 15:01:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 79. 調整APP函式庫錯誤訊息內容
- **Commit ID**: `e4591b7145133d0132aca9b79ea98fa6cfc9e953`
- **作者**: pinchi_lin
- **日期**: 2018-07-04 14:58:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 80. A00-20180615004 修正草稿流程議題後導致發單畫面無法開啟問題
- **Commit ID**: `75ad268134a9934778dbdc93bd9e7c18ee960645`
- **作者**: yamiyeh10
- **日期**: 2018-07-04 11:45:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 81. C01-20180702002  修正FormDispatch為False時，發起流程畫面會停在Loading.
- **Commit ID**: `4e295671bd243762433713ea78fba1186a18bcd2`
- **作者**: 施廷緯
- **日期**: 2018-07-04 10:34:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 82. 修正使用Android點選客製開窗時，畫面會滑動
- **Commit ID**: `7f72a716af890549db9d63694c8c9b25352b4ff8`
- **作者**: 治傑
- **日期**: 2018-07-03 17:52:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`

### 83. 修正APP元件函式庫顯示與隱藏功能造成跑板問題
- **Commit ID**: `8d8dc5fd357080b0994bd6f8de1d79aa1671e900`
- **作者**: pinchi_lin
- **日期**: 2018-07-03 17:21:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 84. 調整發起、待辦、追蹤、通知的流程主旨、簽核意見與選擇部門樣式 調整FormUtil取得主旨ID 調整退回重辦只有一個選項時樣式 新增流程主旨、簽核意見與選擇部門樣式多語系
- **Commit ID**: `4df424b8eafa049b7d4ba926bdaebd052d6bea50`
- **作者**: yamiyeh10
- **日期**: 2018-07-03 16:45:24
- **變更檔案數量**: 26
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/BpmFormatMaterialize.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 85. 開啟取元件類別中的log
- **Commit ID**: `c363cacc1adf177037ef4981f4eb66a5abba364f`
- **作者**: pinchi_lin
- **日期**: 2018-07-03 14:43:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 86. 修正merge錯誤
- **Commit ID**: `14eef2166680b3a664ad2465becf1bdd8181018d`
- **作者**: 治傑
- **日期**: 2018-07-03 14:01:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 87. 調整App函式庫複合元件CSS功能
- **Commit ID**: `36ce2aca2c9447d55908bb05116375debb0a8b5c`
- **作者**: 治傑
- **日期**: 2018-07-03 09:30:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 88. 多增加絕對位置表單轉換成RWD表單功能，同時表單可不進版
- **Commit ID**: `119fa049b34ef7c51336191a0833e264e329429d`
- **作者**: Gaspard
- **日期**: 2018-07-02 15:22:17
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDesigner-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 89. A00-20180510003 修正BPM57版本,退回重辦時,將取得參予者的資料加上防呆。
- **Commit ID**: `9b9684b6bb256677b04e1d2dec90f13505961dc9`
- **作者**: 顏伸儒
- **日期**: 2018-07-02 12:54:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 90. A00-20180702001 修正退回重辦時,未點選欲退回關卡即按下確定後會導致錯誤的問題
- **Commit ID**: `85d1bd92754146b5af388f74e82c413e5440c4fc`
- **作者**: jerry1218
- **日期**: 2018-07-02 11:44:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 91. 修正Web表單設計器，檢視表單修改歷程後，欲取出某一版表單時，只能使用絕對位置的編輯器開啟
- **Commit ID**: `683dfb92a4ec20e3b2e507bb814b502a95f8443f`
- **作者**: Gaspard
- **日期**: 2018-07-02 09:57:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`

### 92. 加入HandWriting元件
- **Commit ID**: `7d7282bdc1776d83ae2b1feec64cd0a5c37b928e`
- **作者**: Gaspard
- **日期**: 2018-06-29 17:12:39
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/HandWritingElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HandWritingElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/resources/html/HandWritingTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/jSignature.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 93. 二次修正欄位名稱過常造成Oracle無法正常寫讀取/寫入資料的問題
- **Commit ID**: `e201909f34d3bdaf9885d7757f746f9ad549043c`
- **作者**: ChinRong
- **日期**: 2018-06-29 16:52:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`

### 94. 修正欄位名稱過常造成Oracle無法正常寫讀取/寫入資料的問題
- **Commit ID**: `9deb8f12f3483f36b2d8f2a9ea96a80db37c4e13`
- **作者**: ChinRong
- **日期**: 2018-06-29 16:50:04
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql`

### 95. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `7a60559f9843c11d7aec43076455e59d49b70576`
- **作者**: Gaspard
- **日期**: 2018-06-29 16:24:01
- **變更檔案數量**: 0

### 96. 修改FormUtil的提示訊息為英文提示
- **Commit ID**: `a0d39400a173646a8231acaf9a1423b481959efe`
- **作者**: Gaspard
- **日期**: 2018-06-29 16:23:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 97. 新增 : E10整合SQL
- **Commit ID**: `5e9f7ecee5af883f0bd73c3188ff2eb53de44dee`
- **作者**: joseph
- **日期**: 2018-06-29 16:13:45
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@E10/create/5.7.2.1_E10_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@E10/create/5.7.2.1_E10_DML_Oracle_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@E10/update/5.7.2.2_E10_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@E10/update/5.7.2.2_E10_DDL_Oracle_1.sql`

### 98. 開發APP元件函式庫的元件顯示功能與調整隱藏功能邏輯
- **Commit ID**: `11524574f756f47958dcd4d4be2b815a79d98e42`
- **作者**: pinchi_lin
- **日期**: 2018-06-29 14:18:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 99. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `23a91ab18b40d029517f58c05febd71288f23b3a`
- **作者**: Gaspard
- **日期**: 2018-06-29 12:00:38
- **變更檔案數量**: 0

### 100. 修正開啟腳本樣版視窗的情況下在點擊右上關閉，全域變數清單不會顯示的異常
- **Commit ID**: `55f9528f4b391454db5ffb4c95fb3a879db10cfb`
- **作者**: Gaspard
- **日期**: 2018-06-29 12:00:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 101. Q00-20180629001 版更PDFViewer的jar 及替換寫法
- **Commit ID**: `0bf74155813453ef16c74d21ad0fa3af922fec45`
- **作者**: waynechang
- **日期**: 2018-06-29 11:06:43
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/lib/ICEpdf/icepdf-core.jar`
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/lib/ICEpdf/icepdf-viewer.jar`
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/src/com/dsc/nana/user_interface/pdf/efgp_pdfViewer/controller/SecurityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-core.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-pro-intl.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-pro.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-viewer.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/levigo-jbig2-imageio.jar`

### 102. 資料選取註冊器，修正原始SQL有order by時，動態組成SQL的方式
- **Commit ID**: `b99ed02ebe8a0aed3d390c20aaec4cc3c4f67e38`
- **作者**: Gaspard
- **日期**: 2018-06-29 10:29:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 103. 移除全形的SQL語法
- **Commit ID**: `7108eb4c6ddb5173b488610b3e52268721b6c047`
- **作者**: Gaspard
- **日期**: 2018-06-29 10:17:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`

### 104. A00-20180628001 修正BPM57版本,修正T100操作刪除簽核流程時會執行的LOG。
- **Commit ID**: `e2316fc857ed8ee32f4036985b184194d740f20d`
- **作者**: 顏伸儒
- **日期**: 2018-06-28 19:33:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/BpmServiceAPIBean.java`

### 105. 補上漏掉的退回重辦jsp
- **Commit ID**: `8fe8e108c8d587fb05a66baf87c1da1d670430ea`
- **作者**: yamiyeh10
- **日期**: 2018-06-28 19:29:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`

### 106. 調整App退回重辦樣式
- **Commit ID**: `b6674c5d58a6743766629453beba2752b0b29383`
- **作者**: yamiyeh10
- **日期**: 2018-06-28 19:26:06
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/historyDierect.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/historyDierect_before.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/historySequence.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/historySequence_before.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 107. 修正App函式庫中RadioButton取值方法
- **Commit ID**: `e1d61a83382a80d42b2d445b901cb2b292d5c3d9`
- **作者**: ChinRong
- **日期**: 2018-06-28 18:56:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 108. 變更scan-interval為30秒，減少因為IO來不及讀完NaNaWeb.war導致無法順利部署的問題
- **Commit ID**: `684c39b386f3cf1a8748267342c6fbcb29c2a190`
- **作者**: lorenchang
- **日期**: 2018-06-28 17:20:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 109. 開發APP元件函式庫的元件禁用功能與修正附件按鈕樣板id錯誤問題
- **Commit ID**: `b97852d4be2bb6bd6734c23f1e6b9995c4e2b3c9`
- **作者**: pinchi_lin
- **日期**: 2018-06-28 16:58:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 110. 調整App函式庫
- **Commit ID**: `ba8d079d76405ea2b7ea1b9beebff528f3ac3136`
- **作者**: ChinRong
- **日期**: 2018-06-28 16:08:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 111. 新增 : 整合產品表單同步Merge功能
- **Commit ID**: `a1876d5efdf63a0f219df1432e89013da1a5d2c3`
- **作者**: joseph
- **日期**: 2018-06-28 16:00:57
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java`

### 112. 新增 :RESTful發起流程接口，紀錄流程發起資訊在ProcessMappingKey資料表
- **Commit ID**: `0eb26fd0a77d6517f8ede380bdb14776145391a9`
- **作者**: joseph
- **日期**: 2018-06-28 14:58:18
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/util/WriteBackRecord.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/IPrsMappingKeyDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/OJBPrsMappingKeyDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/EAIHeaderKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`

### 113. 修正 : RESTful 撤銷流程 ,因報錯內容過大導致Response異常
- **Commit ID**: `4f58a19777603c63eca55f1d8ddf2dbf1fd37abc`
- **作者**: joseph
- **日期**: 2018-06-28 14:45:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 114. 修正 : RestFul發單使用URLDecoder套件在編碼,因Form資料有&符號導致解析異常 ,調整為使用java源生字串編碼
- **Commit ID**: `9c14aef8811bb5fce9ae63302319c93802363734`
- **作者**: joseph
- **日期**: 2018-06-28 14:42:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 115. 新增 : E10功能 -子單身開窗
- **Commit ID**: `e478851229a3f165b0287a04d6e5ef59cdeae29b`
- **作者**: joseph
- **日期**: 2018-06-28 14:20:48
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormDefinitionJSONTransfer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/OpenWin/ChildGridChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-openWin-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/SubGridTransfer.js`

### 116. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `18610f330a3910260f4bc8bbd2f07c50d598a256`
- **作者**: yamiyeh10
- **日期**: 2018-06-28 11:47:19
- **變更檔案數量**: 0

### 117. C01-20180626002 修正入口平台整合設定中微信使用者管理搜尋功能異常
- **Commit ID**: `51801d53d01c1ff8a227145c54f6fa52820a16e6`
- **作者**: yamiyeh10
- **日期**: 2018-06-28 11:46:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentWeChateUser.js`

### 118. 修正安卓手機打開表單會出現undefined is not a function 的問題
- **Commit ID**: `8ae954c922d4dfd8e831bc134d9147f02fcf3fa5`
- **作者**: ChinRong
- **日期**: 2018-06-28 11:42:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`

### 119. 修正merge錯誤
- **Commit ID**: `10b76339e44636754a3e3a1f8254f248e26c0854`
- **作者**: ChinRong
- **日期**: 2018-06-28 09:38:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 120. 調整App函式庫
- **Commit ID**: `8df7cfbba348f8b805e19944cb914cf96c629134`
- **作者**: ChinRong
- **日期**: 2018-06-27 18:25:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 121. 調整水平線ID名稱 調整Title元件logo拿掉與調整ID名稱 補上Attachment元件漏掉icon 調整分頁、流程名稱、終止、撤銷樣式 調整按鈕、選項、開窗元件、DoubleTextBox元件
- **Commit ID**: `db973d321082cc13285f7fdebc2882a7e483225c`
- **作者**: yamiyeh10
- **日期**: 2018-06-27 18:18:42
- **變更檔案數量**: 23
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HorizontalLineElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 122. 開發APP元件函式庫labelcss功能
- **Commit ID**: `949acab0e6beaac4e8732ab959af3f48b52aec47`
- **作者**: 治傑
- **日期**: 2018-06-27 18:02:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 123. A00-20180620001 修正BPM57版本,設定個人資訊裡的預設代理人設定,和流程代理人設定裡面的起始與結束時間選取異常。
- **Commit ID**: `faed8dd1121aed1bbec13dc31290cf62b499159f`
- **作者**: 顏伸儒
- **日期**: 2018-06-27 16:02:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`

### 124. 開發APP元件函式庫的時間元件取值與設值功能
- **Commit ID**: `c402169c29240784dce7596448e68d6823f35a60`
- **作者**: pinchi_lin
- **日期**: 2018-06-27 15:50:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 125. A00-20180625001 修正BPM57版本,從T100端欲重建BPM流程時會執行到的判斷。
- **Commit ID**: `1adf51ed891ea7e87c3851a19afe7a0f99f30a75`
- **作者**: 顏伸儒
- **日期**: 2018-06-27 14:46:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 126. 補上工作通知表單資料表單來源欄位
- **Commit ID**: `65a0b33d0692fde0fab4a48550f89bb886529b64`
- **作者**: ChinRong
- **日期**: 2018-06-26 18:59:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`

### 127. A00-20180626001 修正SQL指令存在全形空白的字元導致異常。
- **Commit ID**: `f099ddcf8d0091e3f4502ba27464d99d3f7ce8be`
- **作者**: 施廷緯
- **日期**: 2018-06-26 18:49:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 128. 調整APP表單函示庫的輸入元件設值與取值以及設定主旨功能
- **Commit ID**: `57dc8ac44095f3a5b55d413e4bb6ba2f3f95869c`
- **作者**: pinchi_lin
- **日期**: 2018-06-26 16:48:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 129. 移除多餘LOG顯示
- **Commit ID**: `055f536a03159df9a55c4f3d34279aa3201efd84`
- **作者**: Gaspard
- **日期**: 2018-06-26 16:16:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 130. 增加表單的頁籤元件可透過流程設計器設定於各關卡是否顯示
- **Commit ID**: `7602d573264d5e91fe91f092ef5cbdd01bcafab0`
- **作者**: Gaspard
- **日期**: 2018-06-26 16:07:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SubTabElement.java`

### 131. 配合公司調整產品預設Port
- **Commit ID**: `5bb28962419c7d6fa0eb96f17bb6c7bcf297a717`
- **作者**: lorenchang
- **日期**: 2018-06-26 15:54:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 132. 表單設計器增加QRCode元件
- **Commit ID**: `f139f160259ff2336690657ce45dbde6a1e710f5`
- **作者**: Gaspard
- **日期**: 2018-06-26 14:07:16
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/QRCodeElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/QRCodeElement.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/resources/html/QRCodeTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/bpm-qrcode.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/qrcode.png`

### 133. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `19dd6e88ca392b3a25ace434ba5ce9a1c0426f2e`
- **作者**: 施廷緯
- **日期**: 2018-06-26 11:46:26
- **變更檔案數量**: 0

### 134. C01-20180621003 修正57測試機整合WF ERP單據撤銷、終止、回寫失敗。在WorkflowManager、WorkflowManagerLocal補上@Remoter及@Local。
- **Commit ID**: `8a04a69fb7644099b30ae8bc1669f53770e21a58`
- **作者**: 施廷緯
- **日期**: 2018-06-26 11:40:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManagerLocal.java`

### 135. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `d811ca76289a985d2c1eefacc058989e98064eee`
- **作者**: pinchi_lin
- **日期**: 2018-06-26 11:29:25
- **變更檔案數量**: 0

### 136. 新增APP用元件函式庫
- **Commit ID**: `cc67adb9bf24a35f1874acc7719afe976e16608d`
- **作者**: pinchi_lin
- **日期**: 2018-06-26 11:26:04
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormUtil.js`

### 137. 增加Grid元件列印表單時漏呼叫的載入後方法、修正名片式呈現時若無資料會顯示兩次無資料的異常
- **Commit ID**: `49fe8d6f5bc5db63b70ed1ec446f903af937d3a4`
- **作者**: Gaspard
- **日期**: 2018-06-26 11:25:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 138. 修正ESS整合中Q串F無法開啟的問題處理
- **Commit ID**: `1b8b50d02cbfbe29b7f2acb19b3162e3268c4ba0`
- **作者**: Gaspard
- **日期**: 2018-06-26 11:23:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp`

### 139. C01-20180624008 修正發起時能發單但有ReferenceError:goMenu is not defined的錯誤
- **Commit ID**: `a06592e0c2af6eb22e433af71bd753601df7489b`
- **作者**: pinchi_lin
- **日期**: 2018-06-26 10:14:24
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`

### 140. 修正企業微信使用者管理頁面的啟用狀態顯示為2
- **Commit ID**: `122aa60402657b40fdf7a813834a43202f1b33e3`
- **作者**: 治傑
- **日期**: 2018-06-25 17:26:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentWeChateUser.js`

### 141. A00-20180615002 修正文件總管，搜尋文件打開閱讀後(沒閱讀不會異常)，在搜尋時會發生找不到任何文件
- **Commit ID**: `39f313706a0a21efd09c599b49eaaf0168dcda5f`
- **作者**: waynechang
- **日期**: 2018-06-25 14:19:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 142. A00-20180612001 修正BPM57版本,查詢開窗輸入搜尋條件後,查詢的資料排序消失的問題。
- **Commit ID**: `cf6962ee40486de13fb634f599d81645aee72d91`
- **作者**: 顏伸儒
- **日期**: 2018-06-25 13:50:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 143. A00-20180615001 修正ISO文件權限屬性管理作業異常
- **Commit ID**: `3b9006ccccecdc0fcfbd3a98f7468bdb6fbd301f`
- **作者**: waynechang
- **日期**: 2018-06-25 13:42:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageAccessRightAction.java`

### 144. A00-20180613001 修正核決關卡取回重辦功能判斷異常
- **Commit ID**: `c26be6f8f4ba0ecfb4a411b4db7bf745af41014a`
- **作者**: waynechang
- **日期**: 2018-06-25 11:33:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 145. 修正鼎捷移動預設中間層不會取到Grid與附件的資料
- **Commit ID**: `027abaac8b5833dad84a279049e9d0200103e055`
- **作者**: ChinRong
- **日期**: 2018-06-25 10:36:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 146. 修正行動版Gird沒綁定新增鈕時會出現undefined按鈕
- **Commit ID**: `19a231f467e96bf034f2b140f1d4fed9049867c4`
- **作者**: 治傑
- **日期**: 2018-06-22 17:18:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`

### 147. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `0ff7d717a108f470a127d371a141a33443451ba7`
- **作者**: joseph
- **日期**: 2018-06-22 16:05:39
- **變更檔案數量**: 0

### 148. 移除 : 表單同步測試程式
- **Commit ID**: `e2a3e62a7d3df7152480c47d3482ccb9b25162df`
- **作者**: joseph
- **日期**: 2018-06-22 16:05:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/TestyJSONBean.java`

### 149. [C01-20180530003]修正BPM57版本,核決關卡沒有人簽核過,不顯示在退回重瓣的清單裡。
- **Commit ID**: `fa11073295046fb1e616c9be479e1cad0c4427c9`
- **作者**: 顏伸儒
- **日期**: 2018-06-22 15:56:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java`

### 150. 修正 : 整合產品表單同步時因static物件導致吃到舊的變數資料
- **Commit ID**: `a41b9aefe2ebb86158a740c5bb189b83958e18de`
- **作者**: joseph
- **日期**: 2018-06-22 15:39:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`

### 151. 新增 : 表單同步支援Date元件,以及加入E10產品時加入FormScript
- **Commit ID**: `8984794ec8380fd8ba4c43e62fe69c02c2a3dbde`
- **作者**: joseph
- **日期**: 2018-06-22 15:38:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormDefinitionJSONTransfer.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/E10Form.js`

### 152. 修正 : Cross 同步產品資訊時不會去改設定檔設定
- **Commit ID**: `14054ca930cfc6c483cbf3299251f156d5c6100d`
- **作者**: joseph
- **日期**: 2018-06-22 15:29:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java`

### 153. 調整 : 回寫整合產品簽核歷程機制
- **Commit ID**: `c4f142094105bf173462919c3fb8da46e03a730d`
- **作者**: joseph
- **日期**: 2018-06-22 15:27:46
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/WriteBackType2IntFieldConversion.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/util/WriteBackRecord.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/util/WriteBackType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/E10SendSignInfoBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10Manager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaIntSys.properties`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`

### 154. 調整 : 取得簽核歷程加入表單id
- **Commit ID**: `7901963e3295584a1b6ee9b4e1c24e1b8a3092c2`
- **作者**: joseph
- **日期**: 2018-06-22 14:18:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`

### 155. A00-20180530027 修正: ISO文管首頁 IE無法開啟檔案,以及檔名異常
- **Commit ID**: `10dc1277fe97b592688a36ca137dc613428e9eef`
- **作者**: joseph
- **日期**: 2018-06-22 14:16:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 156. Q00-20180619001 修正 : WebService fetchWorkFlowDiagram  接口 取URL不支援 Https
- **Commit ID**: `9bd93b65d20da14d4e5949ccc8206bff68dbf6a4`
- **作者**: joseph
- **日期**: 2018-06-22 14:08:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/SystemConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopModelManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`

### 157. 修正App頁籤功能異常問題 1.多頁籤時顯示異常 2.頁籤與子頁籤更改ID時顯示異常 3.選擇子頁籤時影響其餘頁籤顯示 4.子頁籤Value過長時畫面跑版
- **Commit ID**: `25580193a4a6ad79eb7ba5b50f436152f0118f85`
- **作者**: yamiyeh10
- **日期**: 2018-06-22 10:19:33
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SubTabElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 158. 增加FormUtil的共用method
- **Commit ID**: `f3dcfdb1e1469c9d8852e5278ddd7ae66a50ed6c`
- **作者**: Gaspard
- **日期**: 2018-06-22 09:00:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 159. 將「工作通知清單」與「監控流程」頁面的快搜按鈕，從查詢收合窗移到外層靠右排列
- **Commit ID**: `9d4c1d4738ab129c35f7b6f54f052d21d23a1057`
- **作者**: Gaspard
- **日期**: 2018-06-21 16:32:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 160. 增加資料選取註冊器功能
- **Commit ID**: `cc414bb9cedb65fe05e144c192ad69dbbc26b60b`
- **作者**: Gaspard
- **日期**: 2018-06-21 15:49:25
- **變更檔案數量**: 36
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/List2JsonStringConversion.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/ListIncludeMap2JsonStringConversion.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/Map2JsonStringConversion.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/CustomDataChooserConf.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/CustomDataChooserDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/CustomDataChooserTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IFormDefinitionDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/QueryDesinger.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_Oracle_1.sql`

### 161. 移除多餘的console.log
- **Commit ID**: `a86af1fa576c79593ec6b1e5a17fad812b9eff88`
- **作者**: ChinRong
- **日期**: 2018-06-21 15:09:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 162. 修正行動版RWD設計器標記中間層無效的問題
- **Commit ID**: `fd885ed94701446c3bc7c9ded07fc2b3c4b5d4a0`
- **作者**: ChinRong
- **日期**: 2018-06-21 09:04:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 163. 調整RESTful服務
- **Commit ID**: `df216f0b88c05e1b3f1d551398065728346eb94e`
- **作者**: ChinRong
- **日期**: 2018-06-20 19:08:26
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 164. 新增RESTful接口 1.終止流程 2.退回重辦 3.加簽關卡
- **Commit ID**: `1a45404dc81e6da40742b4ea3fdfcd431ff130e9`
- **作者**: yamiyeh10
- **日期**: 2018-06-20 18:37:04
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageActivitesReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackagePerformersReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 165. 簽核流設計師中加入行動版表單存取控管可以批次關卡設定
- **Commit ID**: `4b3247f9b8aafb7f16097c3978b4288f493afba8`
- **作者**: pinchi_lin
- **日期**: 2018-06-20 18:15:24
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`

### 166. 修正連續簽核功能,修正互聯V2參數問題
- **Commit ID**: `8d7a2274a0c309541abe1cc71457103b623f55df`
- **作者**: jd
- **日期**: 2018-06-20 18:01:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/RemoteUser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 167. 加入中台使用名稱
- **Commit ID**: `bff47d82598938ae8692ae073a0772d0d58f55d5`
- **作者**: 治傑
- **日期**: 2018-06-20 14:15:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java`

### 168. 修正行動版RWD設計器在修改子頁籤ID時會產生多的區塊
- **Commit ID**: `08699f2fbc81daf10ebd0de5838eaf4dc63db0da`
- **作者**: ChinRong
- **日期**: 2018-06-20 13:42:06
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 169. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `37c391314c38753e9dab59b35f8b6e881d767618`
- **作者**: Gaspard
- **日期**: 2018-06-20 10:21:41
- **變更檔案數量**: 0

### 170. 修改讓程式樣板的內容可以透過ctrl+c來複製需要的程式
- **Commit ID**: `99fb559fddbe8a108203d84e983e6f43d7d5d800`
- **作者**: Gaspard
- **日期**: 2018-06-20 10:21:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 171. 修正行動版Gird沒綁定新增鈕時會出現undefined按鈕
- **Commit ID**: `a5ae7f0339d5eba304848b2934b7afd94e8f39c2`
- **作者**: 治傑
- **日期**: 2018-06-20 10:15:16
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 172. 修正BPMApp若行動版表單設計師設置空欄位時畫面顯示${column}問題
- **Commit ID**: `b3dd1b9b9a30c89bc4f729d630cd5c1595f642eb`
- **作者**: yamiyeh10
- **日期**: 2018-06-19 18:33:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 173. 同A00-20180606005 修正刪除子部門後，會發生找不到帳號的錯誤訊息。
- **Commit ID**: `08ed2466925c5e6357c716e2aacd950a7b6bc01b`
- **作者**: 施廷緯
- **日期**: 2018-06-19 15:26:26
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/DeleteOrgUnitAction.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`

### 174. No number
- **Commit ID**: `296a5b60f47f59423066d0b0126ef890707be72c`
- **作者**: arielshih
- **日期**: 2018-06-19 13:47:47
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - ❌ **刪除**: `"3.Implementation/subproject/webapp/NaNa URL\351\200\243\347\265\220\346\270\205\345\226\256.doc"`
  - ❌ **刪除**: `"3.Implementation/subproject/webapp/NaNa\346\265\201\347\250\213\350\263\207\346\226\231\347\265\261\350\250\210v1.0.doc"`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Index.jsp`
  - ❌ **刪除**: `"3.Implementation/subproject/webapp/\345\267\262\347\237\245\345\225\217\351\241\214.doc"`
  - ❌ **刪除**: `"3.Implementation/subproject/webapp/\347\265\261\350\250\210\346\265\201\347\250\213\350\263\207\346\226\231\350\246\217\346\240\274\346\233\270.doc"`

### 175. 修正企業微信多語系僅支援繁中
- **Commit ID**: `6044d1d6245345cec626aa7e3e6497ec0a17fa89`
- **作者**: 治傑
- **日期**: 2018-06-19 10:29:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 176. 新增RESTful接口
- **Commit ID**: `1d2bcc5dd109890e339fcf64d1e02e9b954c0517`
- **作者**: ChinRong
- **日期**: 2018-06-15 17:50:49
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageAttachmentReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessReexecuteListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessRollBackListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessRollBackParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`

### 177. 修正議題
- **Commit ID**: `72e49f3c0ddc31a87b53edc8088ad5801e3fb2ee`
- **作者**: ChinRong
- **日期**: 2018-06-15 17:47:12
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 178. 舊發起流程RESTful接口新增回傳流程序號
- **Commit ID**: `df43692e3c24fd3fb961e73bfbdad4a463ff777a`
- **作者**: ChinRong
- **日期**: 2018-06-15 17:44:15
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`

### 179. 增加ISOFile透過DocServer轉呼叫流程主機進行轉檔
- **Commit ID**: `faf17df0d71c6a246e9eb479f653d42060823603`
- **作者**: waynechang
- **日期**: 2018-06-15 16:52:38
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/PDFHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/PDFHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IPDFHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/PDFHandlerImpl.java`

### 180. A00-20180427005 修正監控流程時,如果選擇未結案流程，才會出現撤銷流程選項
- **Commit ID**: `021fbb105d6713c2a40b6a47769cb563635da2e3`
- **作者**: jerry1218
- **日期**: 2018-06-15 16:14:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 181. C01-20180611002 增加轉檔PDF需要有書籤、交互參照(超連結)的功能
- **Commit ID**: `56796f8f6b625b23a0e82538f2ea7741e584c07e`
- **作者**: waynechang
- **日期**: 2018-06-15 11:07:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java`

### 182. A00-20180606005	新增備註及修改縮排問題
- **Commit ID**: `9b018c455bf3ffea4b68784f0faacd29102e089a`
- **作者**: 施廷緯
- **日期**: 2018-06-14 16:27:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`

### 183. A00-20180529002 修正ISO使用紀錄查詢當文件ID有單引號時會報錯
- **Commit ID**: `74acedde417010294a3eda0a78cc01e53f3b056c`
- **作者**: waynechang
- **日期**: 2018-06-14 13:43:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ReadingRecordListReaderImpl.java`

### 184. A00-20180606005 修正刪除子部門會發生找不到此帳號的錯誤訊息
- **Commit ID**: `a599c6980f0350cf27b6e5f756163a9ac7091f54`
- **作者**: 施廷緯
- **日期**: 2018-06-13 20:06:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/DeleteOrgUnitAction.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`

### 185. C01-20180514004 修正BPM重啟後使用企業微信登入App會取不到產品序號的問題
- **Commit ID**: `148df54796cd47f4a31d1a9654acb01fe8c2be86`
- **作者**: ChinRong
- **日期**: 2018-06-13 18:43:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 186. 修正BPM57版本,向前(後)加簽關卡時無法正常發單的問題。
- **Commit ID**: `e7aecb270aceea02881135954f87e798b0b45f37`
- **作者**: 顏伸儒
- **日期**: 2018-06-13 17:54:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 187. 新增簽核RESTful服務接口
- **Commit ID**: `9ed0989f46aa20647c59857fb82cf3cdf62d9280`
- **作者**: 治傑
- **日期**: 2018-06-13 17:44:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`

### 188. 修正User在ProcessID少於兩碼無法發起流程問題。
- **Commit ID**: `033c8a45af51fb48984129833e8c5f569dde2d5d`
- **作者**: 施廷緯
- **日期**: 2018-06-13 15:59:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 189. C01-20180613002 修正bpmTable.js-319行length拼錯
- **Commit ID**: `a705dce20423f945eb1a32cea910cbd136282b7f`
- **作者**: jerry1218
- **日期**: 2018-06-13 14:59:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 190. C01-20180613001 修正button元件資料選取器中step3如使用複製查詢標籤功能會與step2的index互相影響的異常
- **Commit ID**: `c63adf2e157e371d50c5ef6256c0d97908fce09a`
- **作者**: jerry1218
- **日期**: 2018-06-13 14:58:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 191. 新增簽核RESTful服務java bean
- **Commit ID**: `214dede14770fb754edb95c9d0cf8182cdaef795`
- **作者**: 治傑
- **日期**: 2018-06-13 14:45:19
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageAttachmentReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageFormDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageStdDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DispatchProcessParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DispatchProcessStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/PackageParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/PackageRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/PackageStdDataRes.java`

### 192. 修正刪除子部門會發生找不到此帳號的錯誤訊息。
- **Commit ID**: `285e3d4e34c692cc68e9594afaca53c20dc8ed94`
- **作者**: 施廷緯
- **日期**: 2018-06-13 10:43:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/DeleteOrgUnitAction.java`

### 193. 修正HR小助手 檢查部門失效且離職人員有主部門條件(微調)
- **Commit ID**: `83b88203212bc1fd1c20a5ded1eccb049caafc59`
- **作者**: 施廷緯
- **日期**: 2018-06-13 10:37:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java`

### 194. 修改表單設計器清單頁的查詢功能，查出來的結果可顯示為「響應式」或是「絕對位置」表單
- **Commit ID**: `4fcae637d4e044624e839bf4f0405f60e6b75c00`
- **作者**: Gaspard
- **日期**: 2018-06-13 10:01:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDefinitionSearchReader.java`

### 195. 將QueryDesigner.css也加上資源版本控制
- **Commit ID**: `3f8b2cfdf71ff52f5004731d5ffa62138d6dbe37`
- **作者**: Gaspard
- **日期**: 2018-06-12 16:28:17
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp`

### 196. 修改Web表單設計器，恢復拖曳元件時可自訂貼齊格線大小的功能
- **Commit ID**: `e99f805a0de3bb051bce36e6732cd2c85e7546d2`
- **作者**: Gaspard
- **日期**: 2018-06-12 10:46:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 197. 調整APP站台restful服務支持RWD表單與增加表單來源
- **Commit ID**: `cb5d934c443d323f22e8f5e9b91d826dc10ba5a4`
- **作者**: pinchi_lin
- **日期**: 2018-06-11 18:34:56
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmFormBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormDefinitionBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`

### 198. 調整APP中行事曆跳轉詳情應用功能
- **Commit ID**: `930df931e2bda8c17767ffbaf4306b484fe4ecaa`
- **作者**: pinchi_lin
- **日期**: 2018-06-11 18:08:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`

### 199. C01-20180608002 修正PerformWorkItemMain.jsp與easyflow整合時,沒有任何一筆會導致loadding不完的問題
- **Commit ID**: `3d9038be1f69e73e089bd27bfbc9e07e5d8d85fa`
- **作者**: jerry1218
- **日期**: 2018-06-11 16:49:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 200. A00-20180611001 取消流程序號圖片的cancelBubble
- **Commit ID**: `0f78c197dd8ddd5808b73d8bbcfb38b53080f685`
- **作者**: jerry1218
- **日期**: 2018-06-11 15:54:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`

### 201. C01-20180611001 修改個人化首頁-追蹤流程&我的追蹤2功能轉頁時呈現的條件與個人化首頁一致
- **Commit ID**: `4031d2a7ebf87d0ea56f35baba2195d7c9616195`
- **作者**: jerry1218
- **日期**: 2018-06-11 14:51:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 202. 修改表單Title元件僅在大螢幕呈現，且文字直接擺放於100%寬度的容器內
- **Commit ID**: `87a8d51d7d02e24ac00f36b461847d03d1d80f79`
- **作者**: Gaspard
- **日期**: 2018-06-08 17:10:06
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 203. 修正 : E10同步表單後無法儲存問題
- **Commit ID**: `b42f95f2c6705be794c08d030f50b4ddcddaaa29`
- **作者**: joseph
- **日期**: 2018-06-08 15:41:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 204. 修正 :Restful 取簽核歷程 ,因沒有時間而報錯問題
- **Commit ID**: `2971552ad2bc35ece3e0c292edffb19884dee028`
- **作者**: joseph
- **日期**: 2018-06-08 15:40:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`

### 205. 修正 : Restful發單 Grid資料因浮點樹型態無法轉型
- **Commit ID**: `909c6aefedeef775a9c4bcaaf46d5390af560655`
- **作者**: joseph
- **日期**: 2018-06-08 15:38:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 206. 加入表單腳本樣版功能
- **Commit ID**: `4b2f2620e8751300fe71b71c803dbb306e6450cc`
- **作者**: Gaspard
- **日期**: 2018-06-08 09:50:03
- **變更檔案數量**: 19
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/Map2JsonStringConversion.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormScriptCategory.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormScriptTemplate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IFormDefinitionDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_Oracle_1.sql`

### 207. 修正表單相對位置設計器未開啟鼎捷移動開關時拖拉仍會標記中間層
- **Commit ID**: `881ace780f27ec9d61579f1d1b5fb7862ef1c0ab`
- **作者**: ChinRong
- **日期**: 2018-06-07 15:17:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 208. 調整ESS表單預設呈現高度為1500
- **Commit ID**: `274a0fd02ef961e34ee612a15333a446534033d6`
- **作者**: Gaspard
- **日期**: 2018-06-07 11:52:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp`

### 209. 優化Web表單設計器，提升表單開啟效能，將表單輔助格線變更為3種模式，分別為5x5 10x10 20x20。
- **Commit ID**: `774c0014e08ce5d9a14abdb055f1aedc8754848c`
- **作者**: Gaspard
- **日期**: 2018-06-07 11:06:37
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppAbsoluteDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`

### 210. A00-20180402002 修正 新增單文件類別無法開啟下階類別
- **Commit ID**: `adf0f94fd5d335e0c70bed9baa4275b2e84b8404`
- **作者**: waynechang
- **日期**: 2018-06-06 17:50:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp`

### 211. 產生ESS表單的橫向Scrollbar
- **Commit ID**: `7cb47420554cd62cbdb53f151a91f4f66ee626da`
- **作者**: Gaspard
- **日期**: 2018-06-06 17:24:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp`

### 212. 修正5.7.1.1_DML_MSSQL_1.sql內含MSSQL2008無法使用的語法
- **Commit ID**: `b12abee2e35a614f2c31b64bb6bf09cebc68ed3e`
- **作者**: lorenchang
- **日期**: 2018-06-06 17:06:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_1.sql`

### 213. 增加BCL 轉檔需要的jar
- **Commit ID**: `122b236f8cffc3332fb6f7db1c56d0f8dcc5248f`
- **作者**: waynechang
- **日期**: 2018-06-06 17:01:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`

### 214. 加入ISO文件檔案管理模組製作索引文件時會用到的jar檔。
- **Commit ID**: `d68e65deed7e0a586c7cf9a5b845b3adf3d58ec9`
- **作者**: 顏伸儒
- **日期**: 2018-06-06 16:57:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`

### 215. 補上轉派資訊多語系
- **Commit ID**: `331590e2aa1c0d62615507a89d4aa4204e5528bc`
- **作者**: 治傑
- **日期**: 2018-06-06 15:49:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 216. 新增行動版處理的流程增加轉派資訊
- **Commit ID**: `d15e99c6720b03e9321fcacb7127c44834bafd2d`
- **作者**: 治傑
- **日期**: 2018-06-06 11:02:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`

### 217. 調整行動表單自動轉換機制，如果是用切分欄位的話在行動表單會用多欄位顯示
- **Commit ID**: `02fa016cc8488ad036d6543d93e8d86dc895b2d2`
- **作者**: ChinRong
- **日期**: 2018-06-06 09:16:50
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 218. 修改引用資源版號變成預設值0000000000，未來手動更新時直接變成當下的timestamp
- **Commit ID**: `09b54c5a562f0d227db1e34db0ec0699ece2f616`
- **作者**: Gaspard
- **日期**: 2018-06-05 17:27:20
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql`

### 219. Q00-20180530002 修改IE瀏覽器placeholder顏色預防使者誤解
- **Commit ID**: `b570f4963fce2fb1b1efac70a3b0cdac1955ebde`
- **作者**: jerry1218
- **日期**: 2018-06-05 14:56:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`

### 220. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `f737a1b76df6097756c19ca27467920c3a0d8d0a`
- **作者**: jd
- **日期**: 2018-06-05 10:51:28
- **變更檔案數量**: 0

### 221. 修正LOG記錄功能
- **Commit ID**: `8ca5189a6c33cfeacf689fa2f535aa2fb92ad118`
- **作者**: jd
- **日期**: 2018-06-05 10:50:49
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulClientDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulServiceDinWhale.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/AbstractPackageDinwhaleReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/AbstractPackageDinwhaleRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleBatchReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleChartRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleOpReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleOperationRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWebLog.properties`

### 222. A00-20180530032 增加ISO模組url通知信連結
- **Commit ID**: `741c8979956ae0c6172af2a45e5e9297295bfcc3`
- **作者**: waynechang
- **日期**: 2018-06-04 17:32:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/ExtraLinkMapping.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 223. 修改組織HRM小助手同步當部門失效時，忽略離職人員設定此單位為主部門的卡控。
- **Commit ID**: `da1990123bc5ccaa9e522494cc30967102f4d104`
- **作者**: 施廷緯
- **日期**: 2018-06-04 16:38:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java`

### 224. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `422b142ba8afadfe39a5e95a8a2864f82f293182`
- **作者**: jd
- **日期**: 2018-06-04 16:08:02
- **變更檔案數量**: 0

### 225. Q00-20180604001 修正主管頁面兩個錯誤 1.圖表的分類有本週,本月,本季,但邏輯為較舊版本的最近七天,最近一月,已修改邏輯與分類一致 2.直方圖計算基礎錯誤,正確的邏輯應為所選部門下面的逾時單據統計,依User分群(原為所選部門下面的人員的所有逾時單據統計)
- **Commit ID**: `1fff5ecf7c4b87432ff27cf4d41a14327cbaa0d4`
- **作者**: jerry1218
- **日期**: 2018-06-04 15:07:57
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 226. 修正行動表單預覽pdf檔案當附檔名為大寫時無法預覽的問題
- **Commit ID**: `2418dfeb7ed1e7e5c5faca462c6e4e482c6d83f2`
- **作者**: ChinRong
- **日期**: 2018-06-04 13:59:59
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 227. (1)表單的全域變數中，提供資源動態載入時可使用的變數 (2)優化表單全域變數產生方法
- **Commit ID**: `773eda3349dfa2199c5207fa0cd8cbdf3c0ccad8`
- **作者**: Gaspard
- **日期**: 2018-06-04 11:36:32
- **變更檔案數量**: 23
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/Constants.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalFormViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`

### 228. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `a56b8d1c04bd14af1b54ca8b6020529ef2634a87`
- **作者**: jd
- **日期**: 2018-06-04 11:09:16
- **變更檔案數量**: 0

### 229. A00-20180514001 修正 :  資料選取器開窗後，GRID欄位沒有按照設定所顯示
- **Commit ID**: `1d86863d631488ffa76dc3182c0867854733cd11`
- **作者**: joseph
- **日期**: 2018-06-04 10:30:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`

### 230. 增加引用JS、CSS資源的版本設定
- **Commit ID**: `9d089f89e00bd633f43a750346035e1b5db790da`
- **作者**: Gaspard
- **日期**: 2018-06-02 23:18:13
- **變更檔案數量**: 238
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CannotAccessWarnning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxCommonTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExtOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxFormTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxProcessTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/AttachmentExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/ButtonExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DialogExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DropdownExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormScriptExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/RadioButtonExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TextboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TimeExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Index.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PerformWorkFromMail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/WrapProcessMonitorInfo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppAbsoluteDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/InstallCertificate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocCategory/ManageDocCategoryMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/AccessRightChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/BatchUploadMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocClauseChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocLevelChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocServerChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentForQuery.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SingleDocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SnGenRuleChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetMultiLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/CompleteThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/LogoImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ShowSignImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracing.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmTaskManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/VipUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AdjustActivityOrder.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseOrganizationUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteReferProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkRegetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeReferProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/OnlySignImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraditionInvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ViewReassignHistory.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WebHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewAutoAgentActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewDecisionActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewParticipantActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/ProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/SubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/CreateProcessModule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/ManageProcessModuleMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/SetModuleAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/CompleteFormDataSearching.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ChooseDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteLeftEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessPicture.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/WebViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`

### 231. 增加資源載入版號的系統變數設計(加入SQL)
- **Commit ID**: `00426f93e7ddd9d7535b6f168c2410c1b71e710c`
- **作者**: Gaspard
- **日期**: 2018-06-01 23:21:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql`

### 232. 增加資源載入版號的系統變數設計
- **Commit ID**: `4efc218594a422f7d32020a1acc0a09fabcee3de`
- **作者**: Gaspard
- **日期**: 2018-06-01 23:18:23
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/Constants.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 233. A00-20180514003 修正 Button元件,設定為SQL註冊器開窗 ,在選取資料後,呼叫的方法無作用
- **Commit ID**: `1343fea80b95d34a6aa9d796b4fe85d72a4e83ec`
- **作者**: joseph
- **日期**: 2018-06-01 14:13:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/CustomDataChooserTemplate.txt`

### 234. A00-20180530019 調整多語系
- **Commit ID**: `2f2cd100c6e4289d736997e6b2878b342a6a39bf`
- **作者**: waynechang
- **日期**: 2018-06-01 11:17:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`

### 235. 新增鼎捷移動待辦清單轉派資訊
- **Commit ID**: `25e61d79e4cff11c55426ab4d0122a475bf8d7b7`
- **作者**: ChinRong
- **日期**: 2018-06-01 10:05:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 236. 調整APP中鼎捷移動行事曆新增的JSON格式
- **Commit ID**: `f0aabee39e82d206a56592198fbfce1e974a41b5`
- **作者**: pinchi_lin
- **日期**: 2018-05-31 14:18:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`

### 237. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `dd916ac125991647bfcf655d2b278e51cd5fc1de`
- **作者**: jd
- **日期**: 2018-05-31 10:28:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`

### 238. 調整LOG顯示機制
- **Commit ID**: `71c55e2f9e4ce2e255ca87f9390c1b538207454f`
- **作者**: jd
- **日期**: 2018-05-31 10:25:24
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/FormElement.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestFulHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulServiceDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`

### 239. 調整APP的鼎捷移動推播消息跳轉至詳情應用
- **Commit ID**: `88b45df65b5097d26ac8a2eadb1015cebf3df441`
- **作者**: pinchi_lin
- **日期**: 2018-05-30 18:34:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`

### 240. 調整App取得已轉派列表tool位置
- **Commit ID**: `78d41d49d44a567124b47dc683ff6e774dcc7c31`
- **作者**: yamiyeh10
- **日期**: 2018-05-30 18:19:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`

### 241. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `0f99896c5ddcfe48fdb0cab5b04aa2e62465bdd7`
- **作者**: 治傑
- **日期**: 2018-05-30 17:55:39
- **變更檔案數量**: 0

### 242. 新增鼎捷移動"轉派資訊"概要字段
- **Commit ID**: `aa35d3c26766a6155c3187df82f7fad32ba01824`
- **作者**: 治傑
- **日期**: 2018-05-30 17:55:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 243. 調整鼎捷移動行事曆功能,將WorkInfo塞到outer_schedule_id欄位中
- **Commit ID**: `abe50300af2f432e642df7af65d0b87b950e3fb1`
- **作者**: ChinRong
- **日期**: 2018-05-30 17:54:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`

### 244. 新增App用的已轉派流程tool與listreader
- **Commit ID**: `9dc6d27253834fb41371b511531d3c140c907eff`
- **作者**: yamiyeh10
- **日期**: 2018-05-30 17:40:06
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`

### 245. C01-***********  調整 發信及發通知 不會因為Qeueu報錯而Rollback
- **Commit ID**: `24dccddaf0fca807af2cb1696440eb0912a5f3cf`
- **作者**: joseph
- **日期**: 2018-05-30 15:47:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`

### 246. 修改loadding圖案
- **Commit ID**: `35252297baa52d17badc43f76b57b460f44d917f`
- **作者**: jerry1218
- **日期**: 2018-05-30 15:29:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/index_images/ajax-loader-v57.gif`

### 247. 移除copyfile及db內的@iso，改由獨立模組自行維護
- **Commit ID**: `4eb0321328bfc8038e39a0a4a983f094af020848`
- **作者**: lorenchang
- **日期**: 2018-05-30 15:17:02
- **變更檔案數量**: 43
- **檔案變更詳細**:
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOCancel001.form`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOInv001.form`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOMod001.form`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISONew001.form`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOPaperApply.form`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOPaperRecover.form`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOPaperWriteOff.form`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\344\275\234\345\273\242\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\346\226\260\345\242\236\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\347\264\231\346\234\254\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\350\252\277\351\226\261\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\350\256\212\346\233\264\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\347\264\231\346\234\254\346\255\270\351\202\204(\346\262\226\351\212\267)\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/create/IndexNaNaISODB_ORACLE9i.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/create/IndexNaNaISODB_SQLServer2005.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/create/InitISOData_ORACLE9i.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/create/InitISOData_SQLServer2005.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/create/InitNaNaISODB_Pure_ORACLE9i.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/create/InitNaNaISODB_Pure_SQLServer2005.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/ISO2.7.0_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso1.2.0_UpdateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso1.3.0_UpdateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso2.0.0_UpdateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso2.2.0_UpdateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso2.3.0_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.10.0_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.2.0_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.3.0_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.4.0_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.5.0_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.6.0_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.7.0_ISOReport_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.7.0_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.7.0_updateSQL_build-3.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.8.0_ISOReport_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso3.8.0_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso5.5.0.1_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso5.5.1.1_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso5.5.2.1_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso5.5.4.1_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso5.5.5.1_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso5.6.1.1_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/iso5.6.3.1_updateSQL.sql`

### 248. RWD表單開發優化
- **Commit ID**: `5cbe0abcf49a2c894b3d2d6d6605ebdc2b41defa`
- **作者**: Gaspard
- **日期**: 2018-05-30 14:46:54
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ListElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/RwdGridTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 249. 修正待簽核流程以及表單詳情資料相反的問題
- **Commit ID**: `ef59c841e4bd0903d25620369e68bbb07ddba59c`
- **作者**: ChinRong
- **日期**: 2018-05-30 11:40:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js`

### 250. 更新詳情維護多語系
- **Commit ID**: `717f14a4cb7748e4f6612a51926cb6fe869f3a0e`
- **作者**: ChinRong
- **日期**: 2018-05-30 11:20:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 251. 調整入口平台詳情ID維護
- **Commit ID**: `ee922774a876625aad620ab58f36285f40e51921`
- **作者**: ChinRong
- **日期**: 2018-05-30 11:05:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 252. Q00-20180330004 修正英文語系第二層manu太長時會有字串被截斷問題
- **Commit ID**: `e8c85c2096e6a2148de3541b7ce6fb90ffd88443`
- **作者**: jerry1218
- **日期**: 2018-05-30 10:46:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 253. 新增入口平台鼎捷移動詳情應用代號維護功能
- **Commit ID**: `b880b5ea0dae1b6224c5ab468137656bda0d91d4`
- **作者**: ChinRong
- **日期**: 2018-05-30 10:39:35
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5722.xls`

### 254. 修正RemoteObjectProvider.createWorkflowServerManager method中存在session中的WorkflowServerManagerDelegate session key錯誤問題
- **Commit ID**: `1334291d4e12a22aa94d978051c90e6aeee5ebde`
- **作者**: jerry1218
- **日期**: 2018-05-30 09:53:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`

