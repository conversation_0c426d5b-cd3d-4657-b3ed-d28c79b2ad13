# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.9.4_yankey
- **舊版本**: release_5.8.9.4
- **生成時間**: 2025-07-18 10:45:46
- **新增 Commit 數量**: 4

## 變更摘要

### lorenchang (1 commits)

- **2023-12-20 15:26:13**: [流程封存]修正若特製流程存在條件時，會出現ConditionDefinition PK 重覆導致無法封存的異常
  - 變更檔案: 1 個

### 林致帆 (1 commits)

- **2023-11-27 16:31:42**: [Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊
  - 變更檔案: 1 個

### 邱郁晏 (2 commits)

- **2023-11-28 17:29:23**: [附件擴充] 在線閱讀新增發布檔下載接口
  - 變更檔案: 4 個
- **2023-12-04 18:08:08**: [Web] V00-20231204001 修正工作轉派使用者，原處理者無法查閱已轉派的工作清單問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [流程封存]修正若特製流程存在條件時，會出現ConditionDefinition PK 重覆導致無法封存的異常
- **Commit ID**: `227dfc4224c94f5dc8f2db10f5ef14d68ff3c918`
- **作者**: lorenchang
- **日期**: 2023-12-20 15:26:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonImpl.java`

### 2. [Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊
- **Commit ID**: `34334373f9e501315f1886b7bdf798db1567569f`
- **作者**: 林致帆
- **日期**: 2023-11-27 16:31:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 3. [附件擴充] 在線閱讀新增發布檔下載接口
- **Commit ID**: `e49f53ed4ff5cec61d54b3f82dc348058bf29511`
- **作者**: 邱郁晏
- **日期**: 2023-11-28 17:29:23
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/BPMviewer.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html`

### 4. [Web] V00-20231204001 修正工作轉派使用者，原處理者無法查閱已轉派的工作清單問題
- **Commit ID**: `797cba4e79d6e1b63d3832481dc257dc929cf8c2`
- **作者**: 邱郁晏
- **日期**: 2023-12-04 18:08:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

