# Release Notes - BPM

## 版本資訊
- **新版本**: release_5.8.4.1
- **舊版本**: release_5.8.3.3
- **生成時間**: 2025-07-18 11:44:40
- **新增 Commit 數量**: 219

## 變更摘要

### lorenchang (5 commits)

- **2022-06-26 22:37:36**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.1
  - 變更檔案: 25 個
- **2020-08-21 13:50:58**: [內部]越南語系更新
  - 變更檔案: 3 個
- **2020-08-20 14:35:36**: [內部]越南語系更新
  - 變更檔案: 2 個
- **2020-08-19 10:18:01**: [內部]越南語系更新
  - 變更檔案: 4 個
- **2020-08-07 17:25:08**: [內部] 將5841多語系檔併入總檔
  - 變更檔案: 2 個

### way<PERSON><PERSON> (15 commits)

- **2020-08-24 12:13:09**: 更新5841 DB.patch
  - 變更檔案: 1 個
- **2020-08-21 10:21:53**: 更新5841 DB.patch
  - 變更檔案: 1 個
- **2020-08-20 10:54:20**: [流程設計師]C01-20200813005 修正V58流程設計師刪除流程時，未刪除FormType屬性，導致無法刪除表單定義
  - 變更檔案: 1 個
- **2020-08-14 18:21:04**: 更新5841 DB.patch
  - 變更檔案: 1 個
- **2020-08-14 18:17:14**: [ISO]Q00-20200814001 修正舊版ISO表單版更至5833時，若文件編號是由系統產生時，文件編號未回寫到formInstace
  - 變更檔案: 1 個
- **2020-08-14 13:53:35**: [Web]A00-20200813001 修正ISO表單附件設為「ISO表單附件轉PDF」時，開啟附件上傳畫面後會提示錯誤訊息
  - 變更檔案: 1 個
- **2020-08-13 18:05:37**: [E10]Q00-20200812002 E10流程 執行到「回寫E10服務任務」時，須將E10回傳錯誤訊息回寫至BPM流程變數並設定流程讓表單可以回到「人員處理」的關卡，並將訊息顯示在簽核畫面上。[補]
  - 變更檔案: 1 個
- **2020-08-13 13:49:37**: [E10]Q00-20200812002 E10流程 執行到「回寫E10服務任務」時，須將E10回傳錯誤訊息回寫至BPM流程變數並設定流程讓表單可以回到「人員處理」的關卡，並將訊息顯示在簽核畫面上。[補]
  - 變更檔案: 1 個
- **2020-08-12 17:34:30**: [E10]Q00-20200812002 E10流程 執行到「回寫E10服務任務」時，須將E10回傳錯誤訊息回寫至BPM流程變數並設定流程讓表單可以回到「人員處理」的關卡，並將訊息顯示在簽核畫面上。
  - 變更檔案: 3 個
- **2020-08-12 17:22:09**: [流程引擎]Q00-20200812001 當Router的線有設定條件，且結束的關卡為「EndEvent」關卡時；若關卡線的條件滿足時，會無法結案
  - 變更檔案: 1 個
- **2020-08-11 17:02:05**: [Web]Q00-20200811005 修正報表設計器檢查程式代號重複時，仍可以新增
  - 變更檔案: 1 個
- **2020-08-10 14:57:04**: 更新5841BPMRsrcBundle.xlsx 及 DB.patch
  - 變更檔案: 2 個
- **2020-08-06 14:35:48**: [Web]Q00-20200806002 產品回收NTKO專案
  - 變更檔案: 6 個
- **2020-07-24 12:04:51**: [Web]S00- 增加報表設計器功能
  - 變更檔案: 21 個
- **2020-06-18 12:01:30**: [流程引擎]A00-20200609001 調整DB欄位長度(WorkItem.attachmentHits)
  - 變更檔案: 4 個

### yanann_chen (15 commits)

- **2020-08-21 13:42:57**: [流程引擎]A00-20200819002 修正: 流程負責人監控流程匯出Excel時發生"超過了可開啟的游標之數目上限"錯誤
  - 變更檔案: 1 個
- **2020-08-21 11:25:59**: [Web]A00-20200819003 修正: 樹狀開窗樹狀圖高度不夠，導致部門無法完整呈現
  - 變更檔案: 1 個
- **2020-08-13 10:24:23**: [Web]A00-20200803003 修正: ajax_CommonAccessor.login報錯
  - 變更檔案: 1 個
- **2020-08-07 17:25:42**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2020-08-07 17:25:18**: [流程引擎]C01-20200708004 修正: 通知信件內容沒有正確斷行[補]
  - 變更檔案: 2 個
- **2020-08-07 14:16:19**: [流程引擎]C01-20200708004 修正: 通知信件內容沒有正確斷行
  - 變更檔案: 3 個
- **2020-08-07 09:52:31**: [Web]A00-20200803001 修正: 日期元件缺少越南文多語系造成顯示異常
  - 變更檔案: 1 個
- **2020-08-05 11:17:39**: [Web]C01-20200720003 修正: 自動簽核關卡簡易流程圖順序顯示異常
  - 變更檔案: 1 個
- **2020-08-05 10:44:55**: [Web]C01-20200701002 修正: 迴圈行流程無法於追蹤流程中執行取回重辦
  - 變更檔案: 1 個
- **2020-08-05 10:14:34**: [Web]A00-20200727003 修正: 取回畫面排版異常
  - 變更檔案: 1 個
- **2020-08-05 10:05:56**: [Web]C01-20200728001 修正: 待辦事項搜尋條件無法清空流程名稱
  - 變更檔案: 1 個
- **2020-07-31 14:19:50**: [Web]C01-20200729003 修正: Chrome遮罩功能異常問題
  - 變更檔案: 1 個
- **2020-07-30 17:33:17**: [流程引擎]A00-20200605001 修正問題: XPDL流程中核決層級未依設定啟動自動簽核[補]
  - 變更檔案: 1 個
- **2020-07-30 17:25:14**: [流程引擎]A00-20200605001 修正問題: XPDL流程中核決層級未依設定啟動自動簽核
  - 變更檔案: 1 個
- **2020-07-30 15:56:56**: [流程引擎]Q00-20200730002 修正問題: 被代理工作的取回重辦URL，workItemOID為null
  - 變更檔案: 1 個

### cherryliao (48 commits)

- **2020-08-20 18:01:19**: [BPM APP]Q00-20200820004 修正行動端表單在Grid有繫結選項元件且含有額外輸入框時無法編輯問題
  - 變更檔案: 2 個
- **2020-08-20 14:44:14**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2020-08-20 14:36:04**: [BPM APP]Q00-20200819004 調整在移動端中間層畫面能夠正常顯示Grid繫結選項元件額外輸入框的資料
  - 變更檔案: 1 個
- **2020-08-18 13:36:11**: [BPM APP]Q00-20200818001 修正移動端簽核意見帶入近期簽核記錄值不會寫入流程的問題
  - 變更檔案: 1 個
- **2020-08-13 19:19:38**: [BPM APP]Q00-20200813001 調整IMG已轉派流程，原中間層與動態渲染表單沒有顯示表單詳情按鈕的問題
  - 變更檔案: 3 個
- **2020-08-13 13:06:20**: [BPM APP]Q00-20200812003 修正從IMG推播消息或行事曆進動態渲染表單時，在工作已被處理依舊會顯示表單內容的問題
  - 變更檔案: 1 個
- **2020-08-11 12:21:59**: [BPM APP]S00-20200525005 調整行動端表單選項元件與grid綁定功能可支援額外輸入框選項
  - 變更檔案: 3 個
- **2020-08-07 13:54:12**: [BPM APP]調整IMG推播消息可連到動態渲染表單應用[補]
  - 變更檔案: 5 個
- **2020-08-06 11:59:37**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-08-06 11:54:55**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-08-06 11:20:42**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-08-04 12:03:19**: [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
  - 變更檔案: 1 個
- **2020-07-31 19:17:37**: [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
  - 變更檔案: 4 個
- **2020-07-24 20:28:52**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-07-23 13:34:16**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-07-23 10:23:00**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-07-16 17:58:10**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 6 個
- **2020-07-14 16:15:26**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 4 個
- **2020-07-14 15:50:26**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 4 個
- **2020-07-06 13:49:40**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 2 個
- **2020-07-01 19:50:39**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-07-01 11:18:51**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 6 個
- **2020-06-29 16:01:38**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 2 個
- **2020-06-24 17:41:42**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 5 個
- **2020-06-23 15:19:36**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 2 個
- **2020-06-19 18:41:36**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 1 個
- **2020-06-19 15:45:33**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 2 個
- **2020-06-19 09:44:21**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 2 個
- **2020-06-18 10:58:19**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 1 個
- **2020-06-18 08:53:33**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-06-18 08:46:00**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 2 個
- **2020-06-16 15:06:11**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 5 個
- **2020-06-15 19:01:22**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能
  - 變更檔案: 3 個
- **2020-06-11 15:40:55**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-06-11 15:34:50**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-06-10 15:00:48**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 2 個
- **2020-06-05 17:40:04**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-06-03 17:28:07**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-06-03 15:12:36**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 10 個
- **2020-05-26 15:27:50**: [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
  - 變更檔案: 1 個
- **2020-05-26 14:04:02**: [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
  - 變更檔案: 1 個
- **2020-05-25 18:42:56**: [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
  - 變更檔案: 5 個
- **2020-05-14 18:22:04**: [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能
  - 變更檔案: 2 個
- **2020-05-14 11:28:58**: [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
  - 變更檔案: 2 個
- **2020-05-14 11:04:38**: [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
  - 變更檔案: 2 個
- **2020-08-07 10:40:07**: [BPM APP]C01-20200804003 修正移動端產品開窗人員列表當人員有多部門時只會顯示單一部門但非主部門的問題
  - 變更檔案: 1 個
- **2020-08-06 14:25:47**: [BPM APP]C01-20200730002修正IMG在追蹤流程取得中間層顯示資訊時發生錯誤問題
  - 變更檔案: 1 個
- **2020-07-30 17:38:27**: [BPM APP]C01-20200724004修正行動端在閒置超過一定時間後操作畫面上功能時顯示的錯誤訊息文字說明
  - 變更檔案: 6 個

### yamiyeh10 (63 commits)

- **2020-08-20 15:05:36**: [BPM APP]Q00-20200820003 修正行動端的發起表單畫面當上傳的附件過多時會發生無法滑動問題
  - 變更檔案: 1 個
- **2020-08-20 11:43:29**: [BPM APP]Q00-20200819003 調整在移動端中間層畫面能夠正常顯示選項元件額外輸入框的資料
  - 變更檔案: 1 個
- **2020-08-20 09:40:23**: [BPM APP]Q00-20200814002 修正動態渲染表單在繼續簽核時無法正常開啟下一筆流程問題
  - 變更檔案: 5 個
- **2020-08-17 17:31:43**: [BPM APP]Q00-20200817001 修正動態渲染表單在無整合鼎捷移動情況下移除表單傳送至IMG功能
  - 變更檔案: 1 個
- **2020-08-13 15:19:25**: [BPM APP]Q00-20200813004 調整動態渲染表單在傳送表單定義時的相關多語系詞彙
  - 變更檔案: 1 個
- **2020-08-13 14:41:40**: [BPM APP]Q00-20200813002 修正動態渲染表單中Grid和附件元件當流程設定隱藏時沒有效果問題
  - 變更檔案: 1 個
- **2020-08-13 10:18:44**: [BPM APP]Q00-20200812004 修正動態渲染表單在智能快簽的待辦事項未顯示擱置按鈕問題
  - 變更檔案: 1 個
- **2020-08-12 16:20:55**: [BPM APP]Q00-20200811004 修正行動端在發起流程沒掛載表單時錯誤訊息頁面無顯示錯誤圖片問題
  - 變更檔案: 1 個
- **2020-08-12 15:02:20**: [BPM APP]Q00-20200811003 修正行動端的退回重辦畫面在選擇退回關卡時按鈕會位移跑版問題
  - 變更檔案: 1 個
- **2020-08-11 11:49:49**: [BPM APP]S00-20200525003 新增行動端支援T100表單原始值在欄位下方顯示功能
  - 變更檔案: 17 個
- **2020-08-07 19:12:26**: [BPMAPP]Q00-20200331005 調整行動端表單選項元件支援可額外產生輸入框功能
  - 變更檔案: 11 個
- **2020-08-04 15:57:21**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-07-31 19:07:46**: [RESTFul]調整同意派送RESTful服務接口
  - 變更檔案: 2 個
- **2020-07-31 13:36:13**: [RESTFul]調整取追蹤清單RESTful服務接口
  - 變更檔案: 2 個
- **2020-07-30 12:09:19**: [RESTFul]調整取待辦清單RESTful服務的回傳值
  - 變更檔案: 2 個
- **2020-07-28 15:36:05**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 7 個
- **2020-07-28 11:01:58**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 2 個
- **2020-07-27 09:34:02**: [BPM APP]調整IMG行事曆可連到動態渲染表單應用[補]
  - 變更檔案: 2 個
- **2020-07-24 15:49:18**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-07-24 14:44:27**: [BPM APP]調整IMG行事曆可連到動態渲染表單應用
  - 變更檔案: 5 個
- **2020-07-23 10:45:13**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 4 個
- **2020-07-23 10:35:58**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 2 個
- **2020-07-17 18:23:42**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 4 個
- **2020-07-17 18:21:51**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 3 個
- **2020-07-17 18:19:45**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 3 個
- **2020-07-16 14:47:07**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-07-16 14:45:32**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-07-15 18:16:21**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 4 個
- **2020-07-15 18:11:13**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 2 個
- **2020-07-14 16:24:17**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 7 個
- **2020-07-10 19:02:19**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-07-10 19:01:22**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 8 個
- **2020-07-03 17:31:46**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 5 個
- **2020-07-03 09:39:03**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 3 個
- **2020-07-02 11:19:37**: [BPM APP]新增LINE官方帳號推送消息功能[補]
  - 變更檔案: 1 個
- **2020-07-01 09:44:48**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 2 個
- **2020-06-30 15:44:26**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 6 個
- **2020-06-24 18:29:47**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 2 個
- **2020-06-23 16:40:13**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能
  - 變更檔案: 1 個
- **2020-06-23 15:34:11**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-06-18 15:13:12**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-06-18 11:58:25**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 1 個
- **2020-06-18 11:33:20**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 6 個
- **2020-06-16 17:40:58**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 3 個
- **2020-06-15 16:30:01**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能
  - 變更檔案: 1 個
- **2020-06-12 12:31:36**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 16 個
- **2020-06-10 17:20:10**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 3 個
- **2020-06-04 15:53:17**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 3 個
- **2020-06-03 18:14:13**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 5 個
- **2020-06-03 12:01:38**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 10 個
- **2020-06-02 13:48:54**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能
  - 變更檔案: 10 個
- **2020-05-29 10:33:33**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 19 個
- **2020-05-28 15:20:32**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-05-28 14:31:20**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能
  - 變更檔案: 4 個
- **2020-05-26 14:57:06**: [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
  - 變更檔案: 1 個
- **2020-05-26 12:28:57**: [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
  - 變更檔案: 2 個
- **2020-05-26 11:54:13**: [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
  - 變更檔案: 1 個
- **2020-05-26 11:52:18**: [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
  - 變更檔案: 3 個
- **2020-05-15 16:01:57**: [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
  - 變更檔案: 1 個
- **2020-05-15 15:08:00**: [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
  - 變更檔案: 3 個
- **2020-05-14 18:31:54**: [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
  - 變更檔案: 3 個
- **2020-05-14 10:18:26**: [BPM APP]Line-OA加入取消與恢復訂閱消息機制
  - 變更檔案: 1 個
- **2020-05-14 10:07:48**: [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能
  - 變更檔案: 1 個

### pinchi_lin (21 commits)

- **2020-08-20 14:05:09**: [BPM APP]Q00-20200820002 修正LINE在點封鎖時會拋出錯誤的問題
  - 變更檔案: 1 個
- **2020-08-20 10:13:13**: [BPM APP]Q00-20200817007 修正企業微信同步組織排程會有無法插入NULL值到資料行的問題
  - 變更檔案: 3 個
- **2020-08-18 19:17:36**: [BPM APP]Q00-20200817003 修正IMG追蹤流程時取活動關卡錯誤導致中間層表單資料顯示異常的問題
  - 變更檔案: 4 個
- **2020-08-17 18:23:14**: [BPM APP]Q00-20200817004 修正行動端簽核與追蹤已處理操作提醒或取回時進階按鈕仍可點擊的問題
  - 變更檔案: 16 個
- **2020-08-17 16:06:04**: [BPM APP]Q00-20200817002 修正IMG轉由他人處理功能中選取人員的篩選條件移除電子郵件
  - 變更檔案: 1 個
- **2020-08-12 15:57:20**: [BPM APP]Q00-20200811002 修正行動端發起或簽核有多部門選擇時進階按鈕仍可點擊問題
  - 變更檔案: 5 個
- **2020-08-07 17:27:54**: [BPM APP]S00-20200525001調整BPMAPP表單grid元件支持必填樣式
  - 變更檔案: 4 個
- **2020-08-07 17:22:14**: [BPM APP]Q00-20190924001調整IMG在詳情表單點按鈕返回(非左上箭號)會保留篩選條件並刷新功能
  - 變更檔案: 2 個
- **2020-08-05 14:00:22**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-07-31 17:17:44**: [RESTFul]調整發起、撤銷流程RESTful服務接口[補]
  - 變更檔案: 1 個
- **2020-07-31 15:00:47**: [RESTFul]調整發起、撤銷流程RESTful服務接口
  - 變更檔案: 2 個
- **2020-07-28 16:03:33**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 3 個
- **2020-07-27 19:16:26**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-07-23 11:25:43**: [BPM APP]調整IMG推播消息可連到動態渲染表單應用
  - 變更檔案: 10 個
- **2020-07-23 10:00:03**: [BPM APP]調整IMG列表撈取動態生成表單應用ID[補]
  - 變更檔案: 4 個
- **2020-07-21 16:56:32**: [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
  - 變更檔案: 7 個
- **2020-07-16 17:36:08**: [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
  - 變更檔案: 3 個
- **2020-07-15 19:26:09**: [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
  - 變更檔案: 1 個
- **2020-07-09 10:13:15**: [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能
  - 變更檔案: 2 個
- **2020-08-04 14:53:43**: [BPM APP]C01-20200728003修正IMG中間層因附件有設定查看權限導致無法顯示問題[補]
  - 變更檔案: 1 個
- **2020-08-04 14:50:19**: [BPM APP]C01-20200728003修正IMG中間層因附件有設定查看權限導致無法顯示問題
  - 變更檔案: 2 個

### 詩雅 (24 commits)

- **2020-08-19 20:05:28**: [BPM APP]S00-20200525001調整BPMAPP表單grid元件支持必填樣式[補]
  - 變更檔案: 1 個
- **2020-07-23 17:54:29**: [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
  - 變更檔案: 6 個
- **2020-07-22 10:55:34**: [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
  - 變更檔案: 5 個
- **2020-07-15 19:47:37**: [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
  - 變更檔案: 7 個
- **2020-07-10 17:01:29**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 2 個
- **2020-07-07 17:26:54**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 3 個
- **2020-07-01 15:49:36**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 2 個
- **2020-06-29 14:53:53**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 2 個
- **2020-06-24 12:12:13**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 1 個
- **2020-06-23 17:43:58**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 2 個
- **2020-06-19 16:44:04**: [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
  - 變更檔案: 2 個
- **2020-06-18 18:58:11**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-06-16 16:31:41**: [BPM APP]調整IMG列表撈取動態生成表單應用ID
  - 變更檔案: 3 個
- **2020-06-15 13:55:26**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-06-10 18:19:07**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 2 個
- **2020-06-10 13:41:36**: [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
  - 變更檔案: 1 個
- **2020-06-09 15:33:48**: [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
  - 變更檔案: 2 個
- **2020-06-05 15:42:46**: [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
  - 變更檔案: 1 個
- **2020-06-04 15:21:06**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 3 個
- **2020-06-04 14:35:33**: [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
  - 變更檔案: 5 個
- **2020-05-27 17:52:44**: [BPM APP]新增LINE圖文選單BPM首頁與聯絡人圖示
  - 變更檔案: 2 個
- **2020-05-22 18:04:38**: [BPM APP]LINE圖文選單新增BPM首頁連結
  - 變更檔案: 1 個
- **2020-05-18 15:07:03**: [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
  - 變更檔案: 1 個
- **2020-05-18 09:31:59**: [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
  - 變更檔案: 3 個

### 林致帆 (10 commits)

- **2020-08-19 17:43:16**: [內部]Q00-20200819006 E10同步表單的log訊息調整
  - 變更檔案: 1 個
- **2020-07-13 18:37:18**: [流程引擎]A00-20200709001 修正呼叫removeAbsencerecord方法，Absencerecord資料表資料無法移除
  - 變更檔案: 2 個
- **2020-05-07 08:32:28**: 流程模組維護會使用到主要以下程式: ProcessModuleDef.java ProcessModuleDefMgr.java ProcessModuleDefBean.java ProcessModuleDefDelegate.java ProcessModuleDefLocal.java ProcessModuleViewer.java ProcessModuleAction.java ProcessModuleDefinitionDTO.java ProcessModuleAccessRightVo.java 1.因不維護，故移除，其他程式有應用到上述程式內容，確認後不需使用的已刪除 2.左側功能列產生的流程模組按鈕，也在jsp中移除產生該按鈕的method 3.SQL調整，出貨ProcessModuleAccessRight,ProcessModuleContainer,ProcessModuleDefinition這三個資料表已不使用，故直接移除create指令
  - 變更檔案: 25 個
- **2020-08-07 11:56:47**: [Web]A00-202006190015調整，若有兩個人(一個在職,一個離職)共用一個Ldap帳號，在職人員用Ldap登入會顯示重覆的Ldap帳號
  - 變更檔案: 1 個
- **2020-08-05 09:52:56**: [Web]C01-20200729002調整使用者監控流程的流程清單頁"執行中的活動"欄位顯示內容，與系統管理員的監控流程的流程清單頁一致
  - 變更檔案: 1 個
- **2020-08-04 10:46:08**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2020-08-04 10:45:37**: [Web]C01-20200727008修正若表單定義的xml中複合元件沒有dialogType資料，導致版更後開啟有關聯的流程畫面出現異常
  - 變更檔案: 1 個
- **2020-07-30 17:24:09**: [Web]A00-20200714001 修正查詢樣版的搜尋條件為開始包含or結束包含，查詢結果為相反
  - 變更檔案: 1 個
- **2020-07-30 15:50:43**: [Web]A00-20200729001 修正從BPM的外部Url連結"查詢流程實例內的單一表單資料",若表單有附件，點擊後無法下載
  - 變更檔案: 1 個
- **2020-07-30 15:02:31**: [Web]A00-20200622004修正表單欄位有設定顯示小數點後幾位或是顯示千分位，在流程設計師的關卡中欄位驗證是onblur或是both的設定下，造成欄位顯示異常
  - 變更檔案: 1 個

### 王鵬程 (7 commits)

- **2020-08-19 17:21:20**: [Web]A00-20200803004 修正當processinstance.viewprocess.type為ALL時，流程圖太寬會無法完整顯示
  - 變更檔案: 3 個
- **2020-08-14 14:19:14**: [Web]A00-20200810001 修正表單中有必填欄位，當上傳附件後必填欄位的星號(RWD表單)和紅色外框(絕對表單)會消失
  - 變更檔案: 1 個
- **2020-08-03 15:37:58**: [Web]A00-20200728001 修改系統設定的第四個選項(trace.list.orderby)的描述
  - 變更檔案: 2 個
- **2020-07-02 16:02:12**: [ESS]A00-20200312003 修正ESS員工培訓規劃課程查詢(ESSQ92) insert語法中OID的值有誤
  - 變更檔案: 1 個
- **2020-06-20 11:19:57**: [T100]Q01-20200620001 修正執行T100_SyncTables_Oracle.sql，資料庫出現錯誤
  - 變更檔案: 1 個
- **2020-08-05 18:48:14**: [Web]A00-20200729002 修正IOS進入掛雙表單的流程中，點切換表單按紐後彈出的Dialog無法操作和關閉
  - 變更檔案: 1 個
- **2020-08-04 17:37:34**: [Web]A00-20200720001 修正在IE下從待辦事項中進入ESS的流程，點擊更多按鈕展開後的選單會被表單遮住
  - 變更檔案: 1 個

### jerry1218 (9 commits)

- **2020-08-17 13:34:21**: [帳號啟用](1).修改帳號啟用功能-使用者名稱問題(2).修改UserCacheSingletonMap在使用者沒有掛部門的情境下會無法存入cache
  - 變更檔案: 3 個
- **2020-08-12 15:48:44**: [帳號啟用]邏輯變更 1.增加SystemVariable設定初始值 2.組織同步新增使用者值接依初始值給予預設帳號狀態
  - 變更檔案: 18 個
- **2020-08-11 12:01:11**: [帳號啟用]修正搜尋後如果只有一筆的情況下,加上全選會沒辦法按操作選項的問題
  - 變更檔案: 1 個
- **2020-05-21 10:27:37**: [merge後更改]修改UpdateSQL符合S00-20200508002
  - 變更檔案: 2 個
- **2020-05-21 10:10:04**: 修正merge導致的錯誤
  - 變更檔案: 3 個
- **2020-04-29 11:19:51**: [帳號啟用]程式調整
  - 變更檔案: 7 個
- **2020-04-28 11:32:41**: [帳號啟用]日起選擇&模糊查詢部分調整
  - 變更檔案: 2 個
- **2020-04-27 16:29:26**: [帳號啟用]多語系補充
  - 變更檔案: 1 個
- **2020-04-27 16:28:27**: 新增帳號啟用功能
  - 變更檔案: 71 個

### walter_wu (1 commits)

- **2020-08-11 16:42:30**: [RESTFul]調整發起、撤銷流程RESTful服務接口[補]
  - 變更檔案: 1 個

### wusnnn (1 commits)

- **2020-05-12 09:31:47**: [流程引擎]S00-20200508002簡化模組(ModuleDefinition)及程式(ProgramDefinition)的多語系格式方便維護(XML轉JSON)
  - 變更檔案: 12 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.1
- **Commit ID**: `c3aa98f73e6708ec54dc8ade1e096cb8857c1f17`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:37:36
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. 更新5841 DB.patch
- **Commit ID**: `1197db82840e137eed9ef0116238052e9ef1f554`
- **作者**: waynechang
- **日期**: 2020-08-24 12:13:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 3. [內部]越南語系更新
- **Commit ID**: `fb3151c14b6aa126e51936734e9b9b1fa6556288`
- **作者**: lorenchang
- **日期**: 2020-08-21 13:50:58
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`

### 4. [流程引擎]A00-20200819002 修正: 流程負責人監控流程匯出Excel時發生"超過了可開啟的游標之數目上限"錯誤
- **Commit ID**: `4458b3c30e7a5d3a35c114192a85a6ba77457b0e`
- **作者**: yanann_chen
- **日期**: 2020-08-21 13:42:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 5. [Web]A00-20200819003 修正: 樹狀開窗樹狀圖高度不夠，導致部門無法完整呈現
- **Commit ID**: `33ff11d52c73c5a98272a9e0779393e408a8af00`
- **作者**: yanann_chen
- **日期**: 2020-08-21 11:25:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`

### 6. 更新5841 DB.patch
- **Commit ID**: `e7d57d8d1b8027058d248ee1f54821731040d6cf`
- **作者**: waynechang
- **日期**: 2020-08-21 10:21:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 7. [BPM APP]Q00-20200820004 修正行動端表單在Grid有繫結選項元件且含有額外輸入框時無法編輯問題
- **Commit ID**: `c01fefbb6413f1299df18b585cf358698ffa44c8`
- **作者**: cherryliao
- **日期**: 2020-08-20 18:01:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 8. [BPM APP]Q00-20200820003 修正行動端的發起表單畫面當上傳的附件過多時會發生無法滑動問題
- **Commit ID**: `8969d3d76425b4bf859afd509f58264671159da2`
- **作者**: yamiyeh10
- **日期**: 2020-08-20 15:05:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 9. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `9f104dd231fd47904742bc409b6f8a3ea4b6deac`
- **作者**: cherryliao
- **日期**: 2020-08-20 14:44:14
- **變更檔案數量**: 0

### 10. [BPM APP]Q00-20200819004 調整在移動端中間層畫面能夠正常顯示Grid繫結選項元件額外輸入框的資料
- **Commit ID**: `5923aa33cbb60555a9b6db0c6113a8c4ccd62789`
- **作者**: cherryliao
- **日期**: 2020-08-20 14:36:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`

### 11. [內部]越南語系更新
- **Commit ID**: `650b6c7def90a60590640f62c3ed508025261f73`
- **作者**: lorenchang
- **日期**: 2020-08-20 14:35:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`

### 12. [BPM APP]Q00-20200820002 修正LINE在點封鎖時會拋出錯誤的問題
- **Commit ID**: `d88e72c6a6d5ceca2c859051b115b18aa4101989`
- **作者**: pinchi_lin
- **日期**: 2020-08-20 14:05:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 13. [BPM APP]Q00-20200819003 調整在移動端中間層畫面能夠正常顯示選項元件額外輸入框的資料
- **Commit ID**: `218e0bc5b1770b4bf1f9460a7e2739ab35479c4b`
- **作者**: yamiyeh10
- **日期**: 2020-08-20 11:43:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`

### 14. [流程設計師]C01-20200813005 修正V58流程設計師刪除流程時，未刪除FormType屬性，導致無法刪除表單定義
- **Commit ID**: `773b11733a4b8f1733c25f7b69168cf43a9e9b73`
- **作者**: waynechang
- **日期**: 2020-08-20 10:54:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/RelevantDataDefinition.java`

### 15. [BPM APP]Q00-20200817007 修正企業微信同步組織排程會有無法插入NULL值到資料行的問題
- **Commit ID**: `45fff19be55bd21499e274c070f20d6e75472990`
- **作者**: pinchi_lin
- **日期**: 2020-08-20 10:13:13
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`

### 16. [BPM APP]Q00-20200814002 修正動態渲染表單在繼續簽核時無法正常開啟下一筆流程問題
- **Commit ID**: `ae5a7d5527da8aacfcf282c1596bd7fcb70f6a55`
- **作者**: yamiyeh10
- **日期**: 2020-08-20 09:40:23
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 17. [BPM APP]S00-20200525001調整BPMAPP表單grid元件支持必填樣式[補]
- **Commit ID**: `145841c922f4f2fa01bb7e0f907f1c9215e102d2`
- **作者**: 詩雅
- **日期**: 2020-08-19 20:05:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js`

### 18. [內部]Q00-20200819006 E10同步表單的log訊息調整
- **Commit ID**: `95350b04237b4f46c4355da375e8ad35adfe44ee`
- **作者**: 林致帆
- **日期**: 2020-08-19 17:43:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 19. [Web]A00-20200803004 修正當processinstance.viewprocess.type為ALL時，流程圖太寬會無法完整顯示
- **Commit ID**: `4b3ded1c8394d81a8236794ce30f261ca21aae4e`
- **作者**: 王鵬程
- **日期**: 2020-08-19 17:21:20
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 20. [內部]越南語系更新
- **Commit ID**: `43da20f7f2c401231fe4d2044c85775d96a8df6e`
- **作者**: lorenchang
- **日期**: 2020-08-19 10:18:01
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_vi_VN.properties`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 21. [BPM APP]Q00-20200817003 修正IMG追蹤流程時取活動關卡錯誤導致中間層表單資料顯示異常的問題
- **Commit ID**: `46e708619ccca760c299b3f3a66881abb0ea1b3d`
- **作者**: pinchi_lin
- **日期**: 2020-08-18 19:17:36
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`

### 22. [BPM APP]Q00-20200818001 修正移動端簽核意見帶入近期簽核記錄值不會寫入流程的問題
- **Commit ID**: `5f93bc7a94a19fab708942698c4fbfd85d113927`
- **作者**: cherryliao
- **日期**: 2020-08-18 13:36:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 23. [BPM APP]Q00-20200817004 修正行動端簽核與追蹤已處理操作提醒或取回時進階按鈕仍可點擊的問題
- **Commit ID**: `ab6791ba059cee44fc4b3d7b6c4ccd6702bca240`
- **作者**: pinchi_lin
- **日期**: 2020-08-17 18:23:14
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 24. [BPM APP]Q00-20200817001 修正動態渲染表單在無整合鼎捷移動情況下移除表單傳送至IMG功能
- **Commit ID**: `e0ecf694b8c478dc0c9a70aef567dfd6a7f2fc1c`
- **作者**: yamiyeh10
- **日期**: 2020-08-17 17:31:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 25. [BPM APP]Q00-20200817002 修正IMG轉由他人處理功能中選取人員的篩選條件移除電子郵件
- **Commit ID**: `3f19a107c4d8ce60ba423c958974f4a18a7ee6c4`
- **作者**: pinchi_lin
- **日期**: 2020-08-17 16:06:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 26. [帳號啟用](1).修改帳號啟用功能-使用者名稱問題(2).修改UserCacheSingletonMap在使用者沒有掛部門的情境下會無法存入cache
- **Commit ID**: `683ea464d6a02962a42321ef7e34679e35a69150`
- **作者**: jerry1218
- **日期**: 2020-08-17 13:34:21
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserManageListReader.java`

### 27. 更新5841 DB.patch
- **Commit ID**: `f054f5745338ddbfa3a12046d03bf195d9d345c4`
- **作者**: waynechang
- **日期**: 2020-08-14 18:21:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 28. [ISO]Q00-20200814001 修正舊版ISO表單版更至5833時，若文件編號是由系統產生時，文件編號未回寫到formInstace
- **Commit ID**: `67d3b03c6138b0710f5ef44d5d03856e095bcff5`
- **作者**: waynechang
- **日期**: 2020-08-14 18:17:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`

### 29. [Web]A00-20200810001 修正表單中有必填欄位，當上傳附件後必填欄位的星號(RWD表單)和紅色外框(絕對表單)會消失
- **Commit ID**: `b1506f53f56505c6a06f18200e4b4a4ea2678881`
- **作者**: 王鵬程
- **日期**: 2020-08-14 14:19:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 30. [Web]A00-20200813001 修正ISO表單附件設為「ISO表單附件轉PDF」時，開啟附件上傳畫面後會提示錯誤訊息
- **Commit ID**: `b0e1e814d8672c3cbbff53d2913a420d3f528e4d`
- **作者**: waynechang
- **日期**: 2020-08-14 13:53:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 31. [BPM APP]Q00-20200813001 調整IMG已轉派流程，原中間層與動態渲染表單沒有顯示表單詳情按鈕的問題
- **Commit ID**: `8dcd9e5a7dd7478a8ff66c1c9e3b3b34a95df970`
- **作者**: cherryliao
- **日期**: 2020-08-13 19:19:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 32. [E10]Q00-20200812002 E10流程 執行到「回寫E10服務任務」時，須將E10回傳錯誤訊息回寫至BPM流程變數並設定流程讓表單可以回到「人員處理」的關卡，並將訊息顯示在簽核畫面上。[補]
- **Commit ID**: `cf5348f8adbfaff321e6031c26feca7aeedc20fa`
- **作者**: waynechang
- **日期**: 2020-08-13 18:05:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 33. [BPM APP]Q00-20200813004 調整動態渲染表單在傳送表單定義時的相關多語系詞彙
- **Commit ID**: `a967feceb61df98b31e4de7504ed1c28f1cc6444`
- **作者**: yamiyeh10
- **日期**: 2020-08-13 15:19:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 34. [BPM APP]Q00-20200813002 修正動態渲染表單中Grid和附件元件當流程設定隱藏時沒有效果問題
- **Commit ID**: `ffe9f69ab00cab7b25ec43add9fc5ee23df863d3`
- **作者**: yamiyeh10
- **日期**: 2020-08-13 14:41:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 35. [E10]Q00-20200812002 E10流程 執行到「回寫E10服務任務」時，須將E10回傳錯誤訊息回寫至BPM流程變數並設定流程讓表單可以回到「人員處理」的關卡，並將訊息顯示在簽核畫面上。[補]
- **Commit ID**: `e5142ba3a8ba8c0c031b920463ba919462c1251f`
- **作者**: waynechang
- **日期**: 2020-08-13 13:49:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 36. [BPM APP]Q00-20200812003 修正從IMG推播消息或行事曆進動態渲染表單時，在工作已被處理依舊會顯示表單內容的問題
- **Commit ID**: `5b4e54d26a962e9132def556c472ec7022a7484b`
- **作者**: cherryliao
- **日期**: 2020-08-13 13:06:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 37. [Web]A00-20200803003 修正: ajax_CommonAccessor.login報錯
- **Commit ID**: `33a8dc933b783adf135dd6bfd2864c0c89a00ab8`
- **作者**: yanann_chen
- **日期**: 2020-08-13 10:24:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`

### 38. [BPM APP]Q00-20200812004 修正動態渲染表單在智能快簽的待辦事項未顯示擱置按鈕問題
- **Commit ID**: `164481486a62206c882cd978d445fa1943360f2d`
- **作者**: yamiyeh10
- **日期**: 2020-08-13 10:18:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 39. [E10]Q00-20200812002 E10流程 執行到「回寫E10服務任務」時，須將E10回傳錯誤訊息回寫至BPM流程變數並設定流程讓表單可以回到「人員處理」的關卡，並將訊息顯示在簽核畫面上。
- **Commit ID**: `87fa1e28f60209c2be257b7b90ba18599fcea9e5`
- **作者**: waynechang
- **日期**: 2020-08-12 17:34:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10Manager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerLocal.java`

### 40. [流程引擎]Q00-20200812001 當Router的線有設定條件，且結束的關卡為「EndEvent」關卡時；若關卡線的條件滿足時，會無法結案
- **Commit ID**: `f556a49dae09de22129c51f18a2872a71dce8d1e`
- **作者**: waynechang
- **日期**: 2020-08-12 17:22:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 41. [BPM APP]Q00-20200811004 修正行動端在發起流程沒掛載表單時錯誤訊息頁面無顯示錯誤圖片問題
- **Commit ID**: `dc4f8e35a092b37c76fc7aa8d8340bb873d81d8f`
- **作者**: yamiyeh10
- **日期**: 2020-08-12 16:20:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 42. [BPM APP]Q00-20200811002 修正行動端發起或簽核有多部門選擇時進階按鈕仍可點擊問題
- **Commit ID**: `3b2e29a747f1ee53b331626676c0111ab013e6cf`
- **作者**: pinchi_lin
- **日期**: 2020-08-12 15:57:20
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 43. [帳號啟用]邏輯變更 1.增加SystemVariable設定初始值 2.組織同步新增使用者值接依初始值給予預設帳號狀態
- **Commit ID**: `cb705f7ec796fe2cad7ca76f34632037c7691f41`
- **作者**: jerry1218
- **日期**: 2020-08-12 15:48:44
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportConstants.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/cfg/StepCfgs.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/NaNaTableUtilV2_0.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/NanaTableUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/SyncTableUtilV1_1.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/SyncTableUtilV1_2.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/Emp.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/User.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/ImportOrgXML.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/OrgXMLGenerator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ws/DomainObjOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@syncorg/update/5.8.4.1_DDL_MSSQL_1.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@syncorg/update/5.8.4.1_DDL_Oracle_1.sql`

### 44. [BPM APP]Q00-20200811003 修正行動端的退回重辦畫面在選擇退回關卡時按鈕會位移跑版問題
- **Commit ID**: `5e6e9737d800925396893c3518b7afaefae1ca93`
- **作者**: yamiyeh10
- **日期**: 2020-08-12 15:02:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 45. [Web]Q00-20200811005 修正報表設計器檢查程式代號重複時，仍可以新增
- **Commit ID**: `ffd23e1537292a8c8d68a7702db75ebd0b02e478`
- **作者**: waynechang
- **日期**: 2020-08-11 17:02:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`

### 46. [RESTFul]調整發起、撤銷流程RESTful服務接口[補]
- **Commit ID**: `1dba1d83ef47ba473bcb212f81de41fb36c727d7`
- **作者**: walter_wu
- **日期**: 2020-08-11 16:42:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 47. [BPM APP]S00-20200525005 調整行動端表單選項元件與grid綁定功能可支援額外輸入框選項
- **Commit ID**: `6d8951a50a1d5e38e88bc52d686ed62058f999c5`
- **作者**: cherryliao
- **日期**: 2020-08-11 12:21:59
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 48. [帳號啟用]修正搜尋後如果只有一筆的情況下,加上全選會沒辦法按操作選項的問題
- **Commit ID**: `daf189a04b9f62b10829f9cb3b73e92bb3a19af9`
- **作者**: jerry1218
- **日期**: 2020-08-11 12:01:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp`

### 49. [BPM APP]S00-20200525003 新增行動端支援T100表單原始值在欄位下方顯示功能
- **Commit ID**: `bf44ce8c9ab136f03b3d71046427d53cc4461587`
- **作者**: yamiyeh10
- **日期**: 2020-08-11 11:49:49
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 50. 更新5841BPMRsrcBundle.xlsx 及 DB.patch
- **Commit ID**: `ca75f3e1f7430034de70a67f43dd6ec751fa9ddf`
- **作者**: waynechang
- **日期**: 2020-08-10 14:57:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 51. [BPMAPP]Q00-20200331005 調整行動端表單選項元件支援可額外產生輸入框功能
- **Commit ID**: `66a944e58763cf428fd3f10b40c1580a4885a647`
- **作者**: yamiyeh10
- **日期**: 2020-08-07 19:12:26
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/resources/html/AppSelectElementTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerSelect.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 52. [BPM APP]S00-20200525001調整BPMAPP表單grid元件支持必填樣式
- **Commit ID**: `f8a66c418bebfd9457d6be53f5c2aa0efd35f1c9`
- **作者**: pinchi_lin
- **日期**: 2020-08-07 17:27:54
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 53. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `25bf2b65ccca7b6db28dd3258c907fa38139b800`
- **作者**: yanann_chen
- **日期**: 2020-08-07 17:25:42
- **變更檔案數量**: 0

### 54. [流程引擎]C01-20200708004 修正: 通知信件內容沒有正確斷行[補]
- **Commit ID**: `1b83e03b1fd21481a4b4eb696515ca06e1a78017`
- **作者**: yanann_chen
- **日期**: 2020-08-07 17:25:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`

### 55. [內部] 將5841多語系檔併入總檔
- **Commit ID**: `4caa7ff29f79a24f39ad601b4e0f9401e8172417`
- **作者**: lorenchang
- **日期**: 2020-08-07 17:25:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 56. [BPM APP]Q00-20190924001調整IMG在詳情表單點按鈕返回(非左上箭號)會保留篩選條件並刷新功能
- **Commit ID**: `2b48fc31e315d3a43bf01b63532661716baf34d5`
- **作者**: pinchi_lin
- **日期**: 2020-08-07 17:22:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/jdajia.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 57. [流程引擎]C01-20200708004 修正: 通知信件內容沒有正確斷行
- **Commit ID**: `8f2df546a06edd442330c22f3dff64490c736597`
- **作者**: yanann_chen
- **日期**: 2020-08-07 14:16:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`

### 58. [BPM APP]調整IMG推播消息可連到動態渲染表單應用[補]
- **Commit ID**: `5f2ab0da30566cc101b14f16736534d7312e8891`
- **作者**: cherryliao
- **日期**: 2020-08-07 13:54:12
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 59. [Web]Q00-20200806002 產品回收NTKO專案
- **Commit ID**: `aabdecbd524e58b8eae4e9252183d1592012bd1c`
- **作者**: waynechang
- **日期**: 2020-08-06 14:35:48
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 60. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `2c630feddd6700315bd920cac6cf7ca46e31dc36`
- **作者**: cherryliao
- **日期**: 2020-08-06 11:59:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 61. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `9e67c1229782356a6b730a18f07f9071cd5cadde`
- **作者**: cherryliao
- **日期**: 2020-08-06 11:54:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 62. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `a504a18de9dc461324326ef153eeb7633badf7d1`
- **作者**: cherryliao
- **日期**: 2020-08-06 11:20:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 63. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `52dbb072be6d6a0a984aa2592c836c8cb411b425`
- **作者**: pinchi_lin
- **日期**: 2020-08-05 14:00:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 64. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `57842af0798c0f8bf33fe6313d48310187c067da`
- **作者**: yamiyeh10
- **日期**: 2020-08-04 15:57:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 65. [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
- **Commit ID**: `512283e199e33878c631f07f514043a93aac2b56`
- **作者**: cherryliao
- **日期**: 2020-08-04 12:03:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 66. [Web]A00-20200728001 修改系統設定的第四個選項(trace.list.orderby)的描述
- **Commit ID**: `2be2b7938df23c4ec5ead4e1185b6d5b87b8833d`
- **作者**: 王鵬程
- **日期**: 2020-08-03 15:37:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`

### 67. [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
- **Commit ID**: `e26090935f653e331554fd9e735004152d6e90dc`
- **作者**: cherryliao
- **日期**: 2020-07-31 19:17:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/LineMessageObject.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 68. [RESTFul]調整同意派送RESTful服務接口
- **Commit ID**: `e0e94518dd7ff2300b9f018666bc978c10c9ed2e`
- **作者**: yamiyeh10
- **日期**: 2020-07-31 19:07:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 69. [RESTFul]調整發起、撤銷流程RESTful服務接口[補]
- **Commit ID**: `effe57fd8edb6e07d27c7186e00e4123d9d9e26a`
- **作者**: pinchi_lin
- **日期**: 2020-07-31 17:17:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 70. [RESTFul]調整發起、撤銷流程RESTful服務接口
- **Commit ID**: `acc729ad809e1eff0bfdba256bbcf3a937ae1a93`
- **作者**: pinchi_lin
- **日期**: 2020-07-31 15:00:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 71. [RESTFul]調整取追蹤清單RESTful服務接口
- **Commit ID**: `e7504a112751c46b05f795a8be88a03d93b896e8`
- **作者**: yamiyeh10
- **日期**: 2020-07-31 13:36:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/TraceListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java`

### 72. [RESTFul]調整取待辦清單RESTful服務的回傳值
- **Commit ID**: `8c882ad2c65fae95afcbb944261a49bb7271fe86`
- **作者**: yamiyeh10
- **日期**: 2020-07-30 12:09:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 73. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `8b8ee892c76410a2381c61f105ada60dc7cb2991`
- **作者**: pinchi_lin
- **日期**: 2020-07-28 16:03:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 74. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `c868469df81560cb1762c9dbbd4e958c9c44780f`
- **作者**: yamiyeh10
- **日期**: 2020-07-28 15:36:05
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/img-share-square-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/img-share-square.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`

### 75. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `d8f1060e3e7d968164f5063f3d32c20ac4ac4a8e`
- **作者**: yamiyeh10
- **日期**: 2020-07-28 11:01:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 76. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `9ec38b7cc9dcfd93a3c0465bc7f436a77fa41142`
- **作者**: pinchi_lin
- **日期**: 2020-07-27 19:16:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 77. [BPM APP]調整IMG行事曆可連到動態渲染表單應用[補]
- **Commit ID**: `090894f37cbc9adb01166ee6c079d0487fddcafb`
- **作者**: yamiyeh10
- **日期**: 2020-07-27 09:34:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 78. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `c28cc90eef96116a81d59b40d8fa0600b335177b`
- **作者**: cherryliao
- **日期**: 2020-07-24 20:28:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 79. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `78eb653911176cf144f5fc838d583507ef97f36c`
- **作者**: yamiyeh10
- **日期**: 2020-07-24 15:49:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 80. [BPM APP]調整IMG行事曆可連到動態渲染表單應用
- **Commit ID**: `441e8c20a50ed436c7b033f520a42cad166f1160`
- **作者**: yamiyeh10
- **日期**: 2020-07-24 14:44:27
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 81. [Web]S00- 增加報表設計器功能
- **Commit ID**: `48e3e832d79d6c96ce7dcb9b2891539a213952a8`
- **作者**: waynechang
- **日期**: 2020-07-24 12:04:51
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ReportDefinitionManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/report/ReportDesignerDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/JSPFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportTemplate.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/customModule/ChartQueryTemplate.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`

### 82. [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
- **Commit ID**: `b50e74a7d04df667a7da67573cec5c576625e97f`
- **作者**: 詩雅
- **日期**: 2020-07-23 17:54:29
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 83. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `95242b964a8c9ab05801c38e9c798207cafa0607`
- **作者**: cherryliao
- **日期**: 2020-07-23 13:34:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 84. [BPM APP]調整IMG推播消息可連到動態渲染表單應用
- **Commit ID**: `983602a3d98d4d5f509a0f04d072eeb360dea558`
- **作者**: pinchi_lin
- **日期**: 2020-07-23 11:25:43
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`

### 85. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `ed5f988a12c96f1cdb1942ab168a4cfa612f1531`
- **作者**: yamiyeh10
- **日期**: 2020-07-23 10:45:13
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java`

### 86. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `51cf337ad5de8c7021bc52d9398e9518ee288e6d`
- **作者**: yamiyeh10
- **日期**: 2020-07-23 10:35:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`

### 87. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `6d5d8a23cd16ad6b9e5595e70c72edcb3eeff07d`
- **作者**: cherryliao
- **日期**: 2020-07-23 10:23:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 88. [BPM APP]調整IMG列表撈取動態生成表單應用ID[補]
- **Commit ID**: `26261e65e347b641ab78113120983b6944ee6390`
- **作者**: pinchi_lin
- **日期**: 2020-07-23 10:00:03
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 89. [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
- **Commit ID**: `fa829336f10345bd17e30d752c4ea8b8e2e016b0`
- **作者**: 詩雅
- **日期**: 2020-07-22 10:55:34
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 90. [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
- **Commit ID**: `b6a6b35cf6a5d66a7499f037e9d2578c6e50a7f1`
- **作者**: pinchi_lin
- **日期**: 2020-07-21 16:56:32
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 91. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `e02b1f017afda47a1855c6959112014e844e68c4`
- **作者**: yamiyeh10
- **日期**: 2020-07-17 18:23:42
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 92. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `7a7eb2dcd2bf93ad86a50f41509063d7ab8d7cdb`
- **作者**: yamiyeh10
- **日期**: 2020-07-17 18:21:51
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterDesignFormReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`

### 93. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `4910f7319828665f14b0c5316c6cd32f7fc63e50`
- **作者**: yamiyeh10
- **日期**: 2020-07-17 18:19:45
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`

### 94. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `203c40faa25932bd3d5afe922ce29a0da16e6970`
- **作者**: cherryliao
- **日期**: 2020-07-16 17:58:10
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/DinWhaleCacheSingletonMap.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 95. [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
- **Commit ID**: `5b16af223bf95872e694feddf161ec46cbd87b36`
- **作者**: pinchi_lin
- **日期**: 2020-07-16 17:36:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorLocal.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 96. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `2c304604dc3114a600aa0c1bf4d6fb16886678fd`
- **作者**: yamiyeh10
- **日期**: 2020-07-16 14:47:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 97. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `6837c56665c438ac62d1cdd08aa577e766782862`
- **作者**: yamiyeh10
- **日期**: 2020-07-16 14:45:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 98. [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
- **Commit ID**: `e7ca4e1f8ae0a6a3cdfb46a571bd8c0fdfe1abb8`
- **作者**: 詩雅
- **日期**: 2020-07-15 19:47:37
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java`

### 99. [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能[補]
- **Commit ID**: `07240ce7367bc2cd03793969dd00a5756bc73906`
- **作者**: pinchi_lin
- **日期**: 2020-07-15 19:26:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 100. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `7e78718cddd83883fde8115b0d7fb70bca659a7f`
- **作者**: yamiyeh10
- **日期**: 2020-07-15 18:16:21
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 101. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `bf0c42cd47068ce0ce3f30f463883fb210f92760`
- **作者**: yamiyeh10
- **日期**: 2020-07-15 18:11:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`

### 102. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `ce8a759e6d43f9c161aa668cbdd11246ca224b5e`
- **作者**: yamiyeh10
- **日期**: 2020-07-14 16:24:17
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/BriefList.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/NodeDataList.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/NodeItemList.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/NodeUserList.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 103. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `79aafd761785b630716de18b0aebc4300137746e`
- **作者**: cherryliao
- **日期**: 2020-07-14 16:15:26
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 104. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `2e5c02b0dd833e828411c92c7de91d3fd94dbd03`
- **作者**: cherryliao
- **日期**: 2020-07-14 15:50:26
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 105. [流程引擎]A00-20200709001 修正呼叫removeAbsencerecord方法，Absencerecord資料表資料無法移除
- **Commit ID**: `669381c0a102472b75fb395b36bddf4e3c8d1c43`
- **作者**: 林致帆
- **日期**: 2020-07-13 18:37:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DDL_Oracle_1.sql`

### 106. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `bac77b48d89f850b9789a87190c7597669bb1dfb`
- **作者**: yamiyeh10
- **日期**: 2020-07-10 19:02:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 107. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `ad6962ae37c5b4a5384ed3947f1e92f2fa7d4c39`
- **作者**: yamiyeh10
- **日期**: 2020-07-10 19:01:22
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ImageElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DataListContent.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`

### 108. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `fbd926dc4e80a74599b8658af5345304ecb7f7f8`
- **作者**: 詩雅
- **日期**: 2020-07-10 17:01:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineOperation.java`

### 109. [BPM APP]新增LINE官方帳號中流程通知訊息支援展示表單欄位功能
- **Commit ID**: `0161705adba8aa50d7dbe9dca66404b68a7b4be1`
- **作者**: pinchi_lin
- **日期**: 2020-07-09 10:13:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorLocal.java`

### 110. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `e4b8f3bd041f991ffc9d5ae98a5a270cf32cc767`
- **作者**: 詩雅
- **日期**: 2020-07-07 17:26:54
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Line.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineOperation.java`

### 111. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `32960e738adc5fcc0af82f526caa41174f71d7e6`
- **作者**: cherryliao
- **日期**: 2020-07-06 13:49:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`

### 112. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `4bf2ba3bdb7dcb22258c86ac4dac0345344bd791`
- **作者**: yamiyeh10
- **日期**: 2020-07-03 17:31:46
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 113. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `891312888e0465444029eb99de90feb84599896c`
- **作者**: yamiyeh10
- **日期**: 2020-07-03 09:39:03
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformRESTTransferTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`

### 114. [ESS]A00-20200312003 修正ESS員工培訓規劃課程查詢(ESSQ92) insert語法中OID的值有誤
- **Commit ID**: `04b857159eada3d38ca6c4e1f6da7196960f1edc`
- **作者**: 王鵬程
- **日期**: 2020-07-02 16:02:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.7.3.2_AppForm_DML_MSSQL_1.sql`

### 115. [BPM APP]新增LINE官方帳號推送消息功能[補]
- **Commit ID**: `6595c7e2fdf217ee5c19e1fbeca298f134d95a03`
- **作者**: yamiyeh10
- **日期**: 2020-07-02 11:19:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/LineMessageObject.java`

### 116. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `1d87d8611498b1a74dd31ae0b90ea35dba81d0a9`
- **作者**: cherryliao
- **日期**: 2020-07-01 19:50:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 117. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `7e34de445726faa77c285312f667891f1136f6c4`
- **作者**: 詩雅
- **日期**: 2020-07-01 15:49:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 118. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `25fd82af7da5137d7fb94fa65f772b1728b4a98d`
- **作者**: cherryliao
- **日期**: 2020-07-01 11:18:51
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 119. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `e717d03a0657b6b8f8cd46819b61ada6c2238b93`
- **作者**: yamiyeh10
- **日期**: 2020-07-01 09:44:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`

### 120. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `24c01798bcbe4289bfb4ea61ebe6b08c22ef8eb3`
- **作者**: yamiyeh10
- **日期**: 2020-06-30 15:44:26
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContainerListData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContainerTabData.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 121. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `d8a4a78bb2d3689cfa5374cbc998102e6a4d454f`
- **作者**: cherryliao
- **日期**: 2020-06-29 16:01:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 122. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `f116b5cf83a482b33377d3ae4537d709d7b46946`
- **作者**: 詩雅
- **日期**: 2020-06-29 14:53:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/LineCacheSingletonMap.java`

### 123. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `1e20819dcd1c368b8f49ce92c2bf43892934a7bc`
- **作者**: yamiyeh10
- **日期**: 2020-06-24 18:29:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 124. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `d66ccadcf4ffc8d3a6a7136af1fbad0591449990`
- **作者**: cherryliao
- **日期**: 2020-06-24 17:41:42
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/LineCacheSingletonMap.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 125. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `1fcf16afcb860ca6e6ed65597174af4e7e3a679f`
- **作者**: 詩雅
- **日期**: 2020-06-24 12:12:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 126. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `861346ae8bfef21ccf90d44b08884fd32b069fcb`
- **作者**: 詩雅
- **日期**: 2020-06-23 17:43:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 127. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能
- **Commit ID**: `ec17d9abb0e07e6366f867cfd6563b7ffa104865`
- **作者**: yamiyeh10
- **日期**: 2020-06-23 16:40:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 128. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `97acfdb26db6052eed505596b69433a52cc2c643`
- **作者**: yamiyeh10
- **日期**: 2020-06-23 15:34:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 129. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `485242d5cb2687b63ae1a18695fc48688a23f6f6`
- **作者**: cherryliao
- **日期**: 2020-06-23 15:19:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 130. [T100]Q01-20200620001 修正執行T100_SyncTables_Oracle.sql，資料庫出現錯誤
- **Commit ID**: `85e6e2f66683eb3caa3c6b98bc6c84adc2b5908e`
- **作者**: 王鵬程
- **日期**: 2020-06-20 11:19:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@syncorg/create/T100_SyncTables_Oracle.sql`

### 131. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `3c792bcd81aa98fd2be13e659150e05492a6849e`
- **作者**: cherryliao
- **日期**: 2020-06-19 18:41:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 132. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `eef74fa483f39cf87cf32d86bfcfcf5662280b0d`
- **作者**: 詩雅
- **日期**: 2020-06-19 16:44:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 133. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `400e86b66c42cd2708dca5d41e8740979ae71a26`
- **作者**: cherryliao
- **日期**: 2020-06-19 15:45:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 134. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `ccba7883d1a834429e209975afd3590edceffb51`
- **作者**: cherryliao
- **日期**: 2020-06-19 09:44:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 135. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `c9a5ccf56a0f58a47c430c7466f9d5eee3669752`
- **作者**: 詩雅
- **日期**: 2020-06-18 18:58:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 136. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `5ebe7e2685edfd4d3e216d01866464e4b74dfce2`
- **作者**: yamiyeh10
- **日期**: 2020-06-18 15:13:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 137. [流程引擎]A00-20200609001 調整DB欄位長度(WorkItem.attachmentHits)
- **Commit ID**: `75f8dd5ea035501d4ccfd158826a24ddf3abeaa7`
- **作者**: waynechang
- **日期**: 2020-06-18 12:01:30
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DDL_Oracle_1.sql`

### 138. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `6c652f0a7ae490d83c501da495febf37393341dd`
- **作者**: yamiyeh10
- **日期**: 2020-06-18 11:58:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 139. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `301ad02dc8ab7e8f99d0ca1f210f8b743d0c0e71`
- **作者**: yamiyeh10
- **日期**: 2020-06-18 11:33:20
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Line.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/LineCacheSingletonMap.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 140. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `e27a6c75ffb37a20d02f97b72c6a3cd86ddeeada`
- **作者**: cherryliao
- **日期**: 2020-06-18 10:58:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 141. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `a7849c5a7adc1196a73765efe2cc75b07b657b86`
- **作者**: cherryliao
- **日期**: 2020-06-18 08:53:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 142. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `ff5219a3b6e7008e70844dea89fe9c3abc816acb`
- **作者**: cherryliao
- **日期**: 2020-06-18 08:46:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`

### 143. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `64c755fd817b09fd64aa588c071901fec0559c16`
- **作者**: yamiyeh10
- **日期**: 2020-06-16 17:40:58
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/LineCacheSingletonMap.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 144. [BPM APP]調整IMG列表撈取動態生成表單應用ID
- **Commit ID**: `c69b443f6297ec45b628f4ff052518a8aaa1693c`
- **作者**: 詩雅
- **日期**: 2020-06-16 16:31:41
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 145. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能[補]
- **Commit ID**: `5dd00bf3b0669cc05023799a253ce66b48a6c40f`
- **作者**: cherryliao
- **日期**: 2020-06-16 15:06:11
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/LineCacheSingletonMap.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 146. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能
- **Commit ID**: `a1b06b44041ee18bddde0649cc8d6a10fb329f0d`
- **作者**: cherryliao
- **日期**: 2020-06-15 19:01:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/LineMessageObject.java`

### 147. [BPM APP]新增LINE官方帳號中與訊息操作相關的功能
- **Commit ID**: `5caaa3d756dc0e98b1b026d1d856679d49341f5c`
- **作者**: yamiyeh10
- **日期**: 2020-06-15 16:30:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/LineCacheSingletonMap.java`

### 148. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `8ed15dce28b5ec7d1abe8a2390d52e1d318358ee`
- **作者**: 詩雅
- **日期**: 2020-06-15 13:55:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 149. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `26efe1b6527b076e8957aad506570dca909b302c`
- **作者**: yamiyeh10
- **日期**: 2020-06-12 12:31:36
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileDynamicFormRecord.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileDynamicFormRecordDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileAbstractPlatform.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.4.1_mobile_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.4.1_mobile_DDL_Oracle_1.sql`

### 150. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `8e0f00d9c23f78bfae815486f2eaa4b168352e51`
- **作者**: cherryliao
- **日期**: 2020-06-11 15:40:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 151. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `562e1bc28bf69c3229732e5388dab2aa59462dba`
- **作者**: cherryliao
- **日期**: 2020-06-11 15:34:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 152. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `c68a6cf14bf53123d941ad80f8e154862e283c46`
- **作者**: 詩雅
- **日期**: 2020-06-10 18:19:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`

### 153. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `cd2c54838526c4053441fddff7ae71ec88b662dd`
- **作者**: yamiyeh10
- **日期**: 2020-06-10 17:20:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 154. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `02a45df2485c48f2a2bf633edf02e0395cba0609`
- **作者**: cherryliao
- **日期**: 2020-06-10 15:00:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 155. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `4d4f692b7f3b1648018646602409851da358eb15`
- **作者**: 詩雅
- **日期**: 2020-06-10 13:41:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 156. [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
- **Commit ID**: `82ba9b958e8b3be56cb028d0b13f9bcdf5a49ad3`
- **作者**: 詩雅
- **日期**: 2020-06-09 15:33:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 157. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `1409c275a24797dc9d5dbbc40a4659d0a54ec10a`
- **作者**: cherryliao
- **日期**: 2020-06-05 17:40:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 158. [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
- **Commit ID**: `ba422eba5799c7f41327d57609cf9078c294fb18`
- **作者**: 詩雅
- **日期**: 2020-06-05 15:42:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 159. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `ebcbebca3128fd85223298a0a2f5487ed9287ee4`
- **作者**: yamiyeh10
- **日期**: 2020-06-04 15:53:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 160. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `df16c67178a2ae863e88b3f193c8a4c65e60a560`
- **作者**: 詩雅
- **日期**: 2020-06-04 15:21:06
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 161. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `fc81821d5b094d74b2c9807294a18da3e87f8c31`
- **作者**: 詩雅
- **日期**: 2020-06-04 14:35:33
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`

### 162. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `f41f6a06cb82a196f4335c32340418c6cc56091c`
- **作者**: yamiyeh10
- **日期**: 2020-06-03 18:14:13
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 163. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `aa3268ab4dc9e0e6b52e648860accc5155c5839b`
- **作者**: cherryliao
- **日期**: 2020-06-03 17:28:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 164. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `225a5eda215df1671cf42ffbb5487f548050ebf4`
- **作者**: cherryliao
- **日期**: 2020-06-03 15:12:36
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileAbstractPlatform.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/DinwhaleFormtUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 165. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能[補]
- **Commit ID**: `6aab65ab79f413cf4d8db2d58724196f334f3c98`
- **作者**: yamiyeh10
- **日期**: 2020-06-03 12:01:38
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DataListContent.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 166. [BPM APP]新增鼎捷移動動態生成表單之中間層動態生成接口功能
- **Commit ID**: `ab9646e164fd4119a271424692ded8b84b614f8e`
- **作者**: yamiyeh10
- **日期**: 2020-06-02 13:48:54
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/Authentication.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageExecutionRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/TipListData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 167. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `1eb943f912f752c781b2d0821f1b04c949fe3e3a`
- **作者**: yamiyeh10
- **日期**: 2020-05-29 10:33:33
- **變更檔案數量**: 19
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ActionListContentData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ActionListData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ActionListParamData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/AdditionalData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContainerListData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContainerTagListData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ElementListData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ElementListEditValueData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FixedListData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleDesignFormReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleDesignFormRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageExecutionDesignFormRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterDesignFormReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterDesignFormRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataDesignFormReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataDesignFormRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/TriggerBehaviorData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ValueListData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/VariableListData.java`

### 168. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能[補]
- **Commit ID**: `fc6ec97c750836903f4226a94e451015e220d017`
- **作者**: yamiyeh10
- **日期**: 2020-05-28 15:20:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 169. [BPM APP]新增鼎捷移動動態生成表單之傳送表單定義功能
- **Commit ID**: `42595660fad47e3f6d3ee59ac1bea3b95eef3fd8`
- **作者**: yamiyeh10
- **日期**: 2020-05-28 14:31:20
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 170. [BPM APP]新增LINE圖文選單BPM首頁與聯絡人圖示
- **Commit ID**: `9ebe7c273d89c15913c4509e0d5599835cab31c0`
- **作者**: 詩雅
- **日期**: 2020-05-27 17:52:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/Line Business Image/Contact_icon.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/Line Business Image/DashBoard_icon.png`

### 171. [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
- **Commit ID**: `b109bb14cce002eefba2c3ddf2e7062fa7032f33`
- **作者**: cherryliao
- **日期**: 2020-05-26 15:27:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 172. [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
- **Commit ID**: `b39f08f49b2965e3ab83809f6ff7dd9c15d76612`
- **作者**: yamiyeh10
- **日期**: 2020-05-26 14:57:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 173. [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
- **Commit ID**: `af0fa6857ef33f2e0d58a8af7730a11bf00fb15b`
- **作者**: cherryliao
- **日期**: 2020-05-26 14:04:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 174. [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
- **Commit ID**: `05aac3ee35a0a73a0218910fa845140f61cc0896`
- **作者**: yamiyeh10
- **日期**: 2020-05-26 12:28:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.4.1_mobile_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.4.1_mobile_DML_Oracle_1.sql`

### 175. [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
- **Commit ID**: `e2c0386525085973dc7eaafb1910f0f6c0ff4e6c`
- **作者**: yamiyeh10
- **日期**: 2020-05-26 11:54:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 176. [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
- **Commit ID**: `426a15cdf994c4778129d96488bc04a53c175a7f`
- **作者**: yamiyeh10
- **日期**: 2020-05-26 11:52:18
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 177. [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
- **Commit ID**: `11d2cd1c87d560be6a11a05cae94ab3795a22f4e`
- **作者**: cherryliao
- **日期**: 2020-05-25 18:42:56
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DML_InitMobileDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DML_InitMobileDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.4.1_mobile_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.4.1_mobile_DML_Oracle_1.sql`

### 178. [BPM APP]LINE圖文選單新增BPM首頁連結
- **Commit ID**: `77db7fd0432787720eed57beded55338e46c79c8`
- **作者**: 詩雅
- **日期**: 2020-05-22 18:04:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`

### 179. [merge後更改]修改UpdateSQL符合S00-20200508002
- **Commit ID**: `22bf87496f52f385eb30d0d520203b2464607fc2`
- **作者**: jerry1218
- **日期**: 2020-05-21 10:27:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`

### 180. 修正merge導致的錯誤
- **Commit ID**: `0fe920ed4408e7067cb0eab8a01b794e481b0567`
- **作者**: jerry1218
- **日期**: 2020-05-21 10:10:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`

### 181. [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
- **Commit ID**: `702802e784ce8c0e394c200f18ddc95285c8b499`
- **作者**: 詩雅
- **日期**: 2020-05-18 15:07:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 182. [帳號啟用]程式調整
- **Commit ID**: `0c9859508f4cb8eabccd4f9264fe713bb30ee935`
- **作者**: jerry1218
- **日期**: 2020-04-29 11:19:51
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/UserManageAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/Constants.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManageResult.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`

### 183. [帳號啟用]日起選擇&模糊查詢部分調整
- **Commit ID**: `43fc25e514eb4ed6d3b406fecf43d6077ff79eaf`
- **作者**: jerry1218
- **日期**: 2020-04-28 11:32:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/VipUserView.jsp`

### 184. [帳號啟用]多語系補充
- **Commit ID**: `9927b3f242b5cdfa025a86ae9eff6f499e807af7`
- **作者**: jerry1218
- **日期**: 2020-04-27 16:29:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`

### 185. 新增帳號啟用功能
- **Commit ID**: `c166e4205c42bde4440a77f2b67ce70e6093169a`
- **作者**: jerry1218
- **日期**: 2020-04-27 16:28:27
- **變更檔案數量**: 71
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - ❌ **刪除**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/TrinityDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/UserCurrentType2IntFieldConversion.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/UserCurrentType.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserForListDTO.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserManageResultDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/VipUserCacheSingletonMap.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/exception/UserNotVaildException.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserManageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportConstants.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/cfg/StepCfgs.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/NaNaTableUtilV2_0.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/NanaTableUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/SyncTableUtilV1_1.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/SyncTableUtilV1_2.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/Emp.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/User.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/ImportOrgXML.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/OrgXMLGenerator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ws/DomainObjOperation.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/TrinintyDtoHelper.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/TrinityGateWay.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/TrinityGateWayBean.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/TrinityManager.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/TrinityManagerLocal.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/TrinityModelManager.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/TrinityTemplateTrans.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/dao/DAOFactory.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/dao/ITrinityDao.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/dao/JDBCDAOFactory.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/dao/OJBDAOFactory.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/dao/OJBTrinityDao.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/OnlineUserAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/UserManageAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/organization/UserManageView.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageUser-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManageResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/syncorg/SyncStep_config.xml`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5841.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DDL_Oracle_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@syncorg/create/SyncTables_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@syncorg/create/SyncTables_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@syncorg/update/5.8.4.1_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@syncorg/update/5.8.4.1_DDL_Oracle_1.sql`

### 186. [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能[補]
- **Commit ID**: `b0dff26321bab98f9d7c1ae0ab167bc343d07e6a`
- **作者**: 詩雅
- **日期**: 2020-05-18 09:31:59
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/LineMessageObject.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 187. [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
- **Commit ID**: `94680797420499764ed227f7a43dd9c13ef19236`
- **作者**: yamiyeh10
- **日期**: 2020-05-15 16:01:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 188. [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
- **Commit ID**: `317a0d17187ba8d825c9cf0b15c802f20099a231`
- **作者**: yamiyeh10
- **日期**: 2020-05-15 15:08:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/LineMessageObject.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 189. [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
- **Commit ID**: `3bfe6ae60c1be2634b75cd31ea810e6330b9b367`
- **作者**: yamiyeh10
- **日期**: 2020-05-14 18:31:54
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/LineMessageObject.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 190. [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能
- **Commit ID**: `70f1350053d602d1d941a26cf3a4a4f0e8b51719`
- **作者**: cherryliao
- **日期**: 2020-05-14 18:22:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Line.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 191. [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
- **Commit ID**: `0e1bfe4e743230bd529640f2b70eef16091ff424`
- **作者**: cherryliao
- **日期**: 2020-05-14 11:28:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribe.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 192. [BPM APP]Line-OA加入取消與恢復訂閱消息機制[補]
- **Commit ID**: `85cea1f7a84524efac43bcc52e5e911c58ed1a6d`
- **作者**: cherryliao
- **日期**: 2020-05-14 11:04:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 193. [BPM APP]Line-OA加入取消與恢復訂閱消息機制
- **Commit ID**: `8f67881745653451876935d0bc7dff6c4d49dee8`
- **作者**: yamiyeh10
- **日期**: 2020-05-14 10:18:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`

### 194. [BPM APP]新增LINE綁訂用戶可透過與機器人對話查詢BPM部門聯絡人資料功能
- **Commit ID**: `dc5a6605906b7d4c261ebbb43f43c4cb2f62c01b`
- **作者**: yamiyeh10
- **日期**: 2020-05-14 10:07:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/LineMgr.java`

### 195. [流程引擎]S00-20200508002簡化模組(ModuleDefinition)及程式(ProgramDefinition)的多語系格式方便維護(XML轉JSON)
- **Commit ID**: `f473586ef3220164e15bdc092b2fd613d3284df7`
- **作者**: wusnnn
- **日期**: 2020-05-12 09:31:47
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ModuleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-ems/create/Init_EMS_Data_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-ems/create/Init_EMS_Data_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mcloud/create/InitMCloud_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mcloud/create/InitMCloud_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DML_InitMobileDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DML_InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@sap/create/InitSap_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@sap/create/InitSap_Oracle.sql`

### 196. 流程模組維護會使用到主要以下程式: ProcessModuleDef.java ProcessModuleDefMgr.java ProcessModuleDefBean.java ProcessModuleDefDelegate.java ProcessModuleDefLocal.java ProcessModuleViewer.java ProcessModuleAction.java ProcessModuleDefinitionDTO.java ProcessModuleAccessRightVo.java 1.因不維護，故移除，其他程式有應用到上述程式內容，確認後不需使用的已刪除 2.左側功能列產生的流程模組按鈕，也在jsp中移除產生該按鈕的method 3.SQL調整，出貨ProcessModuleAccessRight,ProcessModuleContainer,ProcessModuleDefinition這三個資料表已不使用，故直接移除create指令
- **Commit ID**: `e7069f06f55d04bd763142a2c6ddcf2d0a82998b`
- **作者**: 林致帆
- **日期**: 2020-05-07 08:32:28
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FavoeitiesManagerDelegate.java`
  - ❌ **刪除**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessModuleDefDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - ❌ **刪除**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProcessModuleAccessRight.java`
  - ❌ **刪除**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProcessModuleContainer.java`
  - ❌ **刪除**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProcessModuleDefinition.java`
  - ❌ **刪除**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProcessModuleDefinitionDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processModule/ProcessModuleDef.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processModule/ProcessModuleDefBean.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processModule/ProcessModuleDefLocal.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processModule/ProcessModuleDefMgr.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ProcessModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/ProcessModuleAccessRightVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/UserServiceBean.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/ProcessModuleViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.3.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.3.1_DML_Oracle_1.sql`

### 197. [Web]A00-202006190015調整，若有兩個人(一個在職,一個離職)共用一個Ldap帳號，在職人員用Ldap登入會顯示重覆的Ldap帳號
- **Commit ID**: `84290360cef1c6fe16235844f14f38369ea5fb6d`
- **作者**: 林致帆
- **日期**: 2020-08-07 11:56:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 198. [BPM APP]C01-20200804003 修正移動端產品開窗人員列表當人員有多部門時只會顯示單一部門但非主部門的問題
- **Commit ID**: `5d4988d8d47fe8aa6d8ba8a6d1f55ac471231276`
- **作者**: cherryliao
- **日期**: 2020-08-07 10:40:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java`

### 199. [Web]A00-20200803001 修正: 日期元件缺少越南文多語系造成顯示異常
- **Commit ID**: `86e6252a142530d10e88d9d7dae61cd0795574d2`
- **作者**: yanann_chen
- **日期**: 2020-08-07 09:52:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/calendar/lang/calendar-VN.js`

### 200. [BPM APP]C01-20200730002修正IMG在追蹤流程取得中間層顯示資訊時發生錯誤問題
- **Commit ID**: `3177f1a6d83e0e8ff0d6381da4024bac0bff1aba`
- **作者**: cherryliao
- **日期**: 2020-08-06 14:25:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 201. [Web]A00-20200729002 修正IOS進入掛雙表單的流程中，點切換表單按紐後彈出的Dialog無法操作和關閉
- **Commit ID**: `1d6aceaeee5473c8fe38fdeaf6de27f007f8da40`
- **作者**: 王鵬程
- **日期**: 2020-08-05 18:48:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 202. [Web]C01-20200720003 修正: 自動簽核關卡簡易流程圖順序顯示異常
- **Commit ID**: `c44beeb420833bc5d0b17a8e5ca47c9b3cad8bb2`
- **作者**: yanann_chen
- **日期**: 2020-08-05 11:17:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 203. [Web]C01-20200701002 修正: 迴圈行流程無法於追蹤流程中執行取回重辦
- **Commit ID**: `9606e0b3b6b97724773d53600f8a4cfe15774ddb`
- **作者**: yanann_chen
- **日期**: 2020-08-05 10:44:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 204. [Web]A00-20200727003 修正: 取回畫面排版異常
- **Commit ID**: `922882dd94e00adf2d5ab37893e3175320bf904e`
- **作者**: yanann_chen
- **日期**: 2020-08-05 10:14:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java`

### 205. [Web]C01-20200728001 修正: 待辦事項搜尋條件無法清空流程名稱
- **Commit ID**: `ff3684bdead0bd31b734bf73cf0152bb29c2ea10`
- **作者**: yanann_chen
- **日期**: 2020-08-05 10:05:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 206. [Web]C01-20200729002調整使用者監控流程的流程清單頁"執行中的活動"欄位顯示內容，與系統管理員的監控流程的流程清單頁一致
- **Commit ID**: `a344dc88eec8c81e3df7a276554fb4cad489bcf1`
- **作者**: 林致帆
- **日期**: 2020-08-05 09:52:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 207. [Web]A00-20200720001 修正在IE下從待辦事項中進入ESS的流程，點擊更多按鈕展開後的選單會被表單遮住
- **Commit ID**: `c0e0d71b96da550c7c433d756971f76936261e43`
- **作者**: 王鵬程
- **日期**: 2020-08-04 17:37:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`

### 208. [BPM APP]C01-20200728003修正IMG中間層因附件有設定查看權限導致無法顯示問題[補]
- **Commit ID**: `119b226bac86c435b1b732df42b29a57b7f5fd25`
- **作者**: pinchi_lin
- **日期**: 2020-08-04 14:53:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 209. [BPM APP]C01-20200728003修正IMG中間層因附件有設定查看權限導致無法顯示問題
- **Commit ID**: `ae956370ac20bc9aadce0ed14b7c8696638477ce`
- **作者**: pinchi_lin
- **日期**: 2020-08-04 14:50:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 210. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `bb0f8f2e7a7ddf611ddeee3eed2ed0fd6c1e75ee`
- **作者**: 林致帆
- **日期**: 2020-08-04 10:46:08
- **變更檔案數量**: 0

### 211. [Web]C01-20200727008修正若表單定義的xml中複合元件沒有dialogType資料，導致版更後開啟有關聯的流程畫面出現異常
- **Commit ID**: `cfbc6726eae7d6e8824a1a6aec30b716e0ff2a8b`
- **作者**: 林致帆
- **日期**: 2020-08-04 10:45:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 212. [Web]C01-20200729003 修正: Chrome遮罩功能異常問題
- **Commit ID**: `5410b0463fd53df980e3401f66294e64e43f8dc8`
- **作者**: yanann_chen
- **日期**: 2020-07-31 14:19:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ModalDialog.js`

### 213. [BPM APP]C01-20200724004修正行動端在閒置超過一定時間後操作畫面上功能時顯示的錯誤訊息文字說明
- **Commit ID**: `56e86125e3fbb8a667319f3494a270bef501eba9`
- **作者**: cherryliao
- **日期**: 2020-07-30 17:38:27
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 214. [流程引擎]A00-20200605001 修正問題: XPDL流程中核決層級未依設定啟動自動簽核[補]
- **Commit ID**: `3317ae8183c4dcf906dbafa174fbc8b26c7ee51a`
- **作者**: yanann_chen
- **日期**: 2020-07-30 17:33:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 215. [流程引擎]A00-20200605001 修正問題: XPDL流程中核決層級未依設定啟動自動簽核
- **Commit ID**: `e0f9da0ebbbc3b718dad0497fb39efa8356a9138`
- **作者**: yanann_chen
- **日期**: 2020-07-30 17:25:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 216. [Web]A00-20200714001 修正查詢樣版的搜尋條件為開始包含or結束包含，查詢結果為相反
- **Commit ID**: `cc1c80b0825d3e62293c0dc233c832e3c025aae7`
- **作者**: 林致帆
- **日期**: 2020-07-30 17:24:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 217. [流程引擎]Q00-20200730002 修正問題: 被代理工作的取回重辦URL，workItemOID為null
- **Commit ID**: `e784ec409c265cedef9900c4b4b939007c3ac286`
- **作者**: yanann_chen
- **日期**: 2020-07-30 15:56:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 218. [Web]A00-20200729001 修正從BPM的外部Url連結"查詢流程實例內的單一表單資料",若表單有附件，點擊後無法下載
- **Commit ID**: `203381e194310c083e9e21743a36667034201637`
- **作者**: 林致帆
- **日期**: 2020-07-30 15:50:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`

### 219. [Web]A00-20200622004修正表單欄位有設定顯示小數點後幾位或是顯示千分位，在流程設計師的關卡中欄位驗證是onblur或是both的設定下，造成欄位顯示異常
- **Commit ID**: `66b2bbb41785b02dce50814033907d43eeff573f`
- **作者**: 林致帆
- **日期**: 2020-07-30 15:02:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

