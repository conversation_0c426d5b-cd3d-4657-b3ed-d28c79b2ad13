{"company_id": "43102800", "company_name": "運時通", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\43102800_運時通\\01客戶基本資料", "files": [{"filename": "[運時通]連線資訊.txt", "raw_content": "[運時通]5641\r\n\r\n連線如下\r\n\r\n微軟VPN ：************** \r\n帳密： tpcon/DSC@89111688\r\n\r\n先遠端連到.17，再透過VM ware連到EFGP\r\n\r\nEFGP 正式機&DB\r\n************\t\r\n帳 administrator\r\n密 s%22992228\r\nhttp://************:8086/NaNaWeb\r\nEFGP 密碼 ST_flowGP\r\n資料庫: NaNa\r\n\r\nVM CONSOLE \r\n192.168.1.17\t\r\n帳 administrator\r\n密 s%22992228\r\n\r\n測試機IP : ************\r\nOS帳密administrator/s%22992228\r\nEFGP：administrator/1234\r\n測試機登入網址：http://************:8086/NaNaWeb\r\n測試機DB\r\n************:1433\r\ndatabase=EFGPTEST\r\n user-name: sa\r\n password: sty123456#\r\n\r\n正式區\r\nhttp://************:6384/ws/r/aws_efsrv?wsdl\r\n\r\n測試區 \r\nhttp://************:6384/ws/r/aws_efsrv_toptest?wsdl\r\n\r\n\r\n---------------------------------------------------------------------------\r\n資深經理  \r\n黃振原 Stanley \r\n台灣 Mobile：+886-986-886-003\r\n公司電話：+886-2-22992222 #1003\r\n248 新北市新北產業園區五權路73號\r\n\r\n中國 Mobile：+86-1360-968-0003\r\n公司電話：+86-769-8335-8888 Ext.8558\r\n广东省 东莞市 大岭山镇 百花洞工业区 厚大路10号\r\n\r\ne-Mail ：<EMAIL>\r\n\r\n#1233 林霜如", "structured_data": {"微軟vpn": "**************", "帳密": "tpcon/DSC@89111688", "efgp": "administrator/1234", "測試機登入網址": "http://************:8086/NaNaWeb", "台灣 mobile": "+886-986-886-003", "公司電話": "+86-769-8335-8888 Ext.8558", "中國 mobile": "+86-1360-968-0003", "e-mail": "<PERSON><PERSON>@stylutionintl.com", "http": "//************:6384/ws/r/aws_efsrv_toptest?wsdl", "database": "EFGPTEST", "測試機ip": "************", "測試機登入網址：http": "//************:8086/NaNaWeb", "************": "1433", "user-name": "sa", "password": "sty123456#", "host": "**************"}, "source_path": "C1.客戶維護相關\\43102800_運時通\\01客戶基本資料\\[運時通]連線資訊.txt", "file_size": 1171, "encoding_used": "UTF-8-SIG", "processed_at": "2025-08-26T10:46:26.074626"}], "total_files": 1, "processed_at": "2025-08-26T10:46:26.074634"}